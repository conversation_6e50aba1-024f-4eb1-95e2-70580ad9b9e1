import { UploadService } from './upload.service';
export declare class UploadController {
    private uploadService;
    constructor(uploadService: UploadService);
    uploadSingle(file: Express.Multer.File, title?: string, description?: string): Promise<import("../entities").Media>;
    uploadMultiple(files: Express.Multer.File[], title?: string, description?: string): Promise<import("../entities").Media[]>;
    findAll(): Promise<import("../entities").Media[]>;
    findOne(id: string): Promise<import("../entities").Media>;
    delete(id: string): Promise<{
        message: string;
    }>;
}
