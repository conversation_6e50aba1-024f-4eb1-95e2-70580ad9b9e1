{"version": 3, "sources": ["../../src/entity-schema/EntitySchemaRelationIdOptions.ts"], "names": [], "mappings": "", "file": "EntitySchemaRelationIdOptions.js", "sourcesContent": ["import { SelectQueryBuilder } from \"../query-builder/SelectQueryBuilder\"\n\nexport interface EntitySchemaRelationIdOptions {\n    /**\n     * Name of relation.\n     */\n    relationName: string\n\n    /**\n     * Alias of the joined (destination) table.\n     */\n    alias?: string\n\n    /**\n     * Extra condition applied to \"ON\" section of join.\n     */\n    queryBuilderFactory?: (\n        qb: SelectQueryBuilder<any>,\n    ) => SelectQueryBuilder<any>\n}\n"], "sourceRoot": ".."}