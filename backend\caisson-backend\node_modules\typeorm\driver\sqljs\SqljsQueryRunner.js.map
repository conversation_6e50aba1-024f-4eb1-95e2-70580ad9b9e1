{"version": 3, "sources": ["../../src/driver/sqljs/SqljsQueryRunner.ts"], "names": [], "mappings": ";;;AAAA,mEAA+D;AAC/D,iGAA6F;AAC7F,gEAA4D;AAC5D,8DAA0D;AAC1D,0EAAsE;AACtE,4FAAwF;AAGxF;;GAEG;AACH,MAAa,gBAAiB,SAAQ,qDAAyB;IAW3D,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YAAY,MAAmB;QAC3B,KAAK,EAAE,CAAA;QAfX;;WAEG;QACK,YAAO,GAAG,KAAK,CAAA;QAanB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;QACpB,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,UAAU,CAAA;QACnC,IAAI,CAAC,WAAW,GAAG,IAAI,yBAAW,CAAC,IAAI,CAAC,CAAA;IAC5C,CAAC;IAED,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;OAEG;IACH,KAAK,CAAC,eAAe;QACjB,MAAM,IAAI,CAAC,KAAK,CAAC,2BAA2B,CAAC,CAAA;IACjD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc;QAChB,MAAM,IAAI,CAAC,KAAK,CAAC,0BAA0B,CAAC,CAAA;IAChD,CAAC;IAEO,KAAK,CAAC,KAAK;QACf,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAA;YAC5B,IAAI,CAAC,OAAO,GAAG,KAAK,CAAA;QACxB,CAAC;IACL,CAAC;IAED,KAAK,CAAC,OAAO;QACT,MAAM,IAAI,CAAC,KAAK,EAAE,CAAA;QAClB,OAAO,KAAK,CAAC,OAAO,EAAE,CAAA;IAC1B,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,iBAAiB;QACnB,MAAM,KAAK,CAAC,iBAAiB,EAAE,CAAA;QAC/B,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC5B,MAAM,IAAI,CAAC,KAAK,EAAE,CAAA;QACtB,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,KAAK,CACP,KAAa,EACb,aAAoB,EAAE,EACtB,mBAAmB,GAAG,KAAK;QAE3B,IAAI,IAAI,CAAC,UAAU;YAAE,MAAM,IAAI,iEAA+B,EAAE,CAAA;QAEhE,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;QAE7C,MAAM,kBAAkB,GAAG,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAA;QAEzD,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,UAAU,EAAE,IAAI,CAAC,CAAA;QAC/D,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,aAAa,EAAE,KAAK,EAAE,UAAU,CAAC,CAAA;QAElE,MAAM,iBAAiB,GAAG,IAAI,qCAAiB,EAAE,CAAA;QACjD,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QACjC,IAAI,SAAc,CAAA;QAElB,IAAI,CAAC;YACD,SAAS,GAAG,kBAAkB,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;YAC7C,IAAI,UAAU,EAAE,CAAC;gBACb,UAAU,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAC9B,OAAO,CAAC,KAAK,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CACtC,CAAA;gBAED,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;YAC9B,CAAC;YAED,oDAAoD;YACpD,MAAM,qBAAqB,GACvB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,qBAAqB,CAAA;YAC7C,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;YAC/B,MAAM,kBAAkB,GAAG,YAAY,GAAG,cAAc,CAAA;YAExD,IACI,qBAAqB;gBACrB,kBAAkB,GAAG,qBAAqB;gBAE1C,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,YAAY,CACtC,kBAAkB,EAClB,KAAK,EACL,UAAU,EACV,IAAI,CACP,CAAA;YAEL,MAAM,OAAO,GAAU,EAAE,CAAA;YAEzB,OAAO,SAAS,CAAC,IAAI,EAAE,EAAE,CAAC;gBACtB,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,CAAA;YACzC,CAAC;YAED,IAAI,CAAC,WAAW,CAAC,wBAAwB,CACrC,iBAAiB,EACjB,KAAK,EACL,UAAU,EACV,IAAI,EACJ,kBAAkB,EAClB,OAAO,EACP,SAAS,CACZ,CAAA;YAED,MAAM,MAAM,GAAG,IAAI,yBAAW,EAAE,CAAA;YAEhC,MAAM,CAAC,QAAQ,GAAG,kBAAkB,CAAC,eAAe,EAAE,CAAA;YACtD,MAAM,CAAC,OAAO,GAAG,OAAO,CAAA;YACxB,MAAM,CAAC,GAAG,GAAG,OAAO,CAAA;YAEpB,SAAS,CAAC,IAAI,EAAE,CAAA;YAEhB,IAAI,OAAO,KAAK,QAAQ,EAAE,CAAC;gBACvB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAA;YACvB,CAAC;YAED,IAAI,mBAAmB,EAAE,CAAC;gBACtB,OAAO,MAAM,CAAA;YACjB,CAAC;iBAAM,CAAC;gBACJ,OAAO,MAAM,CAAC,GAAG,CAAA;YACrB,CAAC;QACL,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACX,IAAI,SAAS,EAAE,CAAC;gBACZ,SAAS,CAAC,IAAI,EAAE,CAAA;YACpB,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,aAAa,CACvC,GAAG,EACH,KAAK,EACL,UAAU,EACV,IAAI,CACP,CAAA;YACD,IAAI,CAAC,WAAW,CAAC,wBAAwB,CACrC,iBAAiB,EACjB,KAAK,EACL,UAAU,EACV,KAAK,EACL,SAAS,EACT,SAAS,EACT,GAAG,CACN,CAAA;YAED,MAAM,IAAI,mCAAgB,CAAC,KAAK,EAAE,UAAU,EAAE,GAAG,CAAC,CAAA;QACtD,CAAC;gBAAS,CAAC;YACP,MAAM,iBAAiB,CAAC,IAAI,EAAE,CAAA;QAClC,CAAC;IACL,CAAC;CACJ;AA1KD,4CA0KC", "file": "SqljsQueryRunner.js", "sourcesContent": ["import { QueryFailedError } from \"../../error/QueryFailedError\"\nimport { QueryRunnerAlreadyReleasedError } from \"../../error/QueryRunnerAlreadyReleasedError\"\nimport { QueryResult } from \"../../query-runner/QueryResult\"\nimport { Broadcaster } from \"../../subscriber/Broadcaster\"\nimport { BroadcasterResult } from \"../../subscriber/BroadcasterResult\"\nimport { AbstractSqliteQueryRunner } from \"../sqlite-abstract/AbstractSqliteQueryRunner\"\nimport { SqljsDriver } from \"./SqljsDriver\"\n\n/**\n * Runs queries on a single sqlite database connection.\n */\nexport class SqljsQueryRunner extends AbstractSqliteQueryRunner {\n    /**\n     * Flag to determine if a modification has happened since the last time this query runner has requested a save.\n     */\n    private isDirty = false\n\n    /**\n     * Database driver used by connection.\n     */\n    driver: SqljsDriver\n\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(driver: SqljsDriver) {\n        super()\n        this.driver = driver\n        this.connection = driver.connection\n        this.broadcaster = new Broadcaster(this)\n    }\n\n    // -------------------------------------------------------------------------\n    // Public methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Called before migrations are run.\n     */\n    async beforeMigration(): Promise<void> {\n        await this.query(`PRAGMA foreign_keys = OFF`)\n    }\n\n    /**\n     * Called after migrations are run.\n     */\n    async afterMigration(): Promise<void> {\n        await this.query(`PRAGMA foreign_keys = ON`)\n    }\n\n    private async flush() {\n        if (this.isDirty) {\n            await this.driver.autoSave()\n            this.isDirty = false\n        }\n    }\n\n    async release(): Promise<void> {\n        await this.flush()\n        return super.release()\n    }\n\n    /**\n     * Commits transaction.\n     * Error will be thrown if transaction was not started.\n     */\n    async commitTransaction(): Promise<void> {\n        await super.commitTransaction()\n        if (!this.isTransactionActive) {\n            await this.flush()\n        }\n    }\n\n    /**\n     * Executes a given SQL query.\n     */\n    async query(\n        query: string,\n        parameters: any[] = [],\n        useStructuredResult = false,\n    ): Promise<any> {\n        if (this.isReleased) throw new QueryRunnerAlreadyReleasedError()\n\n        const command = query.trim().split(\" \", 1)[0]\n\n        const databaseConnection = this.driver.databaseConnection\n\n        this.driver.connection.logger.logQuery(query, parameters, this)\n        await this.broadcaster.broadcast(\"BeforeQuery\", query, parameters)\n\n        const broadcasterResult = new BroadcasterResult()\n        const queryStartTime = Date.now()\n        let statement: any\n\n        try {\n            statement = databaseConnection.prepare(query)\n            if (parameters) {\n                parameters = parameters.map((p) =>\n                    typeof p !== \"undefined\" ? p : null,\n                )\n\n                statement.bind(parameters)\n            }\n\n            // log slow queries if maxQueryExecution time is set\n            const maxQueryExecutionTime =\n                this.driver.options.maxQueryExecutionTime\n            const queryEndTime = Date.now()\n            const queryExecutionTime = queryEndTime - queryStartTime\n\n            if (\n                maxQueryExecutionTime &&\n                queryExecutionTime > maxQueryExecutionTime\n            )\n                this.driver.connection.logger.logQuerySlow(\n                    queryExecutionTime,\n                    query,\n                    parameters,\n                    this,\n                )\n\n            const records: any[] = []\n\n            while (statement.step()) {\n                records.push(statement.getAsObject())\n            }\n\n            this.broadcaster.broadcastAfterQueryEvent(\n                broadcasterResult,\n                query,\n                parameters,\n                true,\n                queryExecutionTime,\n                records,\n                undefined,\n            )\n\n            const result = new QueryResult()\n\n            result.affected = databaseConnection.getRowsModified()\n            result.records = records\n            result.raw = records\n\n            statement.free()\n\n            if (command !== \"SELECT\") {\n                this.isDirty = true\n            }\n\n            if (useStructuredResult) {\n                return result\n            } else {\n                return result.raw\n            }\n        } catch (err) {\n            if (statement) {\n                statement.free()\n            }\n\n            this.driver.connection.logger.logQueryError(\n                err,\n                query,\n                parameters,\n                this,\n            )\n            this.broadcaster.broadcastAfterQueryEvent(\n                broadcasterResult,\n                query,\n                parameters,\n                false,\n                undefined,\n                undefined,\n                err,\n            )\n\n            throw new QueryFailedError(query, parameters, err)\n        } finally {\n            await broadcasterResult.wait()\n        }\n    }\n}\n"], "sourceRoot": "../.."}