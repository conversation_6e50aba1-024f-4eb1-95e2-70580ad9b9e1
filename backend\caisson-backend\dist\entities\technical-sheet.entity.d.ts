import { Document, Types } from 'mongoose';
export type TechnicalSheetDocument = TechnicalSheet & Document;
export declare class TechnicalSheet {
    title: string;
    description: string;
    thumbnail?: string;
    fileUrl: string;
    fileSize: string;
    product: Types.ObjectId;
    createdAt?: Date;
    updatedAt?: Date;
}
export declare const TechnicalSheetSchema: import("mongoose").Schema<TechnicalSheet, import("mongoose").Model<TechnicalSheet, any, any, any, Document<unknown, any, TechnicalSheet, any> & TechnicalSheet & {
    _id: Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, TechnicalSheet, Document<unknown, {}, import("mongoose").FlatRecord<TechnicalSheet>, {}> & import("mongoose").FlatRecord<TechnicalSheet> & {
    _id: Types.ObjectId;
} & {
    __v: number;
}>;
