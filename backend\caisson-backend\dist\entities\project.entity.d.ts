import { Document } from 'mongoose';
export declare enum ProjectCategory {
    RESIDENTIAL = "RESIDENTIAL",
    COMMERCIAL = "COMMERCIAL",
    INDUSTRIAL = "INDUSTRIAL"
}
export type ProjectDocument = Project & Document;
export declare class Project {
    name: string;
    slug: string;
    description: string;
    fullDescription?: string;
    images: string[];
    category: ProjectCategory;
    location: string;
    year: string;
    client: string;
    products: string[];
    createdAt?: Date;
    updatedAt?: Date;
}
export declare const ProjectSchema: import("mongoose").Schema<Project, import("mongoose").Model<Project, any, any, any, Document<unknown, any, Project, any> & Project & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, Project, Document<unknown, {}, import("mongoose").FlatRecord<Project>, {}> & import("mongoose").FlatRecord<Project> & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}>;
