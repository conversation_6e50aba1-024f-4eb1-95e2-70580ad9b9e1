{"version": 3, "sources": ["../../src/driver/react-native/ReactNativeQueryRunner.ts"], "names": [], "mappings": ";;;AACA,mEAA+D;AAC/D,iGAA6F;AAC7F,gEAA4D;AAC5D,8DAA0D;AAC1D,0EAAsE;AACtE,4FAAwF;AAGxF;;GAEG;AACH,MAAa,sBAAuB,SAAQ,qDAAyB;IAOjE,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YAAY,MAAyB;QACjC,KAAK,EAAE,CAAA;QACP,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;QACpB,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,UAAU,CAAA;QACnC,IAAI,CAAC,WAAW,GAAG,IAAI,yBAAW,CAAC,IAAI,CAAC,CAAA;IAC5C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe;QACjB,MAAM,IAAI,CAAC,KAAK,CAAC,2BAA2B,CAAC,CAAA;IACjD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc;QAChB,MAAM,IAAI,CAAC,KAAK,CAAC,0BAA0B,CAAC,CAAA;IAChD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,KAAK,CACP,KAAa,EACb,UAAkB,EAClB,mBAAmB,GAAG,KAAK;QAE3B,IAAI,IAAI,CAAC,UAAU;YAAE,MAAM,IAAI,iEAA+B,EAAE,CAAA;QAEhE,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE,CAAA;QAE/C,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,UAAU,EAAE,IAAI,CAAC,CAAA;QAC/D,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,aAAa,EAAE,KAAK,EAAE,UAAU,CAAC,CAAA;QAElE,MAAM,iBAAiB,GAAG,IAAI,qCAAiB,EAAE,CAAA;QAEjD,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QAEjC,OAAO,IAAI,OAAO,CAAC,KAAK,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE;YAClC,IAAI,CAAC;gBACD,kBAAkB,CAAC,UAAU,CACzB,KAAK,EACL,UAAU,EACV,KAAK,EAAE,GAAQ,EAAE,EAAE;oBACf,oDAAoD;oBACpD,MAAM,qBAAqB,GACvB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,qBAAqB,CAAA;oBAC7C,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;oBAC/B,MAAM,kBAAkB,GAAG,YAAY,GAAG,cAAc,CAAA;oBACxD,IAAI,CAAC,WAAW,CAAC,wBAAwB,CACrC,iBAAiB,EACjB,KAAK,EACL,UAAU,EACV,IAAI,EACJ,kBAAkB,EAClB,GAAG,EACH,SAAS,CACZ,CAAA;oBAED,IACI,qBAAqB;wBACrB,kBAAkB,GAAG,qBAAqB;wBAE1C,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,YAAY,CACtC,kBAAkB,EAClB,KAAK,EACL,UAAU,EACV,IAAI,CACP,CAAA;oBAEL,IAAI,iBAAiB,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC;wBACrC,MAAM,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAA;oBAEjD,MAAM,MAAM,GAAG,IAAI,yBAAW,EAAE,CAAA;oBAEhC,IAAI,GAAG,EAAE,cAAc,CAAC,cAAc,CAAC,EAAE,CAAC;wBACtC,MAAM,CAAC,QAAQ,GAAG,GAAG,CAAC,YAAY,CAAA;oBACtC,CAAC;oBAED,IAAI,GAAG,EAAE,cAAc,CAAC,MAAM,CAAC,EAAE,CAAC;wBAC9B,MAAM,OAAO,GAAG,EAAE,CAAA;wBAClB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;4BACvC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAA;wBAClC,CAAC;wBAED,MAAM,CAAC,GAAG,GAAG,OAAO,CAAA;wBACpB,MAAM,CAAC,OAAO,GAAG,OAAO,CAAA;oBAC5B,CAAC;oBAED,4DAA4D;oBAC5D,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,aAAa,EAAE,CAAC;wBACxC,MAAM,CAAC,GAAG,GAAG,GAAG,CAAC,QAAQ,CAAA;oBAC7B,CAAC;oBAED,IAAI,mBAAmB,EAAE,CAAC;wBACtB,EAAE,CAAC,MAAM,CAAC,CAAA;oBACd,CAAC;yBAAM,CAAC;wBACJ,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;oBAClB,CAAC;gBACL,CAAC,EACD,KAAK,EAAE,GAAQ,EAAE,EAAE;oBACf,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,aAAa,CACvC,GAAG,EACH,KAAK,EACL,UAAU,EACV,IAAI,CACP,CAAA;oBACD,IAAI,CAAC,WAAW,CAAC,wBAAwB,CACrC,iBAAiB,EACjB,KAAK,EACL,UAAU,EACV,KAAK,EACL,SAAS,EACT,SAAS,EACT,GAAG,CACN,CAAA;oBAED,IAAI,CAAC,IAAI,mCAAgB,CAAC,KAAK,EAAE,UAAU,EAAE,GAAG,CAAC,CAAC,CAAA;gBACtD,CAAC,CACJ,CAAA;YACL,CAAC;YAAC,OAAO,GAAG,EAAE,CAAC;gBACX,IAAI,CAAC,GAAG,CAAC,CAAA;YACb,CAAC;oBAAS,CAAC;gBACP,MAAM,iBAAiB,CAAC,IAAI,EAAE,CAAA;YAClC,CAAC;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IAED,4EAA4E;IAC5E,oBAAoB;IACpB,4EAA4E;IAE5E;;OAEG;IACO,WAAW,CACjB,aAA4B,EAC5B,aAAqB,CAAC;QAEtB,OAAO,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,IAAI,GAAG,GAAG,GAAG,IAAI,CAAC,CAAA;IAC5E,CAAC;CACJ;AA1JD,wDA0JC", "file": "ReactNativeQueryRunner.js", "sourcesContent": ["import { ObjectLiteral } from \"../../common/ObjectLiteral\"\nimport { QueryFailedError } from \"../../error/QueryFailedError\"\nimport { QueryRunnerAlreadyReleasedError } from \"../../error/QueryRunnerAlreadyReleasedError\"\nimport { QueryResult } from \"../../query-runner/QueryResult\"\nimport { Broadcaster } from \"../../subscriber/Broadcaster\"\nimport { BroadcasterResult } from \"../../subscriber/BroadcasterResult\"\nimport { AbstractSqliteQueryRunner } from \"../sqlite-abstract/AbstractSqliteQueryRunner\"\nimport { ReactNativeDriver } from \"./ReactNativeDriver\"\n\n/**\n * Runs queries on a single sqlite database connection.\n */\nexport class ReactNativeQueryRunner extends AbstractSqliteQueryRunner {\n    /**\n     * Database driver used by connection.\n     */\n    // @ts-ignore temporary, we need to fix the issue with the AbstractSqliteDriver and circular errors\n    driver: ReactNativeDriver\n\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(driver: ReactNativeDriver) {\n        super()\n        this.driver = driver\n        this.connection = driver.connection\n        this.broadcaster = new Broadcaster(this)\n    }\n\n    /**\n     * Called before migrations are run.\n     */\n    async beforeMigration(): Promise<void> {\n        await this.query(`PRAGMA foreign_keys = OFF`)\n    }\n\n    /**\n     * Called after migrations are run.\n     */\n    async afterMigration(): Promise<void> {\n        await this.query(`PRAGMA foreign_keys = ON`)\n    }\n\n    /**\n     * Executes a given SQL query.\n     */\n    async query(\n        query: string,\n        parameters?: any[],\n        useStructuredResult = false,\n    ): Promise<any> {\n        if (this.isReleased) throw new QueryRunnerAlreadyReleasedError()\n\n        const databaseConnection = await this.connect()\n\n        this.driver.connection.logger.logQuery(query, parameters, this)\n        await this.broadcaster.broadcast(\"BeforeQuery\", query, parameters)\n\n        const broadcasterResult = new BroadcasterResult()\n\n        const queryStartTime = Date.now()\n\n        return new Promise(async (ok, fail) => {\n            try {\n                databaseConnection.executeSql(\n                    query,\n                    parameters,\n                    async (raw: any) => {\n                        // log slow queries if maxQueryExecution time is set\n                        const maxQueryExecutionTime =\n                            this.driver.options.maxQueryExecutionTime\n                        const queryEndTime = Date.now()\n                        const queryExecutionTime = queryEndTime - queryStartTime\n                        this.broadcaster.broadcastAfterQueryEvent(\n                            broadcasterResult,\n                            query,\n                            parameters,\n                            true,\n                            queryExecutionTime,\n                            raw,\n                            undefined,\n                        )\n\n                        if (\n                            maxQueryExecutionTime &&\n                            queryExecutionTime > maxQueryExecutionTime\n                        )\n                            this.driver.connection.logger.logQuerySlow(\n                                queryExecutionTime,\n                                query,\n                                parameters,\n                                this,\n                            )\n\n                        if (broadcasterResult.promises.length > 0)\n                            await Promise.all(broadcasterResult.promises)\n\n                        const result = new QueryResult()\n\n                        if (raw?.hasOwnProperty(\"rowsAffected\")) {\n                            result.affected = raw.rowsAffected\n                        }\n\n                        if (raw?.hasOwnProperty(\"rows\")) {\n                            const records = []\n                            for (let i = 0; i < raw.rows.length; i++) {\n                                records.push(raw.rows.item(i))\n                            }\n\n                            result.raw = records\n                            result.records = records\n                        }\n\n                        // return id of inserted row, if query was insert statement.\n                        if (query.substr(0, 11) === \"INSERT INTO\") {\n                            result.raw = raw.insertId\n                        }\n\n                        if (useStructuredResult) {\n                            ok(result)\n                        } else {\n                            ok(result.raw)\n                        }\n                    },\n                    async (err: any) => {\n                        this.driver.connection.logger.logQueryError(\n                            err,\n                            query,\n                            parameters,\n                            this,\n                        )\n                        this.broadcaster.broadcastAfterQueryEvent(\n                            broadcasterResult,\n                            query,\n                            parameters,\n                            false,\n                            undefined,\n                            undefined,\n                            err,\n                        )\n\n                        fail(new QueryFailedError(query, parameters, err))\n                    },\n                )\n            } catch (err) {\n                fail(err)\n            } finally {\n                await broadcasterResult.wait()\n            }\n        })\n    }\n\n    // -------------------------------------------------------------------------\n    // Protected Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Parametrizes given object of values. Used to create column=value queries.\n     */\n    protected parametrize(\n        objectLiteral: ObjectLiteral,\n        startIndex: number = 0,\n    ): string[] {\n        return Object.keys(objectLiteral).map((key, index) => `\"${key}\"` + \"=?\")\n    }\n}\n"], "sourceRoot": "../.."}