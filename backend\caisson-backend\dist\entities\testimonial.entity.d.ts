import { Document } from 'mongoose';
export type TestimonialDocument = Testimonial & Document;
export declare class Testimonial {
    author: string;
    avatar?: string;
    role?: string;
    company?: string;
    rating: number;
    text: string;
    createdAt?: Date;
    updatedAt?: Date;
}
export declare const TestimonialSchema: import("mongoose").Schema<Testimonial, import("mongoose").Model<Testimonial, any, any, any, Document<unknown, any, Testimonial, any> & Testimonial & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, Testimonial, Document<unknown, {}, import("mongoose").FlatRecord<Testimonial>, {}> & import("mongoose").FlatRecord<Testimonial> & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}>;
