import { Category } from './category.entity';
import { Tag } from './tag.entity';
import { Review } from './review.entity';
import { TechnicalSheet } from './technical-sheet.entity';
export declare enum ProductStatus {
    DRAFT = "draft",
    PUBLISHED = "published",
    ARCHIVED = "archived"
}
export declare class Product {
    id: string;
    name: string;
    slug: string;
    description: string;
    longDescription: string;
    images: string[];
    inStock: boolean;
    features: string[];
    specifications: Record<string, string>;
    category: Category;
    tags: Tag[];
    reviews: Review[];
    technicalSheets: TechnicalSheet[];
    avgRating: number;
    reviewCount: number;
    status: ProductStatus;
    createdAt: Date;
    updatedAt: Date;
}
