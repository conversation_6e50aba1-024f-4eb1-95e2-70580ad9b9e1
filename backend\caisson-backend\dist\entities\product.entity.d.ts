import { Document, Types } from 'mongoose';
export declare enum ProductStatus {
    DRAFT = "draft",
    PUBLISHED = "published",
    ARCHIVED = "archived"
}
export type ProductDocument = Product & Document;
export declare class Product {
    name: string;
    slug: string;
    description: string;
    longDescription?: string;
    images: string[];
    inStock: boolean;
    features: string[];
    specifications?: Record<string, string>;
    category: Types.ObjectId;
    tags: Types.ObjectId[];
    avgRating: number;
    reviewCount: number;
    status: ProductStatus;
    createdAt?: Date;
    updatedAt?: Date;
}
export declare const ProductSchema: import("mongoose").Schema<Product, import("mongoose").Model<Product, any, any, any, Document<unknown, any, Product, any> & Product & {
    _id: Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, Product, Document<unknown, {}, import("mongoose").FlatRecord<Product>, {}> & import("mongoose").FlatRecord<Product> & {
    _id: Types.ObjectId;
} & {
    __v: number;
}>;
