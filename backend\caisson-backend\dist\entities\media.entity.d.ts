import { Document } from 'mongoose';
export declare enum MediaType {
    IMAGE = "image",
    VIDEO = "video",
    DOCUMENT = "document"
}
export type MediaDocument = Media & Document;
export declare class Media {
    title: string;
    description?: string;
    filename: string;
    originalName: string;
    mimeType: string;
    size: number;
    url: string;
    type: MediaType;
    thumbnail?: string;
    createdAt?: Date;
    updatedAt?: Date;
}
export declare const MediaSchema: import("mongoose").Schema<Media, import("mongoose").Model<Media, any, any, any, Document<unknown, any, Media, any> & Media & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, Media, Document<unknown, {}, import("mongoose").FlatRecord<Media>, {}> & import("mongoose").FlatRecord<Media> & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}>;
