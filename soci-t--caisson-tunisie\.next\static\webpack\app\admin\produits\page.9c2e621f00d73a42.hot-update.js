"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/produits/page",{

/***/ "(app-pages-browser)/./app/admin/produits/page.tsx":
/*!*************************************!*\
  !*** ./app/admin/produits/page.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProductsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Edit_MoreHorizontal_Plus_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,MoreHorizontal,Plus,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_MoreHorizontal_Plus_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,MoreHorizontal,Plus,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/ellipsis.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_MoreHorizontal_Plus_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,MoreHorizontal,Plus,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_MoreHorizontal_Plus_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,MoreHorizontal,Plus,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./components/ui/table.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n// Sample product data\nconst initialProducts = [\n    {\n        id: 1,\n        name: \"Coffret Tunnel Volet Roulant\",\n        category: \"coffrets\",\n        status: \"active\"\n    },\n    {\n        id: 4,\n        name: \"Panneau Isolant 2cm\",\n        category: \"panneaux\",\n        status: \"active\"\n    },\n    {\n        id: 5,\n        name: \"Panneau Isolant 3cm\",\n        category: \"panneaux\",\n        status: \"active\"\n    },\n    {\n        id: 6,\n        name: \"Panneau Isolant Sur Mesure\",\n        category: \"panneaux\",\n        status: \"inactive\"\n    },\n    {\n        id: 7,\n        name: \"Fish Box Standard\",\n        category: \"fishbox\",\n        status: \"active\"\n    },\n    {\n        id: 8,\n        name: \"Fish Box Grande Capacité\",\n        category: \"fishbox\",\n        status: \"active\"\n    },\n    {\n        id: 9,\n        name: \"Caisson d'Emballage Personnalisé\",\n        category: \"fishbox\",\n        status: \"inactive\"\n    }\n];\nfunction ProductsPage() {\n    _s();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast)();\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialProducts);\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [categoryFilter, setCategoryFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProductsPage.useEffect\": ()=>{\n            const fetchData = {\n                \"ProductsPage.useEffect.fetchData\": async ()=>{\n                    try {\n                        setLoading(true);\n                        const [productsData, categoriesData] = await Promise.all([\n                            _lib_api__WEBPACK_IMPORTED_MODULE_10__.productApi.getAll(),\n                            _lib_api__WEBPACK_IMPORTED_MODULE_10__.categoryApi.getAll()\n                        ]);\n                        // Transform API data to match the expected format\n                        const transformedProducts = productsData.map({\n                            \"ProductsPage.useEffect.fetchData.transformedProducts\": (product)=>{\n                                var _product_category;\n                                return {\n                                    id: product._id,\n                                    name: product.name,\n                                    category: ((_product_category = product.category) === null || _product_category === void 0 ? void 0 : _product_category.slug) || 'unknown',\n                                    status: product.status === 'published' ? 'active' : 'inactive'\n                                };\n                            }\n                        }[\"ProductsPage.useEffect.fetchData.transformedProducts\"]);\n                        setProducts(transformedProducts.length > 0 ? transformedProducts : initialProducts);\n                        setCategories(categoriesData);\n                    } catch (err) {\n                        console.error('Error fetching data:', err);\n                        toast({\n                            title: \"Erreur\",\n                            description: \"Impossible de charger les données. Utilisation des données de démonstration.\",\n                            variant: \"destructive\"\n                        });\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"ProductsPage.useEffect.fetchData\"];\n            fetchData();\n        }\n    }[\"ProductsPage.useEffect\"], []);\n    const handleDelete = (id)=>{\n        setProducts(products.filter((product)=>product.id !== id));\n        toast({\n            title: \"Produit supprimé\",\n            description: \"Le produit a été supprimé avec succès.\"\n        });\n    };\n    const handleStatusChange = (id, newStatus)=>{\n        setProducts(products.map((product)=>product.id === id ? {\n                ...product,\n                status: newStatus\n            } : product));\n        toast({\n            title: \"Statut mis à jour\",\n            description: \"Le produit est maintenant \".concat(newStatus === \"active\" ? \"actif\" : \"inactif\", \".\")\n        });\n    };\n    // Filter products based on search term, category, and status\n    const filteredProducts = products.filter((product)=>{\n        const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase());\n        const matchesCategory = categoryFilter === \"all\" || product.category === categoryFilter;\n        const matchesStatus = statusFilter === \"all\" || product.status === statusFilter;\n        return matchesSearch && matchesCategory && matchesStatus;\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold\",\n                        children: \"Gestion des produits\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        asChild: true,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/admin/produits/ajouter\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_MoreHorizontal_Plus_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"mr-2 h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 13\n                                }, this),\n                                \" Ajouter un produit\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                lineNumber: 132,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                children: \"Filtres\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                children: \"Filtrer et rechercher des produits\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid gap-4 md:grid-cols-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                        placeholder: \"Rechercher un produit...\",\n                                        value: searchTerm,\n                                        onChange: (e)=>setSearchTerm(e.target.value)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                        value: categoryFilter,\n                                        onValueChange: setCategoryFilter,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                    placeholder: \"Cat\\xe9gorie\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                    lineNumber: 158,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                        value: \"all\",\n                                                        children: \"Toutes les cat\\xe9gories\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                        lineNumber: 161,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                        value: \"coffrets\",\n                                                        children: \"Coffrets tunnel pour volets roulants\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                        lineNumber: 162,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                        value: \"panneaux\",\n                                                        children: \"Panneaux Isolants\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                        lineNumber: 163,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                        value: \"fishbox\",\n                                                        children: \"Fish Box\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                        lineNumber: 164,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                        value: statusFilter,\n                                        onValueChange: setStatusFilter,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                    placeholder: \"Statut\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                    lineNumber: 171,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                        value: \"all\",\n                                                        children: \"Tous les statuts\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                        lineNumber: 174,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                        value: \"active\",\n                                                        children: \"Actif\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                        lineNumber: 175,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                        value: \"inactive\",\n                                                        children: \"Inactif\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                        lineNumber: 176,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                lineNumber: 141,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                children: \"Liste des produits\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                children: [\n                                    filteredProducts.length,\n                                    \" produit\",\n                                    filteredProducts.length > 1 ? \"s\" : \"\",\n                                    \" trouv\\xe9\",\n                                    filteredProducts.length > 1 ? \"s\" : \"\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.Table, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableRow, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                children: \"ID\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                children: \"Nom\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                children: \"Cat\\xe9gorie\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                lineNumber: 198,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                children: \"Prix\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                children: \"Statut\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                className: \"text-right\",\n                                                children: \"Actions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableBody, {\n                                    children: [\n                                        filteredProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableRow, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                        children: product.id\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                        lineNumber: 207,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                        className: \"font-medium\",\n                                                        children: product.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                        lineNumber: 208,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                        children: [\n                                                            product.category === \"coffrets\" && \"Coffrets tunnel pour volets roulants\",\n                                                            product.category === \"panneaux\" && \"Panneaux Isolants\",\n                                                            product.category === \"fishbox\" && \"Fish Box\",\n                                                            product.category === \"Polystyrène\" && \"polystyrène\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                        lineNumber: 209,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium \".concat(product.status === \"active\" ? \"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300\" : \"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300\"),\n                                                            children: product.status === \"active\" ? \"Actif\" : \"Inactif\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                            lineNumber: 217,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                        lineNumber: 216,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                        className: \"text-right\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenu, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuTrigger, {\n                                                                    asChild: true,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        size: \"icon\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_MoreHorizontal_Plus_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                                                lineNumber: 231,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"sr-only\",\n                                                                                children: \"Menu\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                                                lineNumber: 232,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                                        lineNumber: 230,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                                    lineNumber: 229,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuContent, {\n                                                                    align: \"end\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuItem, {\n                                                                            asChild: true,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                                href: \"/admin/produits/modifier/\".concat(product.id),\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_MoreHorizontal_Plus_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                        className: \"mr-2 h-4 w-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                                                        lineNumber: 238,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    \" Modifier\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                                                lineNumber: 237,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                                            lineNumber: 236,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuItem, {\n                                                                            onClick: ()=>handleStatusChange(product.id, product.status === \"active\" ? \"inactive\" : \"active\"),\n                                                                            children: product.status === \"active\" ? \"Désactiver\" : \"Activer\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                                            lineNumber: 241,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuItem, {\n                                                                            className: \"text-red-600 focus:text-red-600\",\n                                                                            onClick: ()=>handleDelete(product.id),\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_MoreHorizontal_Plus_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                    className: \"mr-2 h-4 w-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                                                    lineNumber: 252,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                \" Supprimer\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                                            lineNumber: 248,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                                    lineNumber: 235,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                            lineNumber: 228,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                        lineNumber: 227,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, product.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 17\n                                            }, this)),\n                                        filteredProducts.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableRow, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                colSpan: 6,\n                                                className: \"text-center py-8 text-muted-foreground\",\n                                                children: \"Aucun produit trouv\\xe9\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                            lineNumber: 193,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                lineNumber: 184,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n        lineNumber: 131,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductsPage, \"YxIqmWbKgTGXkrt/sptUiaRPdO8=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast\n    ];\n});\n_c = ProductsPage;\nvar _c;\n$RefreshReg$(_c, \"ProductsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/admin/produits/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/api.ts":
/*!********************!*\
  !*** ./lib/api.ts ***!
  \********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authApi: () => (/* binding */ authApi),\n/* harmony export */   categoryApi: () => (/* binding */ categoryApi),\n/* harmony export */   productApi: () => (/* binding */ productApi),\n/* harmony export */   tagApi: () => (/* binding */ tagApi),\n/* harmony export */   uploadApi: () => (/* binding */ uploadApi)\n/* harmony export */ });\n// API service for communicating with the backend\nconst API_BASE_URL = 'http://localhost:3002';\n// Helper function to get auth token\nconst getAuthToken = ()=>{\n    if (true) {\n        return localStorage.getItem('token');\n    }\n    return null;\n};\n// Helper function to create headers\nconst createHeaders = function() {\n    let includeAuth = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n    const headers = {\n        'Content-Type': 'application/json'\n    };\n    if (includeAuth) {\n        const token = getAuthToken();\n        if (token) {\n            headers.Authorization = \"Bearer \".concat(token);\n        }\n    }\n    return headers;\n};\n// Generic API request function\nasync function apiRequest(endpoint) {\n    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    const url = \"\".concat(API_BASE_URL).concat(endpoint);\n    const response = await fetch(url, {\n        ...options,\n        headers: {\n            ...createHeaders(),\n            ...options.headers\n        }\n    });\n    if (!response.ok) {\n        throw new Error(\"API request failed: \".concat(response.statusText));\n    }\n    return response.json();\n}\n// Authenticated API request function\nasync function authenticatedApiRequest(endpoint) {\n    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    const url = \"\".concat(API_BASE_URL).concat(endpoint);\n    const response = await fetch(url, {\n        ...options,\n        headers: {\n            ...createHeaders(true),\n            ...options.headers\n        }\n    });\n    if (!response.ok) {\n        throw new Error(\"API request failed: \".concat(response.statusText));\n    }\n    return response.json();\n}\n// Product API\nconst productApi = {\n    // Get all products\n    getAll: ()=>apiRequest('/products'),\n    // Get product by ID\n    getById: (id)=>apiRequest(\"/products/\".concat(id)),\n    // Get product by slug\n    getBySlug: (slug)=>apiRequest(\"/products/slug/\".concat(slug)),\n    // Create product (authenticated)\n    create: (data)=>authenticatedApiRequest('/products', {\n            method: 'POST',\n            body: JSON.stringify(data)\n        }),\n    // Update product (authenticated)\n    update: (id, data)=>authenticatedApiRequest(\"/products/\".concat(id), {\n            method: 'PATCH',\n            body: JSON.stringify(data)\n        }),\n    // Delete product (authenticated)\n    delete: (id)=>authenticatedApiRequest(\"/products/\".concat(id), {\n            method: 'DELETE'\n        })\n};\n// Category API\nconst categoryApi = {\n    // Get all categories\n    getAll: ()=>apiRequest('/categories'),\n    // Get category by ID\n    getById: (id)=>apiRequest(\"/categories/\".concat(id)),\n    // Get category by slug\n    getBySlug: (slug)=>apiRequest(\"/categories/slug/\".concat(slug)),\n    // Create category (authenticated)\n    create: (data)=>authenticatedApiRequest('/categories', {\n            method: 'POST',\n            body: JSON.stringify(data)\n        }),\n    // Update category (authenticated)\n    update: (id, data)=>authenticatedApiRequest(\"/categories/\".concat(id), {\n            method: 'PATCH',\n            body: JSON.stringify(data)\n        }),\n    // Delete category (authenticated)\n    delete: (id)=>authenticatedApiRequest(\"/categories/\".concat(id), {\n            method: 'DELETE'\n        })\n};\n// Tag API\nconst tagApi = {\n    // Get all tags\n    getAll: ()=>apiRequest('/tags'),\n    // Get tag by ID\n    getById: (id)=>apiRequest(\"/tags/\".concat(id)),\n    // Create tag (authenticated)\n    create: (data)=>authenticatedApiRequest('/tags', {\n            method: 'POST',\n            body: JSON.stringify(data)\n        }),\n    // Update tag (authenticated)\n    update: (id, data)=>authenticatedApiRequest(\"/tags/\".concat(id), {\n            method: 'PATCH',\n            body: JSON.stringify(data)\n        }),\n    // Delete tag (authenticated)\n    delete: (id)=>authenticatedApiRequest(\"/tags/\".concat(id), {\n            method: 'DELETE'\n        })\n};\n// Auth API\nconst authApi = {\n    // Login\n    login: (email, password)=>apiRequest('/auth/login', {\n            method: 'POST',\n            body: JSON.stringify({\n                email,\n                password\n            })\n        }),\n    // Get profile (authenticated)\n    getProfile: ()=>authenticatedApiRequest('/auth/profile'),\n    // Logout (authenticated)\n    logout: ()=>authenticatedApiRequest('/auth/logout', {\n            method: 'POST'\n        })\n};\n// Upload API\nconst uploadApi = {\n    // Upload single file (authenticated)\n    uploadSingle: (file, title, description)=>{\n        const formData = new FormData();\n        formData.append('file', file);\n        if (title) formData.append('title', title);\n        if (description) formData.append('description', description);\n        const token = getAuthToken();\n        return fetch(\"\".concat(API_BASE_URL, \"/upload/single\"), {\n            method: 'POST',\n            headers: token ? {\n                Authorization: \"Bearer \".concat(token)\n            } : {},\n            body: formData\n        }).then((res)=>res.json());\n    },\n    // Upload multiple files (authenticated)\n    uploadMultiple: (files, title, description)=>{\n        const formData = new FormData();\n        files.forEach((file)=>formData.append('files', file));\n        if (title) formData.append('title', title);\n        if (description) formData.append('description', description);\n        const token = getAuthToken();\n        return fetch(\"\".concat(API_BASE_URL, \"/upload/multiple\"), {\n            method: 'POST',\n            headers: token ? {\n                Authorization: \"Bearer \".concat(token)\n            } : {},\n            body: formData\n        }).then((res)=>res.json());\n    },\n    // Get all uploaded files\n    getAll: ()=>apiRequest('/upload'),\n    // Get file by ID\n    getById: (id)=>apiRequest(\"/upload/\".concat(id)),\n    // Delete file (authenticated)\n    delete: (id)=>authenticatedApiRequest(\"/upload/\".concat(id), {\n            method: 'DELETE'\n        })\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/api.ts\n"));

/***/ })

});