import { Model } from 'mongoose';
import { Review, ReviewDocument } from '../entities/review.entity';
import { ProductDocument } from '../entities/product.entity';
import { CreateReviewDto } from './dto/create-review.dto';
import { UpdateReviewDto } from './dto/update-review.dto';
import { ProductsService } from '../products/products.service';
export declare class ReviewsService {
    private reviewModel;
    private productModel;
    private productsService;
    constructor(reviewModel: Model<ReviewDocument>, productModel: Model<ProductDocument>, productsService: ProductsService);
    create(createReviewDto: CreateReviewDto): Promise<Review>;
    findAll(): Promise<Review[]>;
    findByProduct(productId: string): Promise<Review[]>;
    findOne(id: string): Promise<Review>;
    update(id: string, updateReviewDto: UpdateReviewDto): Promise<Review>;
    remove(id: string): Promise<void>;
    getProductRatingStats(productId: string): Promise<{
        totalReviews: number;
        averageRating: any;
        ratingDistribution: {
            rating: any;
            count: any;
        }[];
    }>;
}
