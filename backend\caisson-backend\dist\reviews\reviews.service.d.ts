import { Repository } from 'typeorm';
import { Review } from '../entities/review.entity';
import { Product } from '../entities/product.entity';
import { CreateReviewDto } from './dto/create-review.dto';
import { UpdateReviewDto } from './dto/update-review.dto';
import { ProductsService } from '../products/products.service';
export declare class ReviewsService {
    private reviewRepository;
    private productRepository;
    private productsService;
    constructor(reviewRepository: Repository<Review>, productRepository: Repository<Product>, productsService: ProductsService);
    create(createReviewDto: CreateReviewDto): Promise<Review>;
    findAll(): Promise<Review[]>;
    findByProduct(productId: string): Promise<Review[]>;
    findOne(id: string): Promise<Review>;
    update(id: string, updateReviewDto: UpdateReviewDto): Promise<Review>;
    remove(id: string): Promise<void>;
    getProductRatingStats(productId: string): Promise<{
        totalReviews: any;
        averageRating: number;
        ratingDistribution: any;
    }>;
}
