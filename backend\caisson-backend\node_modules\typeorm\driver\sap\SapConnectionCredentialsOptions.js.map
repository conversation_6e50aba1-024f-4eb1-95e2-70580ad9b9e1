{"version": 3, "sources": ["../../src/driver/sap/SapConnectionCredentialsOptions.ts"], "names": [], "mappings": "", "file": "SapConnectionCredentialsOptions.js", "sourcesContent": ["/**\n * SAP Hana specific connection credential options.\n */\nexport interface SapConnectionCredentialsOptions {\n    /**\n     * Database host.\n     */\n    readonly host?: string\n\n    /**\n     * Database host port.\n     */\n    readonly port?: number\n\n    /**\n     * Database username.\n     */\n    readonly username?: string\n\n    /**\n     * Database password.\n     */\n    readonly password?: string\n\n    /**\n     * Database name to connect to.\n     */\n    readonly database?: string\n\n    /**\n     * Encrypt database connection\n     */\n    readonly encrypt?: boolean\n\n    /**\n     * Validate database certificate\n     */\n    readonly sslValidateCertificate?: boolean\n\n    /**\n     * Key for encrypted connection\n     */\n    readonly key?: string\n\n    /**\n     * Cert for encrypted connection\n     */\n    readonly cert?: string\n\n    /**\n     * Ca for encrypted connection\n     */\n    readonly ca?: string\n}\n"], "sourceRoot": "../.."}