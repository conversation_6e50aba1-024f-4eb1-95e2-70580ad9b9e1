import {
  IsString,
  <PERSON>NotEmpty,
  IsO<PERSON>al,
  <PERSON>Int,
  <PERSON>,
  <PERSON>,
  IsUUID,
  IsObject,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

class UserDto {
  @ApiProperty({ example: 'user-uuid' })
  @IsString()
  @IsNotEmpty()
  id: string;

  @ApiProperty({ example: 'Mohamed B.' })
  @IsString()
  @IsNotEmpty()
  name: string;
}

export class CreateReviewDto {
  @ApiProperty({ example: 5, minimum: 1, maximum: 5 })
  @IsInt()
  @Min(1)
  @Max(5)
  rating: number;

  @ApiPropertyOptional({ example: 'Excellent produit' })
  @IsString()
  @IsOptional()
  title?: string;

  @ApiPropertyOptional({
    example: 'Très satisfait de ce coffret tunnel. Installation facile et excellente isolation.',
  })
  @IsString()
  @IsOptional()
  content?: string;

  @ApiProperty({
    example: { id: 'user-uuid', name: '<PERSON>' },
  })
  @IsObject()
  @ValidateNested()
  @Type(() => UserDto)
  user: UserDto;

  @ApiProperty({ example: 'product-uuid' })
  @IsUUID()
  @IsNotEmpty()
  productId: string;
}
