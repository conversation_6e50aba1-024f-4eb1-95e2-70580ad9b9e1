import { Injectable, BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Media, MediaType } from '../entities/media.entity';
import * as fs from 'fs';
import * as path from 'path';

@Injectable()
export class UploadService {
  constructor(
    @InjectRepository(Media)
    private mediaRepository: Repository<Media>,
    private configService: ConfigService,
  ) {}

  async uploadFile(
    file: Express.Multer.File,
    title?: string,
    description?: string,
  ): Promise<Media> {
    if (!file) {
      throw new BadRequestException('No file provided');
    }

    // Determine media type based on mime type
    let mediaType: MediaType;
    if (file.mimetype.startsWith('image/')) {
      mediaType = MediaType.IMAGE;
    } else if (file.mimetype.startsWith('video/')) {
      mediaType = MediaType.VIDEO;
    } else {
      mediaType = MediaType.DOCUMENT;
    }

    // Generate file URL
    const baseUrl = `${this.configService.get('FRONTEND_URL')}:${this.configService.get('PORT')}`;
    const fileUrl = `${baseUrl}/uploads/${file.filename}`;

    // Create media record
    const media = this.mediaRepository.create({
      title: title || file.originalname,
      description,
      filename: file.filename,
      originalName: file.originalname,
      mimeType: file.mimetype,
      size: file.size,
      url: fileUrl,
      type: mediaType,
    });

    return this.mediaRepository.save(media);
  }

  async uploadMultipleFiles(
    files: Express.Multer.File[],
    title?: string,
    description?: string,
  ): Promise<Media[]> {
    const uploadPromises = files.map((file, index) =>
      this.uploadFile(file, `${title || 'File'} ${index + 1}`, description),
    );

    return Promise.all(uploadPromises);
  }

  async deleteFile(id: string): Promise<void> {
    const media = await this.mediaRepository.findOne({ where: { id } });
    
    if (!media) {
      throw new BadRequestException('File not found');
    }

    // Delete physical file
    const filePath = path.join(
      this.configService.get('UPLOAD_DEST') || './uploads',
      media.filename,
    );

    try {
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }
    } catch (error) {
      console.error('Error deleting file:', error);
    }

    // Delete database record
    await this.mediaRepository.remove(media);
  }

  async findAll(): Promise<Media[]> {
    return this.mediaRepository.find({
      order: { createdAt: 'DESC' },
    });
  }

  async findOne(id: string): Promise<Media> {
    const media = await this.mediaRepository.findOne({ where: { id } });
    
    if (!media) {
      throw new BadRequestException('File not found');
    }

    return media;
  }

  validateFile(file: Express.Multer.File): void {
    const maxSize = this.configService.get<number>('MAX_FILE_SIZE') || 10485760; // 10MB
    
    if (file.size > maxSize) {
      throw new BadRequestException('File size too large');
    }

    // Add more validation as needed
    const allowedMimeTypes = [
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
      'video/mp4',
      'video/webm',
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    ];

    if (!allowedMimeTypes.includes(file.mimetype)) {
      throw new BadRequestException('File type not allowed');
    }
  }
}
