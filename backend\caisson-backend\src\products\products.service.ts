import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Product, ProductDocument } from '../entities/product.entity';
import { Category, CategoryDocument } from '../entities/category.entity';
import { Tag, TagDocument } from '../entities/tag.entity';
import { CreateProductDto } from './dto/create-product.dto';
import { UpdateProductDto } from './dto/update-product.dto';

@Injectable()
export class ProductsService {
  constructor(
    @InjectModel(Product.name)
    private productModel: Model<ProductDocument>,
    @InjectModel(Category.name)
    private categoryModel: Model<CategoryDocument>,
    @InjectModel(Tag.name)
    private tagModel: Model<TagDocument>,
  ) {}

  async create(createProductDto: CreateProductDto): Promise<Product> {
    const { categoryId, tagIds, ...productData } = createProductDto;

    // Check if slug already exists
    const existingProduct = await this.productModel.findOne({
      slug: createProductDto.slug,
    }).exec();

    if (existingProduct) {
      throw new BadRequestException('Product with this slug already exists');
    }

    // Find category
    const category = await this.categoryModel.findById(categoryId).exec();

    if (!category) {
      throw new NotFoundException('Category not found');
    }

    // Find tags if provided
    let tagObjectIds: Types.ObjectId[] = [];
    if (tagIds && tagIds.length > 0) {
      const tags = await this.tagModel.find({
        _id: { $in: tagIds.map(id => new Types.ObjectId(id)) }
      }).exec();

      if (tags.length !== tagIds.length) {
        throw new NotFoundException('One or more tags not found');
      }

      tagObjectIds = tags.map(tag => (tag as any)._id);
    }

    // Create product
    const product = new this.productModel({
      ...productData,
      category: new Types.ObjectId(categoryId),
      tags: tagObjectIds,
    });

    return product.save();
  }

  async findAll(): Promise<Product[]> {
    return this.productModel.find()
      .populate('category')
      .populate('tags')
      .sort({ createdAt: -1 })
      .exec();
  }

  async findOne(id: string): Promise<Product> {
    const product = await this.productModel.findById(id)
      .populate('category')
      .populate('tags')
      .exec();

    if (!product) {
      throw new NotFoundException('Product not found');
    }

    return product;
  }

  async findBySlug(slug: string): Promise<Product> {
    const product = await this.productModel.findOne({ slug })
      .populate('category')
      .populate('tags')
      .exec();

    if (!product) {
      throw new NotFoundException('Product not found');
    }

    return product;
  }

  async update(id: string, updateProductDto: UpdateProductDto): Promise<Product> {
    const { categoryId, tagIds, ...productData } = updateProductDto;

    // Check if slug already exists (excluding current product)
    if (updateProductDto.slug) {
      const existingProduct = await this.productModel.findOne({
        slug: updateProductDto.slug,
      }).exec();

      if (existingProduct && (existingProduct as any)._id.toString() !== id) {
        throw new BadRequestException('Product with this slug already exists');
      }
    }

    const updateData: any = { ...productData };

    // Update category if provided
    if (categoryId) {
      const category = await this.categoryModel.findById(categoryId).exec();

      if (!category) {
        throw new NotFoundException('Category not found');
      }

      updateData.category = new Types.ObjectId(categoryId);
    }

    // Update tags if provided
    if (tagIds !== undefined) {
      if (tagIds.length > 0) {
        const tags = await this.tagModel.find({
          _id: { $in: tagIds.map(id => new Types.ObjectId(id)) }
        }).exec();

        if (tags.length !== tagIds.length) {
          throw new NotFoundException('One or more tags not found');
        }

        updateData.tags = tags.map(tag => tag._id);
      } else {
        updateData.tags = [];
      }
    }

    const product = await this.productModel.findByIdAndUpdate(
      id,
      updateData,
      { new: true }
    ).populate('category').populate('tags').exec();

    if (!product) {
      throw new NotFoundException('Product not found');
    }

    return product;
  }

  async remove(id: string): Promise<void> {
    const product = await this.productModel.findById(id).exec();

    if (!product) {
      throw new NotFoundException('Product not found');
    }

    await this.productModel.findByIdAndDelete(id).exec();
  }

  async updateRating(productId: string): Promise<void> {
    // For MongoDB, we'll need to use aggregation to calculate ratings
    // This is a simplified version - in a real app, you'd have a Review collection
    // and aggregate from there
    const product = await this.productModel.findById(productId).exec();

    if (!product) {
      throw new NotFoundException('Product not found');
    }

    // For now, we'll just update the product with default values
    // This method will be properly implemented when we add the Review service
    await this.productModel.findByIdAndUpdate(productId, {
      avgRating: 0,
      reviewCount: 0,
    }).exec();
  }
}
