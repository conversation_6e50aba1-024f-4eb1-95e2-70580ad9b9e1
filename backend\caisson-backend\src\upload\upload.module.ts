import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MulterModule } from '@nestjs/platform-express';
import { UploadService } from './upload.service';
import { UploadController } from './upload.controller';
import { Media } from '../entities/media.entity';
import { multerConfig } from './multer.config';

@Module({
  imports: [
    TypeOrmModule.forFeature([Media]),
    MulterModule.register(multerConfig),
  ],
  controllers: [UploadController],
  providers: [UploadService],
  exports: [UploadService],
})
export class UploadModule {}
