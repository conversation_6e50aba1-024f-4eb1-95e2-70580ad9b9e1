{"version": 3, "sources": ["../../src/schema-builder/options/TableIndexOptions.ts"], "names": [], "mappings": "", "file": "TableIndexOptions.js", "sourcesContent": ["/**\n * Database's table index options.\n */\nexport interface TableIndexOptions {\n    // -------------------------------------------------------------------------\n    // Public Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Constraint name.\n     */\n    name?: string\n\n    /**\n     * Columns included in this index.\n     */\n    columnNames: string[]\n\n    /**\n     * Indicates if this index is unique.\n     */\n    isUnique?: boolean\n\n    /**\n     * The SPATIAL modifier indexes the entire column and does not allow indexed columns to contain NULL values.\n     * Works only in MySQL.\n     */\n    isSpatial?: boolean\n\n    /**\n     * Builds the index using the concurrently option.\n     * This options is only supported for postgres database.\n     */\n    isConcurrent?: boolean\n\n    /**\n     * The FULLTEXT modifier indexes the entire column and does not allow prefixing.\n     * Supported only in MySQL & SAP HANA.\n     */\n    isFulltext?: boolean\n\n    /**\n     * NULL_FILTERED indexes are particularly useful for indexing sparse columns, where most rows contain a NULL value.\n     * In these cases, the NULL_FILTERED index can be considerably smaller and more efficient to maintain than\n     * a normal index that includes NULL values.\n     *\n     * Works only in Spanner.\n     */\n    isNullFiltered?: boolean\n\n    /**\n     * Fulltext parser.\n     * Works only in MySQL.\n     */\n    parser?: string\n\n    /**\n     * Index filter condition.\n     */\n    where?: string\n}\n"], "sourceRoot": "../.."}