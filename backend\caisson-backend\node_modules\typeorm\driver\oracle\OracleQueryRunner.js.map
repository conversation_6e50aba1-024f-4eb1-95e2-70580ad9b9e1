{"version": 3, "sources": ["../../src/driver/oracle/OracleQueryRunner.ts"], "names": [], "mappings": ";;;AACA,uCAA0C;AAC1C,mEAA+D;AAC/D,iGAA6F;AAC7F,uFAAmF;AAEnF,wEAAoE;AACpE,gEAA4D;AAE5D,4DAAwD;AACxD,sEAAkE;AAClE,wEAAoE;AAEpE,gFAA4E;AAC5E,sEAAkE;AAClE,wEAAoE;AACpE,yDAAqD;AACrD,8DAA0D;AAC1D,0EAAsE;AACtE,gEAA4D;AAC5D,kDAA8C;AAC9C,oCAAgC;AAGhC,kEAA8D;AAI9D;;GAEG;AACH,MAAa,iBAAkB,SAAQ,iCAAe;IAmBlD,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YAAY,MAAoB,EAAE,IAAqB;QACnD,KAAK,EAAE,CAAA;QACP,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;QACpB,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,UAAU,CAAA;QACnC,IAAI,CAAC,WAAW,GAAG,IAAI,yBAAW,CAAC,IAAI,CAAC,CAAA;QACxC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;IACpB,CAAC;IAED,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;;OAGG;IACH,OAAO;QACH,IAAI,IAAI,CAAC,kBAAkB;YACvB,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAA;QAEnD,IAAI,IAAI,CAAC,yBAAyB;YAC9B,OAAO,IAAI,CAAC,yBAAyB,CAAA;QAEzC,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;YACpD,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC,MAAM;iBACvC,qBAAqB,EAAE;iBACvB,IAAI,CAAC,CAAC,UAAU,EAAE,EAAE;gBACjB,IAAI,CAAC,kBAAkB,GAAG,UAAU,CAAA;gBACpC,OAAO,IAAI,CAAC,kBAAkB,CAAA;YAClC,CAAC,CAAC,CAAA;QACV,CAAC;aAAM,CAAC;YACJ,SAAS;YACT,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC,MAAM;iBACvC,sBAAsB,EAAE;iBACxB,IAAI,CAAC,CAAC,UAAU,EAAE,EAAE;gBACjB,IAAI,CAAC,kBAAkB,GAAG,UAAU,CAAA;gBACpC,OAAO,IAAI,CAAC,kBAAkB,CAAA;YAClC,CAAC,CAAC,CAAA;QACV,CAAC;QAED,OAAO,IAAI,CAAC,yBAAyB,CAAA;IACzC,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,OAAO;QACT,IAAI,CAAC,UAAU,GAAG,IAAI,CAAA;QAEtB,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC3B,OAAM;QACV,CAAC;QAED,MAAM,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAA;IACzC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB,CAClB,iBAAiC,gBAAgB;QAEjD,IAAI,IAAI,CAAC,UAAU;YAAE,MAAM,IAAI,iEAA+B,EAAE,CAAA;QAEhE,yCAAyC;QACzC,IACI,cAAc,KAAK,cAAc;YACjC,cAAc,KAAK,gBAAgB,EACrC,CAAC;YACC,MAAM,IAAI,oBAAY,CAClB,gEAAgE,CACnE,CAAA;QACL,CAAC;QAED,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAA;QAC/B,IAAI,CAAC;YACD,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAA;QAC9D,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACX,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAA;YAChC,MAAM,GAAG,CAAA;QACb,CAAC;QAED,IAAI,IAAI,CAAC,gBAAgB,KAAK,CAAC,EAAE,CAAC;YAC9B,MAAM,IAAI,CAAC,KAAK,CACZ,kCAAkC,GAAG,cAAc,CACtD,CAAA;QACL,CAAC;aAAM,CAAC;YACJ,MAAM,IAAI,CAAC,KAAK,CAAC,qBAAqB,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAA;QAClE,CAAC;QACD,IAAI,CAAC,gBAAgB,IAAI,CAAC,CAAA;QAE1B,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,uBAAuB,CAAC,CAAA;IAC7D,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,iBAAiB;QACnB,IAAI,CAAC,IAAI,CAAC,mBAAmB;YAAE,MAAM,IAAI,uDAA0B,EAAE,CAAA;QAErE,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,yBAAyB,CAAC,CAAA;QAE3D,IAAI,IAAI,CAAC,gBAAgB,KAAK,CAAC,EAAE,CAAC;YAC9B,MAAM,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;YAC1B,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAA;QACpC,CAAC;QACD,IAAI,CAAC,gBAAgB,IAAI,CAAC,CAAA;QAE1B,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAA;IAC9D,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,mBAAmB;QACrB,IAAI,CAAC,IAAI,CAAC,mBAAmB;YAAE,MAAM,IAAI,uDAA0B,EAAE,CAAA;QAErE,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,2BAA2B,CAAC,CAAA;QAE7D,IAAI,IAAI,CAAC,gBAAgB,GAAG,CAAC,EAAE,CAAC;YAC5B,MAAM,IAAI,CAAC,KAAK,CACZ,iCAAiC,IAAI,CAAC,gBAAgB,GAAG,CAAC,EAAE,CAC/D,CAAA;QACL,CAAC;aAAM,CAAC;YACJ,MAAM,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAA;YAC5B,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAA;QACpC,CAAC;QACD,IAAI,CAAC,gBAAgB,IAAI,CAAC,CAAA;QAE1B,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAA;IAChE,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,KAAK,CACP,KAAa,EACb,UAAkB,EAClB,mBAAmB,GAAG,KAAK;QAE3B,IAAI,IAAI,CAAC,UAAU;YAAE,MAAM,IAAI,iEAA+B,EAAE,CAAA;QAEhE,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE,CAAA;QAE/C,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,UAAU,EAAE,IAAI,CAAC,CAAA;QAC/D,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,aAAa,EAAE,KAAK,EAAE,UAAU,CAAC,CAAA;QAElE,MAAM,iBAAiB,GAAG,IAAI,qCAAiB,EAAE,CAAA;QACjD,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QAEjC,IAAI,CAAC;YACD,MAAM,gBAAgB,GAAG;gBACrB,UAAU,EAAE,CAAC,IAAI,CAAC,mBAAmB;gBACrC,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,iBAAiB;aAClD,CAAA;YAED,MAAM,GAAG,GAAG,MAAM,kBAAkB,CAAC,OAAO,CACxC,KAAK,EACL,UAAU,IAAI,EAAE,EAChB,gBAAgB,CACnB,CAAA;YAED,oDAAoD;YACpD,MAAM,qBAAqB,GACvB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,qBAAqB,CAAA;YAC7C,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;YAC/B,MAAM,kBAAkB,GAAG,YAAY,GAAG,cAAc,CAAA;YAExD,IAAI,CAAC,WAAW,CAAC,wBAAwB,CACrC,iBAAiB,EACjB,KAAK,EACL,UAAU,EACV,IAAI,EACJ,kBAAkB,EAClB,GAAG,EACH,SAAS,CACZ,CAAA;YAED,IACI,qBAAqB;gBACrB,kBAAkB,GAAG,qBAAqB;gBAE1C,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,YAAY,CACtC,kBAAkB,EAClB,KAAK,EACL,UAAU,EACV,IAAI,CACP,CAAA;YAEL,MAAM,MAAM,GAAG,IAAI,yBAAW,EAAE,CAAA;YAEhC,MAAM,CAAC,GAAG;gBACN,GAAG,CAAC,IAAI;oBACR,GAAG,CAAC,QAAQ;oBACZ,GAAG,CAAC,YAAY;oBAChB,GAAG,CAAC,eAAe,CAAA;YAEvB,IAAI,GAAG,EAAE,cAAc,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;gBACzD,MAAM,CAAC,OAAO,GAAG,GAAG,CAAC,IAAI,CAAA;YAC7B,CAAC;YAED,IACI,GAAG,EAAE,cAAc,CAAC,UAAU,CAAC;gBAC/B,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,EAC7B,CAAC;gBACC,MAAM,CAAC,OAAO,GAAG,GAAG,CAAC,QAAQ,CAAA;YACjC,CAAC;YAED,IACI,GAAG,EAAE,cAAc,CAAC,iBAAiB,CAAC;gBACtC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,EACpC,CAAC;gBACC,MAAM,CAAC,OAAO,GAAG,GAAG,CAAC,eAAe,CAAA;YACxC,CAAC;YAED,IAAI,GAAG,EAAE,cAAc,CAAC,cAAc,CAAC,EAAE,CAAC;gBACtC,MAAM,CAAC,QAAQ,GAAG,GAAG,CAAC,YAAY,CAAA;YACtC,CAAC;YAED,IAAI,mBAAmB,EAAE,CAAC;gBACtB,OAAO,MAAM,CAAA;YACjB,CAAC;iBAAM,CAAC;gBACJ,OAAO,MAAM,CAAC,GAAG,CAAA;YACrB,CAAC;QACL,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACX,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,aAAa,CACvC,GAAG,EACH,KAAK,EACL,UAAU,EACV,IAAI,CACP,CAAA;YACD,IAAI,CAAC,WAAW,CAAC,wBAAwB,CACrC,iBAAiB,EACjB,KAAK,EACL,UAAU,EACV,KAAK,EACL,SAAS,EACT,SAAS,EACT,GAAG,CACN,CAAA;YAED,MAAM,IAAI,mCAAgB,CAAC,KAAK,EAAE,UAAU,EAAE,GAAG,CAAC,CAAA;QACtD,CAAC;gBAAS,CAAC;YACP,MAAM,iBAAiB,CAAC,IAAI,EAAE,CAAA;QAClC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,MAAM,CACR,KAAa,EACb,UAAkB,EAClB,KAAgB,EAChB,OAAkB;QAElB,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,MAAM,IAAI,iEAA+B,EAAE,CAAA;QAC/C,CAAC;QAED,MAAM,gBAAgB,GAAG;YACrB,UAAU,EAAE,CAAC,IAAI,CAAC,mBAAmB;YACrC,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,iBAAiB;SAClD,CAAA;QAED,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE,CAAA;QAE/C,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,UAAU,EAAE,IAAI,CAAC,CAAA;QAE/D,IAAI,CAAC;YACD,MAAM,MAAM,GAAG,kBAAkB,CAAC,WAAW,CACzC,KAAK,EACL,UAAU,EACV,gBAAgB,CACnB,CAAA;YACD,IAAI,KAAK,EAAE,CAAC;gBACR,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;YAC3B,CAAC;YAED,IAAI,OAAO,EAAE,CAAC;gBACV,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,CAAA;YAC/B,CAAC;YAED,OAAO,MAAM,CAAA;QACjB,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACX,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,aAAa,CACvC,GAAG,EACH,KAAK,EACL,UAAU,EACV,IAAI,CACP,CAAA;YACD,MAAM,IAAI,mCAAgB,CAAC,KAAK,EAAE,UAAU,EAAE,GAAG,CAAC,CAAA;QACtD,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY;QACd,OAAO,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAA;IAC9B,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,UAAU,CAAC,QAAiB;QAC9B,OAAO,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAA;IAC9B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CAAC,QAAgB;QAC9B,IAAI,CAAC;YACD,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,KAAK,CAC1B,0CAA0C,QAAQ,GAAG,CACxD,CAAA;YAED,OAAO,KAAK,CAAC,MAAM,GAAG,CAAC,CAAA;QAC3B,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACT,OAAO,KAAK,CAAA;QAChB,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,kBAAkB;QACpB,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,KAAK,CAC1B,gEAAgE,CACnE,CAAA;QACD,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAA;IAC9B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CAAC,MAAc;QAC1B,OAAO,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;IACjC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB;QAClB,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,KAAK,CAC1B,2EAA2E,CAC9E,CAAA;QACD,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAA;IAClC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ,CAAC,WAA2B;QACtC,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC7D,MAAM,GAAG,GAAG,gEAAgE,SAAS,GAAG,CAAA;QACxF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QACpC,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAA;IACvC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CACX,WAA2B,EAC3B,UAAkB;QAElB,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC7D,MAAM,GAAG,GAAG,mEAAmE,SAAS,0BAA0B,UAAU,GAAG,CAAA;QAC/H,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QACpC,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAA;IACvC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAChB,QAAgB,EAChB,UAAoB;QAEpB,oCAAoC;QACpC,sCAAsC;QACtC,wCAAwC;QACxC,IAAI,UAAU,EAAE,CAAC;YACb,IAAI,CAAC;gBACD,MAAM,IAAI,CAAC,KAAK,CAAC,kCAAkC,QAAQ,IAAI,CAAC,CAAA;YACpE,CAAC;YAAC,OAAO,CAAC,EAAE,CAAC;gBACT,uCAAuC;gBACvC,IAAI,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,qCAAqC,CAAC,EAAE,CAAC;oBAC5D,OAAM;gBACV,CAAC;gBACD,IAAI;gBAEJ,MAAM,CAAC,CAAA;YACX,CAAC;QACL,CAAC;aAAM,CAAC;YACJ,MAAM,IAAI,CAAC,KAAK,CAAC,oBAAoB,QAAQ,GAAG,CAAC,CAAA;QACrD,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CAAC,QAAgB,EAAE,OAAiB;QAClD,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;IAC5B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CACd,UAAkB,EAClB,UAAoB;QAEpB,MAAM,IAAI,oBAAY,CAClB,2DAA2D,CAC9D,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CAAC,UAAkB,EAAE,OAAiB;QAClD,MAAM,IAAI,oBAAY,CAClB,yDAAyD,CAC5D,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CACb,KAAY,EACZ,aAAsB,KAAK,EAC3B,oBAA6B,IAAI,EACjC,gBAAyB,IAAI;QAE7B,IAAI,UAAU,EAAE,CAAC;YACb,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAA;YAC/C,IAAI,YAAY;gBAAE,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;QAC9C,CAAC;QACD,MAAM,SAAS,GAAY,EAAE,CAAA;QAC7B,MAAM,WAAW,GAAY,EAAE,CAAA;QAE/B,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,iBAAiB,CAAC,CAAC,CAAA;QAC7D,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAA;QAE1C,iFAAiF;QACjF,kIAAkI;QAClI,IAAI,iBAAiB;YACjB,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE,CACrC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC,CAC9D,CAAA;QAEL,IAAI,aAAa,EAAE,CAAC;YAChB,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;gBAC5B,sFAAsF;gBACtF,IAAI,CAAC,KAAK,CAAC,IAAI;oBACX,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,SAAS,CACjD,KAAK,EACL,KAAK,CAAC,WAAW,EACjB,KAAK,CAAC,KAAK,CACd,CAAA;gBACL,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAA;gBACjD,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAA;YAC9C,CAAC,CAAC,CAAA;QACN,CAAC;QAED,6FAA6F;QAC7F,MAAM,gBAAgB,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CACzC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,aAAa,IAAI,MAAM,CAAC,YAAY,CAC1D,CAAA;QAED,KAAK,MAAM,MAAM,IAAI,gBAAgB,EAAE,CAAC;YACpC,MAAM,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC;gBAC9C,KAAK,EAAE,KAAK,CAAC,IAAI;gBACjB,IAAI,EAAE,qCAAiB,CAAC,gBAAgB;gBACxC,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,KAAK,EAAE,MAAM,CAAC,YAAY;aAC7B,CAAC,CAAA;YAEF,MAAM,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC;gBAC9C,KAAK,EAAE,KAAK,CAAC,IAAI;gBACjB,IAAI,EAAE,qCAAiB,CAAC,gBAAgB;gBACxC,IAAI,EAAE,MAAM,CAAC,IAAI;aACpB,CAAC,CAAA;YAEF,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;YAC3B,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;QACjC,CAAC;QAED,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;IACrD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CACX,WAA2B,EAC3B,OAAiB,EACjB,kBAA2B,IAAI,EAC/B,cAAuB,IAAI;QAE3B,qGAAqG;QACrG,wDAAwD;QACxD,IAAI,OAAO,EAAE,CAAC;YACV,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAA;YACrD,IAAI,CAAC,YAAY;gBAAE,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;QAC/C,CAAC;QAED,8FAA8F;QAC9F,MAAM,iBAAiB,GAAY,eAAe,CAAA;QAClD,MAAM,KAAK,GAAG,iCAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,MAAM,SAAS,GAAY,EAAE,CAAA;QAC7B,MAAM,WAAW,GAAY,EAAE,CAAA;QAE/B,IAAI,WAAW,EAAE,CAAC;YACd,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;gBAC5B,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAA;gBACxC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAA;YACvD,CAAC,CAAC,CAAA;QACN,CAAC;QAED,iGAAiG;QACjG,kIAAkI;QAClI,IAAI,eAAe;YACf,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE,CACrC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC,CAC5D,CAAA;QAEL,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAA;QACxC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,iBAAiB,CAAC,CAAC,CAAA;QAE/D,kGAAkG;QAClG,MAAM,gBAAgB,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CACzC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,aAAa,IAAI,MAAM,CAAC,YAAY,CAC1D,CAAA;QAED,KAAK,MAAM,MAAM,IAAI,gBAAgB,EAAE,CAAC;YACpC,MAAM,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC;gBAC9C,KAAK,EAAE,KAAK,CAAC,IAAI;gBACjB,IAAI,EAAE,qCAAiB,CAAC,gBAAgB;gBACxC,IAAI,EAAE,MAAM,CAAC,IAAI;aACpB,CAAC,CAAA;YAEF,MAAM,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC;gBAC9C,KAAK,EAAE,KAAK,CAAC,IAAI;gBACjB,IAAI,EAAE,qCAAiB,CAAC,gBAAgB;gBACxC,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,KAAK,EAAE,MAAM,CAAC,YAAY;aAC7B,CAAC,CAAA;YAEF,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;YAC3B,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;QACjC,CAAC;QAED,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;IACrD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CACZ,IAAU,EACV,mBAA4B,KAAK;QAEjC,MAAM,SAAS,GAAY,EAAE,CAAA;QAC7B,MAAM,WAAW,GAAY,EAAE,CAAA;QAC/B,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAA;QACxC,IAAI,gBAAgB;YAAE,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC,CAAA;QACxE,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAA;QACxC,IAAI,gBAAgB;YAChB,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC,CAAA;QACxD,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;IACrD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ,CAAC,MAAqB;QAChC,MAAM,QAAQ,GAAG,iCAAe,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAA;QACtE,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAA;QAE/C,MAAM,SAAS,GAAY,EAAE,CAAA;QAC7B,MAAM,WAAW,GAAY,EAAE,CAAA;QAC/B,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC,CAAA;QAClD,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAA;QACtC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC,CAAA;QACpD,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAA;QAC1C,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;IACrD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CACb,cAA8B,EAC9B,YAAoB;QAEpB,MAAM,SAAS,GAAY,EAAE,CAAA;QAC7B,MAAM,WAAW,GAAY,EAAE,CAAA;QAC/B,MAAM,QAAQ,GAAG,iCAAe,CAAC,OAAO,CAAC,cAAc,CAAC;YACpD,CAAC,CAAC,cAAc;YAChB,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,CAAA;QAC/C,MAAM,QAAQ,GAAG,QAAQ,CAAC,KAAK,EAAE,CAAA;QAEjC,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,GAC/C,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAA;QAExC,QAAQ,CAAC,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,IAAI,YAAY,EAAE,CAAC,CAAC,CAAC,YAAY,CAAA;QAEnE,eAAe;QACf,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,QAAQ,CACX,eAAe,YAAY,GAAG,CAClC,CACJ,CAAA;QACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,QAAQ,CACX,eAAe,YAAY,GAAG,CAClC,CACJ,CAAA;QAED,gCAAgC;QAChC,IACI,QAAQ,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC;YAClC,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,wBAAwB,EACtD,CAAC;YACC,MAAM,WAAW,GAAG,QAAQ,CAAC,cAAc,CAAC,GAAG,CAC3C,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAC1B,CAAA;YAED,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CAC3D,QAAQ,EACR,WAAW,CACd,CAAA;YACD,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CAC3D,QAAQ,EACR,WAAW,CACd,CAAA;YAED,gBAAgB;YAChB,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,QAAQ,CACX,uBAAuB,SAAS,SAAS,SAAS,GAAG,CACzD,CACJ,CAAA;YACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,QAAQ,CACX,uBAAuB,SAAS,SAAS,SAAS,GAAG,CACzD,CACJ,CAAA;QACL,CAAC;QAED,4BAA4B;QAC5B,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;YAChC,MAAM,aAAa,GACf,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,oBAAoB,CAC/C,QAAQ,EACR,MAAM,CAAC,WAAW,CACrB,CAAA;YAEL,2DAA2D;YAC3D,IAAI,MAAM,CAAC,IAAI,KAAK,aAAa;gBAAE,OAAM;YAEzC,4BAA4B;YAC5B,MAAM,aAAa,GACf,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,oBAAoB,CAC/C,QAAQ,EACR,MAAM,CAAC,WAAW,CACrB,CAAA;YAEL,gBAAgB;YAChB,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,QAAQ,CACX,uBACG,MAAM,CAAC,IACX,SAAS,aAAa,GAAG,CAC5B,CACJ,CAAA;YACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,QAAQ,CACX,uBAAuB,aAAa,SACjC,MAAM,CAAC,IACX,GAAG,CACN,CACJ,CAAA;YAED,0BAA0B;YAC1B,MAAM,CAAC,IAAI,GAAG,aAAa,CAAA;QAC/B,CAAC,CAAC,CAAA;QAEF,2BAA2B;QAC3B,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YAC/B,MAAM,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,SAAS,CACzD,QAAQ,EACR,KAAK,CAAC,WAAW,EACjB,KAAK,CAAC,KAAK,CACd,CAAA;YAED,0DAA0D;YAC1D,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY;gBAAE,OAAM;YAEvC,4BAA4B;YAC5B,MAAM,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,SAAS,CACzD,QAAQ,EACR,KAAK,CAAC,WAAW,EACjB,KAAK,CAAC,KAAK,CACd,CAAA;YAED,gBAAgB;YAChB,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,gBAAgB,KAAK,CAAC,IAAI,gBAAgB,YAAY,GAAG,CAC5D,CACJ,CAAA;YACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,gBAAgB,YAAY,gBAAgB,KAAK,CAAC,IAAI,GAAG,CAC5D,CACJ,CAAA;YAED,0BAA0B;YAC1B,KAAK,CAAC,IAAI,GAAG,YAAY,CAAA;QAC7B,CAAC,CAAC,CAAA;QAEF,iCAAiC;QACjC,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;YACxC,MAAM,iBAAiB,GACnB,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,QAAQ,EACR,UAAU,CAAC,WAAW,EACtB,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,EAC7B,UAAU,CAAC,qBAAqB,CACnC,CAAA;YAEL,gEAAgE;YAChE,IAAI,UAAU,CAAC,IAAI,KAAK,iBAAiB;gBAAE,OAAM;YAEjD,4BAA4B;YAC5B,MAAM,iBAAiB,GACnB,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,QAAQ,EACR,UAAU,CAAC,WAAW,EACtB,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,EAC7B,UAAU,CAAC,qBAAqB,CACnC,CAAA;YAEL,gBAAgB;YAChB,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,QAAQ,CACX,uBACG,UAAU,CAAC,IACf,SAAS,iBAAiB,GAAG,CAChC,CACJ,CAAA;YACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,QAAQ,CACX,uBAAuB,iBAAiB,SACrC,UAAU,CAAC,IACf,GAAG,CACN,CACJ,CAAA;YAED,0BAA0B;YAC1B,UAAU,CAAC,IAAI,GAAG,iBAAiB,CAAA;QACvC,CAAC,CAAC,CAAA;QAEF,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;QAEjD,oDAAoD;QACpD,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAA;QAC7B,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAA;IAC/C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CACX,WAA2B,EAC3B,MAAmB;QAEnB,MAAM,KAAK,GAAG,iCAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,MAAM,WAAW,GAAG,KAAK,CAAC,KAAK,EAAE,CAAA;QACjC,MAAM,SAAS,GAAY,EAAE,CAAA;QAC7B,MAAM,WAAW,GAAY,EAAE,CAAA;QAE/B,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,QAAQ,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,EAAE,CAC/C,CACJ,CAAA;QACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,iBACjC,MAAM,CAAC,IACX,GAAG,CACN,CACJ,CAAA;QAED,0CAA0C;QAC1C,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC;YACnB,MAAM,cAAc,GAAG,WAAW,CAAC,cAAc,CAAA;YACjD,wEAAwE;YACxE,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC5B,MAAM,MAAM,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC,wBAAwB;oBACrD,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,wBAAwB;oBAC5C,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,WAAW,EACX,cAAc,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAC9C,CAAA;gBAEP,MAAM,WAAW,GAAG,cAAc;qBAC7B,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,MAAM,CAAC,IAAI,GAAG,CAAC;qBACnC,IAAI,CAAC,IAAI,CAAC,CAAA;gBAEf,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,qBAAqB,MAAM,GAAG,CAClC,CACJ,CAAA;gBACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,oBAAoB,MAAM,kBAAkB,WAAW,GAAG,CAC9D,CACJ,CAAA;YACL,CAAC;YAED,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;YAC3B,MAAM,MAAM,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC,wBAAwB;gBACrD,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,wBAAwB;gBAC5C,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,WAAW,EACX,cAAc,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAC9C,CAAA;YAEP,MAAM,WAAW,GAAG,cAAc;iBAC7B,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,MAAM,CAAC,IAAI,GAAG,CAAC;iBACnC,IAAI,CAAC,IAAI,CAAC,CAAA;YAEf,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,oBAAoB,MAAM,kBAAkB,WAAW,GAAG,CAC9D,CACJ,CAAA;YACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,qBAAqB,MAAM,GAAG,CAClC,CACJ,CAAA;QACL,CAAC;QAED,sBAAsB;QACtB,MAAM,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,CACxC,CAAC,KAAK,EAAE,EAAE,CACN,KAAK,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC;YAC9B,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,IAAI,CAC3C,CAAA;QACD,IAAI,WAAW,EAAE,CAAC;YACd,WAAW,CAAC,OAAO,CAAC,MAAM,CACtB,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,EACxC,CAAC,CACJ,CAAA;YACD,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC,CAAA;YACvD,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC,CAAA;QACpD,CAAC;QAED,2BAA2B;QAC3B,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;YAClB,MAAM,gBAAgB,GAAG,IAAI,yBAAW,CAAC;gBACrC,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,oBAAoB,CACrD,KAAK,EACL,CAAC,MAAM,CAAC,IAAI,CAAC,CAChB;gBACD,WAAW,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC;aAC7B,CAAC,CAAA;YACF,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAA;YAC1C,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,oBACjC,gBAAgB,CAAC,IACrB,cAAc,MAAM,CAAC,IAAI,IAAI,CAChC,CACJ,CAAA;YACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,qBACjC,gBAAgB,CAAC,IACrB,GAAG,CACN,CACJ,CAAA;QACL,CAAC;QAED,IAAI,MAAM,CAAC,aAAa,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC;YAC9C,MAAM,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC;gBAC9C,KAAK,EAAE,KAAK,CAAC,IAAI;gBACjB,IAAI,EAAE,qCAAiB,CAAC,gBAAgB;gBACxC,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,KAAK,EAAE,MAAM,CAAC,YAAY;aAC7B,CAAC,CAAA;YAEF,MAAM,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC;gBAC9C,KAAK,EAAE,KAAK,CAAC,IAAI;gBACjB,IAAI,EAAE,qCAAiB,CAAC,gBAAgB;gBACxC,IAAI,EAAE,MAAM,CAAC,IAAI;aACpB,CAAC,CAAA;YAEF,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;YAC3B,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;QACjC,CAAC;QAED,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;QAEjD,WAAW,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;QAC7B,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAA;IAC/C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CACZ,WAA2B,EAC3B,OAAsB;QAEtB,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC3B,MAAM,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,MAAM,CAAC,CAAA;QAC7C,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CACd,WAA2B,EAC3B,oBAA0C,EAC1C,oBAA0C;QAE1C,MAAM,KAAK,GAAG,iCAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,MAAM,SAAS,GAAG,iCAAe,CAAC,aAAa,CAAC,oBAAoB,CAAC;YACjE,CAAC,CAAC,oBAAoB;YACtB,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,oBAAoB,CAAC,CAAA;QAChE,IAAI,CAAC,SAAS;YACV,MAAM,IAAI,oBAAY,CAClB,WAAW,oBAAoB,0BAA0B,IAAI,CAAC,UAAU,CACpE,KAAK,CACR,SAAS,CACb,CAAA;QAEL,IAAI,SAAS,GAA4B,SAAS,CAAA;QAClD,IAAI,iCAAe,CAAC,aAAa,CAAC,oBAAoB,CAAC,EAAE,CAAC;YACtD,SAAS,GAAG,oBAAoB,CAAA;QACpC,CAAC;aAAM,CAAC;YACJ,SAAS,GAAG,SAAS,CAAC,KAAK,EAAE,CAAA;YAC7B,SAAS,CAAC,IAAI,GAAG,oBAAoB,CAAA;QACzC,CAAC;QAED,MAAM,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,SAAS,EAAE,SAAS,CAAC,CAAA;IACxD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CACd,WAA2B,EAC3B,oBAA0C,EAC1C,SAAsB;QAEtB,MAAM,KAAK,GAAG,iCAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,IAAI,WAAW,GAAG,KAAK,CAAC,KAAK,EAAE,CAAA;QAC/B,MAAM,SAAS,GAAY,EAAE,CAAA;QAC7B,MAAM,WAAW,GAAY,EAAE,CAAA;QAE/B,MAAM,SAAS,GAAG,iCAAe,CAAC,aAAa,CAAC,oBAAoB,CAAC;YACjE,CAAC,CAAC,oBAAoB;YACtB,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CACd,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,oBAAoB,CACnD,CAAA;QACP,IAAI,CAAC,SAAS;YACV,MAAM,IAAI,oBAAY,CAClB,WAAW,oBAAoB,0BAA0B,IAAI,CAAC,UAAU,CACpE,KAAK,CACR,SAAS,CACb,CAAA;QAEL,IACI,CAAC,SAAS,CAAC,WAAW,KAAK,SAAS,CAAC,WAAW;YAC5C,SAAS,CAAC,kBAAkB,KAAK,MAAM,CAAC;YAC5C,SAAS,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI;YACjC,SAAS,CAAC,MAAM,KAAK,SAAS,CAAC,MAAM;YACrC,SAAS,CAAC,aAAa,KAAK,SAAS,CAAC,aAAa;YACnD,SAAS,CAAC,YAAY,KAAK,SAAS,CAAC,YAAY,EACnD,CAAC;YACC,qGAAqG;YACrG,kDAAkD;YAClD,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,SAAS,CAAC,CAAA;YACvC,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,SAAS,CAAC,CAAA;YAEtC,sBAAsB;YACtB,WAAW,GAAG,KAAK,CAAC,KAAK,EAAE,CAAA;QAC/B,CAAC;aAAM,CAAC;YACJ,IAAI,SAAS,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,EAAE,CAAC;gBACpC,gBAAgB;gBAChB,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,mBACjC,SAAS,CAAC,IACd,SAAS,SAAS,CAAC,IAAI,GAAG,CAC7B,CACJ,CAAA;gBACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,mBACjC,SAAS,CAAC,IACd,SAAS,SAAS,CAAC,IAAI,GAAG,CAC7B,CACJ,CAAA;gBAED,uCAAuC;gBACvC,IACI,SAAS,CAAC,SAAS,KAAK,IAAI;oBAC5B,CAAC,SAAS,CAAC,wBAAwB,EACrC,CAAC;oBACC,MAAM,cAAc,GAAG,WAAW,CAAC,cAAc,CAAA;oBAEjD,oCAAoC;oBACpC,MAAM,WAAW,GAAG,cAAc,CAAC,GAAG,CAClC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAC1B,CAAA;oBACD,MAAM,SAAS,GACX,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,WAAW,EACX,WAAW,CACd,CAAA;oBAEL,+CAA+C;oBAC/C,WAAW,CAAC,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAA;oBAC1D,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA;oBAEhC,oCAAoC;oBACpC,MAAM,SAAS,GACX,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,WAAW,EACX,WAAW,CACd,CAAA;oBAEL,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,uBAAuB,SAAS,SAAS,SAAS,GAAG,CACzD,CACJ,CAAA;oBACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,uBAAuB,SAAS,SAAS,SAAS,GAAG,CACzD,CACJ,CAAA;gBACL,CAAC;gBAED,4BAA4B;gBAC5B,WAAW,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;oBACxD,MAAM,aAAa,GACf,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,oBAAoB,CAC/C,WAAW,EACX,MAAM,CAAC,WAAW,CACrB,CAAA;oBAEL,2DAA2D;oBAC3D,IAAI,MAAM,CAAC,IAAI,KAAK,aAAa;wBAAE,OAAM;oBAEzC,4BAA4B;oBAC5B,MAAM,CAAC,WAAW,CAAC,MAAM,CACrB,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,EAC1C,CAAC,CACJ,CAAA;oBACD,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA;oBACvC,MAAM,aAAa,GACf,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,oBAAoB,CAC/C,WAAW,EACX,MAAM,CAAC,WAAW,CACrB,CAAA;oBAEL,gBAAgB;oBAChB,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,uBACG,MAAM,CAAC,IACX,SAAS,aAAa,GAAG,CAC5B,CACJ,CAAA;oBACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,uBAAuB,aAAa,SACjC,MAAM,CAAC,IACX,GAAG,CACN,CACJ,CAAA;oBAED,0BAA0B;oBAC1B,MAAM,CAAC,IAAI,GAAG,aAAa,CAAA;gBAC/B,CAAC,CAAC,CAAA;gBAEF,2BAA2B;gBAC3B,WAAW,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;oBACvD,MAAM,YAAY,GACd,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,SAAS,CACpC,WAAW,EACX,KAAK,CAAC,WAAW,EACjB,KAAK,CAAC,KAAK,CACd,CAAA;oBAEL,0DAA0D;oBAC1D,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY;wBAAE,OAAM;oBAEvC,4BAA4B;oBAC5B,KAAK,CAAC,WAAW,CAAC,MAAM,CACpB,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,EACzC,CAAC,CACJ,CAAA;oBACD,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA;oBACtC,MAAM,YAAY,GACd,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,SAAS,CACpC,WAAW,EACX,KAAK,CAAC,WAAW,EACjB,KAAK,CAAC,KAAK,CACd,CAAA;oBAEL,gBAAgB;oBAChB,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,gBAAgB,KAAK,CAAC,IAAI,gBAAgB,YAAY,GAAG,CAC5D,CACJ,CAAA;oBACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,gBAAgB,YAAY,gBAAgB,KAAK,CAAC,IAAI,GAAG,CAC5D,CACJ,CAAA;oBAED,0BAA0B;oBAC1B,KAAK,CAAC,IAAI,GAAG,YAAY,CAAA;gBAC7B,CAAC,CAAC,CAAA;gBAEF,iCAAiC;gBACjC,WAAW;qBACN,qBAAqB,CAAC,SAAS,CAAC;qBAChC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;oBACpB,MAAM,cAAc,GAChB,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,WAAW,EACX,UAAU,CAAC,WAAW,EACtB,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,EAC7B,UAAU,CAAC,qBAAqB,CACnC,CAAA;oBAEL,gEAAgE;oBAChE,IAAI,UAAU,CAAC,IAAI,KAAK,cAAc;wBAAE,OAAM;oBAE9C,4BAA4B;oBAC5B,UAAU,CAAC,WAAW,CAAC,MAAM,CACzB,UAAU,CAAC,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,EAC9C,CAAC,CACJ,CAAA;oBACD,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA;oBAC3C,MAAM,iBAAiB,GACnB,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,WAAW,EACX,UAAU,CAAC,WAAW,EACtB,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,EAC7B,UAAU,CAAC,qBAAqB,CACnC,CAAA;oBAEL,gBAAgB;oBAChB,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,uBACG,UAAU,CAAC,IACf,SAAS,iBAAiB,GAAG,CAChC,CACJ,CAAA;oBACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,uBAAuB,iBAAiB,SACrC,UAAU,CAAC,IACf,GAAG,CACN,CACJ,CAAA;oBAED,0BAA0B;oBAC1B,UAAU,CAAC,IAAI,GAAG,iBAAiB,CAAA;gBACvC,CAAC,CAAC,CAAA;gBAEN,wCAAwC;gBACxC,MAAM,cAAc,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,CAC3C,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,CAC7C,CAAA;gBACD,WAAW,CAAC,OAAO,CACf,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,cAAe,CAAC,CAC/C,CAAC,IAAI,GAAG,SAAS,CAAC,IAAI,CAAA;gBACvB,SAAS,CAAC,IAAI,GAAG,SAAS,CAAC,IAAI,CAAA;YACnC,CAAC;YAED,IAAI,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC,EAAE,CAAC;gBACnD,IAAI,SAAS,GAAW,EAAE,CAAA;gBAC1B,IAAI,WAAW,GAAW,EAAE,CAAA;gBAC5B,IAAI,UAAU,GAAW,EAAE,CAAA;gBAC3B,IAAI,YAAY,GAAW,EAAE,CAAA;gBAE7B,0BAA0B;gBAC1B,IACI,SAAS,CAAC,OAAO,KAAK,IAAI;oBAC1B,SAAS,CAAC,OAAO,KAAK,SAAS,EACjC,CAAC;oBACC,SAAS,GAAG,WAAW,SAAS,CAAC,OAAO,EAAE,CAAA;oBAE1C,IACI,SAAS,CAAC,OAAO,KAAK,IAAI;wBAC1B,SAAS,CAAC,OAAO,KAAK,SAAS,EACjC,CAAC;wBACC,WAAW,GAAG,WAAW,SAAS,CAAC,OAAO,EAAE,CAAA;oBAChD,CAAC;yBAAM,CAAC;wBACJ,WAAW,GAAG,cAAc,CAAA;oBAChC,CAAC;gBACL,CAAC;qBAAM,IACH,SAAS,CAAC,OAAO,KAAK,IAAI;oBAC1B,SAAS,CAAC,OAAO,KAAK,SAAS,EACjC,CAAC;oBACC,SAAS,GAAG,cAAc,CAAA;oBAC1B,WAAW,GAAG,WAAW,SAAS,CAAC,OAAO,EAAE,CAAA;gBAChD,CAAC;gBAED,sCAAsC;gBACtC,IAAI,SAAS,CAAC,UAAU,KAAK,SAAS,CAAC,UAAU,EAAE,CAAC;oBAChD,IAAI,SAAS,CAAC,UAAU,KAAK,IAAI,EAAE,CAAC;wBAChC,UAAU,GAAG,MAAM,CAAA;wBACnB,YAAY,GAAG,UAAU,CAAA;oBAC7B,CAAC;yBAAM,CAAC;wBACJ,UAAU,GAAG,UAAU,CAAA;wBACvB,YAAY,GAAG,MAAM,CAAA;oBACzB,CAAC;gBACL,CAAC;gBAED,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,YACjC,SAAS,CAAC,IACd,KAAK,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,cAAc,CACtC,SAAS,CACZ,IAAI,SAAS,IAAI,UAAU,EAAE,CACjC,CACJ,CAAA;gBACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,YACjC,SAAS,CAAC,IACd,KAAK,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,cAAc,CACtC,SAAS,CACZ,IAAI,WAAW,IAAI,YAAY,EAAE,CACrC,CACJ,CAAA;YACL,CAAC;YAED,IAAI,SAAS,CAAC,SAAS,KAAK,SAAS,CAAC,SAAS,EAAE,CAAC;gBAC9C,MAAM,cAAc,GAAG,WAAW,CAAC,cAAc,CAAA;gBAEjD,2EAA2E;gBAC3E,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAC5B,MAAM,MAAM,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC,wBAAwB;wBACrD,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,wBAAwB;wBAC5C,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,WAAW,EACX,cAAc,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAC9C,CAAA;oBAEP,MAAM,WAAW,GAAG,cAAc;yBAC7B,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,MAAM,CAAC,IAAI,GAAG,CAAC;yBACnC,IAAI,CAAC,IAAI,CAAC,CAAA;oBAEf,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,qBAAqB,MAAM,GAAG,CAClC,CACJ,CAAA;oBACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,oBAAoB,MAAM,kBAAkB,WAAW,GAAG,CAC9D,CACJ,CAAA;gBACL,CAAC;gBAED,IAAI,SAAS,CAAC,SAAS,KAAK,IAAI,EAAE,CAAC;oBAC/B,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;oBAC9B,yBAAyB;oBACzB,MAAM,MAAM,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,CACnC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,CAC7C,CAAA;oBACD,MAAO,CAAC,SAAS,GAAG,IAAI,CAAA;oBACxB,MAAM,MAAM,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC,wBAAwB;wBACrD,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,wBAAwB;wBAC5C,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,WAAW,EACX,cAAc,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAC9C,CAAA;oBAEP,MAAM,WAAW,GAAG,cAAc;yBAC7B,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,MAAM,CAAC,IAAI,GAAG,CAAC;yBACnC,IAAI,CAAC,IAAI,CAAC,CAAA;oBAEf,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,oBAAoB,MAAM,kBAAkB,WAAW,GAAG,CAC9D,CACJ,CAAA;oBACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,qBAAqB,MAAM,GAAG,CAClC,CACJ,CAAA;gBACL,CAAC;qBAAM,CAAC;oBACJ,MAAM,aAAa,GAAG,cAAc,CAAC,IAAI,CACrC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,CACnC,CAAA;oBACD,cAAc,CAAC,MAAM,CACjB,cAAc,CAAC,OAAO,CAAC,aAAc,CAAC,EACtC,CAAC,CACJ,CAAA;oBAED,yBAAyB;oBACzB,MAAM,MAAM,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,CACnC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,CAC7C,CAAA;oBACD,MAAO,CAAC,SAAS,GAAG,KAAK,CAAA;oBAEzB,gEAAgE;oBAChE,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBAC5B,MAAM,MAAM,GAAG,cAAc,CAAC,CAAC,CAAC;6BAC3B,wBAAwB;4BACzB,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,wBAAwB;4BAC5C,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,WAAW,EACX,cAAc,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAC9C,CAAA;wBAEP,MAAM,WAAW,GAAG,cAAc;6BAC7B,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,MAAM,CAAC,IAAI,GAAG,CAAC;6BACnC,IAAI,CAAC,IAAI,CAAC,CAAA;wBAEf,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,oBAAoB,MAAM,kBAAkB,WAAW,GAAG,CAC9D,CACJ,CAAA;wBACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,qBAAqB,MAAM,GAAG,CAClC,CACJ,CAAA;oBACL,CAAC;gBACL,CAAC;YACL,CAAC;YAED,IAAI,SAAS,CAAC,QAAQ,KAAK,SAAS,CAAC,QAAQ,EAAE,CAAC;gBAC5C,IAAI,SAAS,CAAC,QAAQ,KAAK,IAAI,EAAE,CAAC;oBAC9B,MAAM,gBAAgB,GAAG,IAAI,yBAAW,CAAC;wBACrC,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,oBAAoB,CACrD,KAAK,EACL,CAAC,SAAS,CAAC,IAAI,CAAC,CACnB;wBACD,WAAW,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC;qBAChC,CAAC,CAAA;oBACF,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAA;oBAC1C,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,oBACG,gBAAgB,CAAC,IACrB,cAAc,SAAS,CAAC,IAAI,IAAI,CACnC,CACJ,CAAA;oBACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,qBAAqB,gBAAgB,CAAC,IAAI,GAAG,CACjD,CACJ,CAAA;gBACL,CAAC;qBAAM,CAAC;oBACJ,MAAM,gBAAgB,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,CAC7C,CAAC,MAAM,EAAE,EAAE;wBACP,OAAO,CACH,MAAM,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC;4BAC/B,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CACrB,CAAC,UAAU,EAAE,EAAE,CACX,UAAU,KAAK,SAAS,CAAC,IAAI,CACpC,CACJ,CAAA;oBACL,CAAC,CACJ,CAAA;oBACD,WAAW,CAAC,OAAO,CAAC,MAAM,CACtB,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,gBAAiB,CAAC,EAC9C,CAAC,CACJ,CAAA;oBACD,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,qBAAqB,gBAAiB,CAAC,IAAI,GAAG,CAClD,CACJ,CAAA;oBACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,oBACG,gBAAiB,CAAC,IACtB,cAAc,SAAS,CAAC,IAAI,IAAI,CACnC,CACJ,CAAA;gBACL,CAAC;YACL,CAAC;YAED,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;YACjD,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAA;QAC/C,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa,CACf,WAA2B,EAC3B,cAAoE;QAEpE,KAAK,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,cAAc,EAAE,CAAC;YACpD,MAAM,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,SAAS,EAAE,SAAS,CAAC,CAAA;QAC9D,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CACZ,WAA2B,EAC3B,YAAkC;QAElC,MAAM,KAAK,GAAG,iCAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,MAAM,MAAM,GAAG,iCAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YACtD,CAAC,CAAC,YAAY;YACd,CAAC,CAAC,KAAK,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAA;QAC1C,IAAI,CAAC,MAAM;YACP,MAAM,IAAI,oBAAY,CAClB,WAAW,YAAY,4BAA4B,IAAI,CAAC,UAAU,CAC9D,KAAK,CACR,EAAE,CACN,CAAA;QAEL,MAAM,WAAW,GAAG,KAAK,CAAC,KAAK,EAAE,CAAA;QACjC,MAAM,SAAS,GAAY,EAAE,CAAA;QAC7B,MAAM,WAAW,GAAY,EAAE,CAAA;QAE/B,8BAA8B;QAC9B,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC;YACnB,MAAM,MAAM,GAAG,MAAM,CAAC,wBAAwB;gBAC1C,CAAC,CAAC,MAAM,CAAC,wBAAwB;gBACjC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,WAAW,EACX,WAAW,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAC1D,CAAA;YAEP,MAAM,WAAW,GAAG,WAAW,CAAC,cAAc;iBACzC,GAAG,CAAC,CAAC,aAAa,EAAE,EAAE,CAAC,IAAI,aAAa,CAAC,IAAI,GAAG,CAAC;iBACjD,IAAI,CAAC,IAAI,CAAC,CAAA;YAEf,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,WAAW,CACd,qBAAqB,MAAM,GAAG,CAClC,CACJ,CAAA;YACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,WAAW,CACd,oBAAoB,MAAM,kBAAkB,WAAW,GAAG,CAC9D,CACJ,CAAA;YAED,yBAAyB;YACzB,MAAM,WAAW,GAAG,WAAW,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;YAC7D,WAAY,CAAC,SAAS,GAAG,KAAK,CAAA;YAE9B,mFAAmF;YACnF,IAAI,WAAW,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACxC,MAAM,MAAM,GAAG,WAAW,CAAC,cAAc,CAAC,CAAC,CAAC;qBACvC,wBAAwB;oBACzB,CAAC,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,wBAAwB;oBACxD,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,WAAW,EACX,WAAW,CAAC,cAAc,CAAC,GAAG,CAC1B,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAC1B,CACJ,CAAA;gBAEP,MAAM,WAAW,GAAG,WAAW,CAAC,cAAc;qBACzC,GAAG,CAAC,CAAC,aAAa,EAAE,EAAE,CAAC,IAAI,aAAa,CAAC,IAAI,GAAG,CAAC;qBACjD,IAAI,CAAC,IAAI,CAAC,CAAA;gBAEf,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,WAAW,CACd,oBAAoB,MAAM,kBAAkB,WAAW,GAAG,CAC9D,CACJ,CAAA;gBACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,WAAW,CACd,qBAAqB,MAAM,GAAG,CAClC,CACJ,CAAA;YACL,CAAC;QACL,CAAC;QAED,oBAAoB;QACpB,MAAM,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,CACxC,CAAC,KAAK,EAAE,EAAE,CACN,KAAK,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC;YAC9B,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,IAAI,CAC3C,CAAA;QACD,IAAI,WAAW,EAAE,CAAC;YACd,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC,CAAA;YAC9C,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC,CAAA;QAC7D,CAAC;QAED,oBAAoB;QACpB,MAAM,WAAW,GAAG,WAAW,CAAC,MAAM,CAAC,IAAI,CACvC,CAAC,KAAK,EAAE,EAAE,CACN,CAAC,CAAC,KAAK,CAAC,WAAW;YACnB,KAAK,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC;YAC9B,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,IAAI,CAC3C,CAAA;QACD,IAAI,WAAW,EAAE,CAAC;YACd,WAAW,CAAC,MAAM,CAAC,MAAM,CACrB,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,EACvC,CAAC,CACJ,CAAA;YACD,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC,CAAA;YAC/D,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC,CAAA;QACvE,CAAC;QAED,qBAAqB;QACrB,MAAM,YAAY,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,CACzC,CAAC,MAAM,EAAE,EAAE,CACP,MAAM,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC;YAC/B,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,IAAI,CAC5C,CAAA;QACD,IAAI,YAAY,EAAE,CAAC;YACf,WAAW,CAAC,OAAO,CAAC,MAAM,CACtB,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,EACzC,CAAC,CACJ,CAAA;YACD,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC,CAAA;YACjE,WAAW,CAAC,IAAI,CACZ,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE,YAAY,CAAC,CACtD,CAAA;QACL,CAAC;QAED,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,iBACjC,MAAM,CAAC,IACX,GAAG,CACN,CACJ,CAAA;QACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,QAAQ,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,EAAE,CAC/C,CACJ,CAAA;QAED,IAAI,MAAM,CAAC,aAAa,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC;YAC9C,MAAM,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC;gBAC9C,KAAK,EAAE,KAAK,CAAC,IAAI;gBACjB,IAAI,EAAE,qCAAiB,CAAC,gBAAgB;gBACxC,IAAI,EAAE,MAAM,CAAC,IAAI;aACpB,CAAC,CAAA;YACF,MAAM,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC;gBAC9C,KAAK,EAAE,KAAK,CAAC,IAAI;gBACjB,IAAI,EAAE,qCAAiB,CAAC,gBAAgB;gBACxC,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,KAAK,EAAE,MAAM,CAAC,YAAY;aAC7B,CAAC,CAAA;YAEF,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;YAC3B,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;QACjC,CAAC;QAED,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;QAEjD,WAAW,CAAC,YAAY,CAAC,MAAM,CAAC,CAAA;QAChC,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAA;IAC/C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CACb,WAA2B,EAC3B,OAAiC;QAEjC,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC3B,MAAM,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,MAAM,CAAC,CAAA;QAC9C,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB,CAClB,WAA2B,EAC3B,WAAqB,EACrB,cAAuB;QAEvB,MAAM,KAAK,GAAG,iCAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,MAAM,WAAW,GAAG,KAAK,CAAC,KAAK,EAAE,CAAA;QAEjC,MAAM,EAAE,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,WAAW,EAAE,cAAc,CAAC,CAAA;QAEvE,4GAA4G;QAC5G,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;YACnC,IAAI,WAAW,CAAC,IAAI,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,UAAU,KAAK,MAAM,CAAC,IAAI,CAAC;gBAC5D,MAAM,CAAC,SAAS,GAAG,IAAI,CAAA;QAC/B,CAAC,CAAC,CAAA;QACF,MAAM,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAA;QAEhD,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;QACnC,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAA;IAC/C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB,CACnB,WAA2B,EAC3B,OAAsB;QAEtB,MAAM,KAAK,GAAG,iCAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,MAAM,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;QACxD,MAAM,WAAW,GAAG,KAAK,CAAC,KAAK,EAAE,CAAA;QACjC,MAAM,SAAS,GAAY,EAAE,CAAA;QAC7B,MAAM,WAAW,GAAY,EAAE,CAAA;QAE/B,4DAA4D;QAC5D,MAAM,cAAc,GAAG,WAAW,CAAC,cAAc,CAAA;QACjD,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC5B,MAAM,MAAM,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC,wBAAwB;gBACrD,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,wBAAwB;gBAC5C,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,WAAW,EACX,cAAc,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAC9C,CAAA;YAEP,MAAM,iBAAiB,GAAG,cAAc;iBACnC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,MAAM,CAAC,IAAI,GAAG,CAAC;iBACnC,IAAI,CAAC,IAAI,CAAC,CAAA;YAEf,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,qBAAqB,MAAM,GAAG,CAClC,CACJ,CAAA;YACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,oBAAoB,MAAM,kBAAkB,iBAAiB,GAAG,CACpE,CACJ,CAAA;QACL,CAAC;QAED,2BAA2B;QAC3B,WAAW,CAAC,OAAO;aACd,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;aAC3D,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,CAAC,CAAA;QAEnD,MAAM,MAAM,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC,wBAAwB;YACrD,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,wBAAwB;YAC5C,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,WAAW,EACX,WAAW,CACd,CAAA;QAEP,MAAM,iBAAiB,GAAG,WAAW;aAChC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,IAAI,UAAU,GAAG,CAAC;aACtC,IAAI,CAAC,IAAI,CAAC,CAAA;QACf,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,oBAAoB,MAAM,kBAAkB,iBAAiB,GAAG,CACpE,CACJ,CAAA;QACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,qBAAqB,MAAM,GAAG,CAClC,CACJ,CAAA;QAED,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;QACjD,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAA;IAC/C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAChB,WAA2B,EAC3B,cAAuB;QAEvB,MAAM,KAAK,GAAG,iCAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,MAAM,EAAE,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAA;QACxC,MAAM,IAAI,GAAG,IAAI,CAAC,mBAAmB,CACjC,KAAK,EACL,KAAK,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,EACjD,cAAc,CACjB,CAAA;QACD,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;QACnC,KAAK,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;YACpC,MAAM,CAAC,SAAS,GAAG,KAAK,CAAA;QAC5B,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,sBAAsB,CACxB,WAA2B,EAC3B,gBAA6B;QAE7B,MAAM,KAAK,GAAG,iCAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAE5C,mGAAmG;QACnG,IAAI,CAAC,gBAAgB,CAAC,IAAI;YACtB,gBAAgB,CAAC,IAAI;gBACjB,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,oBAAoB,CAC/C,KAAK,EACL,gBAAgB,CAAC,WAAW,CAC/B,CAAA;QAET,MAAM,EAAE,GAAG,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE,gBAAgB,CAAC,CAAA;QAClE,MAAM,IAAI,GAAG,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,gBAAgB,CAAC,CAAA;QAClE,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;QACnC,KAAK,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,CAAA;IAC/C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,uBAAuB,CACzB,WAA2B,EAC3B,iBAAgC;QAEhC,MAAM,QAAQ,GAAG,iBAAiB,CAAC,GAAG,CAAC,CAAC,gBAAgB,EAAE,EAAE,CACxD,IAAI,CAAC,sBAAsB,CAAC,WAAW,EAAE,gBAAgB,CAAC,CAC7D,CAAA;QACD,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;IAC/B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,oBAAoB,CACtB,WAA2B,EAC3B,YAAkC;QAElC,MAAM,KAAK,GAAG,iCAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,MAAM,gBAAgB,GAAG,iCAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAChE,CAAC,CAAC,YAAY;YACd,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,YAAY,CAAC,CAAA;QACxD,IAAI,CAAC,gBAAgB;YACjB,MAAM,IAAI,oBAAY,CAClB,qDAAqD,KAAK,CAAC,IAAI,EAAE,CACpE,CAAA;QAEL,MAAM,EAAE,GAAG,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,gBAAgB,CAAC,CAAA;QAChE,MAAM,IAAI,GAAG,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE,gBAAgB,CAAC,CAAA;QACpE,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;QACnC,KAAK,CAAC,sBAAsB,CAAC,gBAAgB,CAAC,CAAA;IAClD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,qBAAqB,CACvB,WAA2B,EAC3B,iBAAgC;QAEhC,MAAM,QAAQ,GAAG,iBAAiB,CAAC,GAAG,CAAC,CAAC,gBAAgB,EAAE,EAAE,CACxD,IAAI,CAAC,oBAAoB,CAAC,WAAW,EAAE,gBAAgB,CAAC,CAC3D,CAAA;QACD,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;IAC/B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,qBAAqB,CACvB,WAA2B,EAC3B,eAA2B;QAE3B,MAAM,KAAK,GAAG,iCAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAE5C,mGAAmG;QACnG,IAAI,CAAC,eAAe,CAAC,IAAI;YACrB,eAAe,CAAC,IAAI;gBAChB,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,mBAAmB,CAC9C,KAAK,EACL,eAAe,CAAC,UAAW,CAC9B,CAAA;QAET,MAAM,EAAE,GAAG,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,eAAe,CAAC,CAAA;QAChE,MAAM,IAAI,GAAG,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,eAAe,CAAC,CAAA;QAChE,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;QACnC,KAAK,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAA;IAC7C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,sBAAsB,CACxB,WAA2B,EAC3B,gBAA8B;QAE9B,MAAM,QAAQ,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC,eAAe,EAAE,EAAE,CACtD,IAAI,CAAC,qBAAqB,CAAC,WAAW,EAAE,eAAe,CAAC,CAC3D,CAAA;QACD,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;IAC/B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,mBAAmB,CACrB,WAA2B,EAC3B,WAAgC;QAEhC,MAAM,KAAK,GAAG,iCAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,MAAM,eAAe,GAAG,iCAAe,CAAC,YAAY,CAAC,WAAW,CAAC;YAC7D,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,WAAW,CAAC,CAAA;QACtD,IAAI,CAAC,eAAe;YAChB,MAAM,IAAI,oBAAY,CAClB,oDAAoD,KAAK,CAAC,IAAI,EAAE,CACnE,CAAA;QAEL,MAAM,EAAE,GAAG,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,eAAe,CAAC,CAAA;QAC9D,MAAM,IAAI,GAAG,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,eAAe,CAAC,CAAA;QAClE,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;QACnC,KAAK,CAAC,qBAAqB,CAAC,eAAe,CAAC,CAAA;IAChD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,oBAAoB,CACtB,WAA2B,EAC3B,gBAA8B;QAE9B,MAAM,QAAQ,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC,eAAe,EAAE,EAAE,CACtD,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE,eAAe,CAAC,CACzD,CAAA;QACD,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;IAC/B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,yBAAyB,CAC3B,WAA2B,EAC3B,mBAAmC;QAEnC,MAAM,IAAI,oBAAY,CAAC,gDAAgD,CAAC,CAAA;IAC5E,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,0BAA0B,CAC5B,WAA2B,EAC3B,oBAAsC;QAEtC,MAAM,IAAI,oBAAY,CAAC,gDAAgD,CAAC,CAAA;IAC5E,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,uBAAuB,CACzB,WAA2B,EAC3B,eAAwC;QAExC,MAAM,IAAI,oBAAY,CAAC,gDAAgD,CAAC,CAAA;IAC5E,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,wBAAwB,CAC1B,WAA2B,EAC3B,oBAAsC;QAEtC,MAAM,IAAI,oBAAY,CAAC,gDAAgD,CAAC,CAAA;IAC5E,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB,CAClB,WAA2B,EAC3B,UAA2B;QAE3B,MAAM,KAAK,GAAG,iCAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAE5C,gFAAgF;QAChF,IAAI,CAAC,UAAU,CAAC,IAAI;YAChB,UAAU,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CAC3D,KAAK,EACL,UAAU,CAAC,WAAW,EACtB,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,EAC7B,UAAU,CAAC,qBAAqB,CACnC,CAAA;QAEL,MAAM,EAAE,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAA;QACtD,MAAM,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAA;QACtD,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;QACnC,KAAK,CAAC,aAAa,CAAC,UAAU,CAAC,CAAA;IACnC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB,CACnB,WAA2B,EAC3B,WAA8B;QAE9B,MAAM,QAAQ,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAC5C,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,UAAU,CAAC,CACjD,CAAA;QACD,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;IAC/B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAChB,WAA2B,EAC3B,gBAA0C;QAE1C,MAAM,KAAK,GAAG,iCAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,MAAM,UAAU,GAAG,iCAAe,CAAC,iBAAiB,CAAC,gBAAgB,CAAC;YAClE,CAAC,CAAC,gBAAgB;YAClB,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,IAAI,KAAK,gBAAgB,CAAC,CAAA;QAClE,IAAI,CAAC,UAAU;YACX,MAAM,IAAI,oBAAY,CAClB,+CAA+C,KAAK,CAAC,IAAI,EAAE,CAC9D,CAAA;QAEL,MAAM,EAAE,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAA;QACpD,MAAM,IAAI,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAA;QACxD,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;QACnC,KAAK,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAA;IACtC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe,CACjB,WAA2B,EAC3B,WAA8B;QAE9B,MAAM,QAAQ,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAC5C,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,UAAU,CAAC,CAC/C,CAAA;QACD,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;IAC/B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CACb,WAA2B,EAC3B,KAAiB;QAEjB,MAAM,KAAK,GAAG,iCAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAE5C,sFAAsF;QACtF,IAAI,CAAC,KAAK,CAAC,IAAI;YAAE,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;QAElE,MAAM,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;QAC5C,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAA;QACrC,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;QACnC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAA;IACzB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa,CACf,WAA2B,EAC3B,OAAqB;QAErB,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CACnC,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,KAAK,CAAC,CACvC,CAAA;QACD,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;IAC/B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CACX,WAA2B,EAC3B,WAAgC;QAEhC,MAAM,KAAK,GAAG,iCAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,MAAM,KAAK,GAAG,iCAAe,CAAC,YAAY,CAAC,WAAW,CAAC;YACnD,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,WAAW,CAAC,CAAA;QACvD,IAAI,CAAC,KAAK;YACN,MAAM,IAAI,oBAAY,CAClB,kBAAkB,WAAW,2BAA2B,KAAK,CAAC,IAAI,EAAE,CACvE,CAAA;QACL,sFAAsF;QACtF,IAAI,CAAC,KAAK,CAAC,IAAI;YAAE,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;QAElE,MAAM,EAAE,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAA;QACnC,MAAM,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;QAC9C,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;QACnC,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAA;IAC5B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CACb,WAA2B,EAC3B,OAAqB;QAErB,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CACnC,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,KAAK,CAAC,CACrC,CAAA;QACD,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;IAC/B,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,UAAU,CAAC,SAAiB;QAC9B,MAAM,IAAI,CAAC,KAAK,CAAC,kBAAkB,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC,CAAA;IACpE,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa;QACf,MAAM,0BAA0B,GAAG,IAAI,CAAC,mBAAmB,CAAA;QAC3D,IAAI,CAAC,0BAA0B;YAAE,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAA;QAC9D,IAAI,CAAC;YACD,aAAa;YACb,MAAM,cAAc,GAAG,uEAAuE,CAAA;YAC9F,MAAM,eAAe,GAAoB,MAAM,IAAI,CAAC,KAAK,CACrD,cAAc,CACjB,CAAA;YACD,MAAM,OAAO,CAAC,GAAG,CACb,eAAe,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAC7D,CAAA;YAED,0BAA0B;YAC1B,MAAM,iBAAiB,GAAG,sFAAsF,CAAA;YAChH,MAAM,kBAAkB,GAAoB,MAAM,IAAI,CAAC,KAAK,CACxD,iBAAiB,CACpB,CAAA;YACD,MAAM,OAAO,CAAC,GAAG,CACb,kBAAkB,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAChE,CAAA;YAED,cAAc;YACd,MAAM,eAAe,GAAG,8FAA8F,CAAA;YACtH,MAAM,gBAAgB,GAAoB,MAAM,IAAI,CAAC,KAAK,CACtD,eAAe,CAClB,CAAA;YACD,MAAM,OAAO,CAAC,GAAG,CACb,gBAAgB,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAC9D,CAAA;YACD,IAAI,CAAC,0BAA0B;gBAAE,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAA;QACnE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAI,CAAC;gBACD,2DAA2D;gBAC3D,IAAI,CAAC,0BAA0B;oBAC3B,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAA;YACxC,CAAC;YAAC,OAAO,aAAa,EAAE,CAAC,CAAA,CAAC;YAC1B,MAAM,KAAK,CAAA;QACf,CAAC;IACL,CAAC;IAED,4EAA4E;IAC5E,oBAAoB;IACpB,4EAA4E;IAElE,KAAK,CAAC,SAAS,CAAC,SAAoB;QAC1C,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,2BAA2B,EAAE,CAAC,CAAA;QACxE,IAAI,CAAC,QAAQ,EAAE,CAAC;YACZ,OAAO,EAAE,CAAA;QACb,CAAC;QAED,IAAI,CAAC,SAAS,EAAE,CAAC;YACb,SAAS,GAAG,EAAE,CAAA;QAClB,CAAC;QAED,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAA;QACvD,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAA;QAEnD,MAAM,cAAc,GAAG,SAAS;aAC3B,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;aACvD,GAAG,CAAC,CAAC,EAAE,MAAM,EAAE,SAAS,EAAE,EAAE,EAAE;YAC3B,IAAI,CAAC,MAAM,EAAE,CAAC;gBACV,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,IAAI,aAAa,CAAA;YACxD,CAAC;YAED,OAAO,oBAAoB,MAAM,uBAAuB,SAAS,IAAI,CAAA;QACzE,CAAC,CAAC;aACD,IAAI,CAAC,MAAM,CAAC,CAAA;QAEjB,IAAI,KAAK,GACL,qBAAqB,IAAI,CAAC,UAAU,CAChC,IAAI,CAAC,2BAA2B,EAAE,CACrC,OAAO;YACR,2HAA2H;YAC3H,yBAAyB,qCAAiB,CAAC,iBAAiB,OAAO,qCAAiB,CAAC,IAAI,IAAI,CAAA;QACjG,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC;YAAE,KAAK,IAAI,QAAQ,cAAc,EAAE,CAAA;QAEhE,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;QACvC,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,MAAW,EAAE,EAAE;YAC/B,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAA;YAE7D,MAAM,IAAI,GAAG,IAAI,WAAI,EAAE,CAAA;YACvB,IAAI,CAAC,QAAQ;gBACT,UAAU,CAAC,QAAQ,IAAI,MAAM,CAAC,UAAU,CAAC,IAAI,eAAe,CAAA;YAChE,IAAI,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,aAAa,CAAA;YACpE,IAAI,CAAC,IAAI,GAAG,UAAU,CAAC,SAAS,CAAA;YAChC,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,OAAO,CAAC,CAAA;YACjC,IAAI,CAAC,YAAY;gBACb,MAAM,CAAC,MAAM,CAAC,KAAK,qCAAiB,CAAC,iBAAiB,CAAA;YAC1D,OAAO,IAAI,CAAA;QACf,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,UAAU,CAAC,UAAqB;QAC5C,IAAI,UAAU,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACxC,OAAO,EAAE,CAAA;QACb,CAAC;QAED,MAAM,QAAQ,GAA4C,EAAE,CAAA;QAE5D,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAA;QACnD,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAA;QAEvD,IAAI,CAAC,UAAU,EAAE,CAAC;YACd,MAAM,SAAS,GAAG,gDAAgD,CAAA;YAClE,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAA;QACnD,CAAC;aAAM,CAAC;YACJ,MAAM,eAAe,GAAG,UAAU;iBAC7B,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE;gBACf,MAAM,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;gBAElC,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;oBACpB,MAAM,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,GAAG,KAAK,CAAA;oBAC9B,OAAO,eAAe,MAAM,yBAAyB,IAAI,IAAI,CAAA;gBACjE,CAAC;qBAAM,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBAC5B,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,KAAK,CAAA;oBAC5B,OAAO,eAAe,MAAM,yBAAyB,IAAI,IAAI,CAAA;gBACjE,CAAC;qBAAM,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBAC5B,MAAM,CAAC,IAAI,CAAC,GAAG,KAAK,CAAA;oBACpB,OAAO,oBAAoB,IAAI,IAAI,CAAA;gBACvC,CAAC;qBAAM,CAAC;oBACJ,OAAO,OAAO,CAAA;gBAClB,CAAC;YACL,CAAC,CAAC;iBACD,IAAI,CAAC,MAAM,CAAC,CAAA;YACjB,MAAM,SAAS,GAAG,wDAAwD,eAAe,EAAE,CAAA;YAC3F,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAA;QACnD,CAAC;QAED,yDAAyD;QACzD,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACxB,OAAO,EAAE,CAAA;QACb,CAAC;QAED,iDAAiD;QACjD,MAAM,gBAAgB,GAAG,QAAQ;aAC5B,GAAG,CAAC,CAAC,EAAE,UAAU,EAAE,KAAK,EAAE,EAAE,EAAE;YAC3B,OAAO,mBAAmB,KAAK,6BAA6B,UAAU,IAAI,CAAA;QAC9E,CAAC,CAAC;aACD,IAAI,CAAC,MAAM,CAAC,CAAA;QACjB,MAAM,UAAU,GAAG,2CAA2C,gBAAgB,GAAG,CAAA;QAEjF,MAAM,UAAU,GACZ,4EAA4E;YAC5E,mGAAmG;YACnG,yBAAyB;YACzB,oHAAoH;YACpH,kHAAkH;YAClH,UAAU,gBAAgB,wCAAwC;YAClE,4EAA4E,CAAA;QAEhF,MAAM,cAAc,GAChB,uIAAuI;YACvI,wFAAwF;YACxF,6BAA6B;YAC7B,yHAAyH;YACzH,qLAAqL;YACrL,UAAU,gBAAgB,mCAAmC,CAAA;QAEjE,MAAM,cAAc,GAChB,kIAAkI;YAClI,6BAA6B;YAC7B,yHAAyH;YACzH,UAAU,gBAAgB,kFAAkF,CAAA;QAEhH,MAAM,CACF,SAAS,EACT,SAAS,EACT,aAAa,EACb,aAAa,EAChB,GAAsB,MAAM,OAAO,CAAC,GAAG,CAAC;YACrC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;YACtB,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;YACtB,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC;YAC1B,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC;SAC7B,CAAC,CAAA;QAEF,kCAAkC;QAClC,OAAO,MAAM,OAAO,CAAC,GAAG,CACpB,QAAQ,CAAC,GAAG,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;YAC3B,MAAM,KAAK,GAAG,IAAI,aAAK,EAAE,CAAA;YACzB,MAAM,KAAK,GACP,OAAO,CAAC,OAAO,CAAC,KAAK,aAAa;gBAClC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM;oBACxB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,KAAK,aAAa,CAAC;gBAC7C,CAAC,CAAC,SAAS;gBACX,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;YAC1B,KAAK,CAAC,QAAQ,GAAG,eAAe,CAAA;YAChC,KAAK,CAAC,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC,CAAA;YAC/B,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CACnC,OAAO,CAAC,YAAY,CAAC,EACrB,KAAK,CACR,CAAA;YAED,yCAAyC;YACzC,KAAK,CAAC,OAAO,GAAG,MAAM,OAAO,CAAC,GAAG,CAC7B,SAAS;iBACJ,MAAM,CACH,CAAC,QAAQ,EAAE,EAAE,CACT,QAAQ,CAAC,OAAO,CAAC,KAAK,OAAO,CAAC,OAAO,CAAC;gBACtC,QAAQ,CAAC,YAAY,CAAC;oBAClB,OAAO,CAAC,YAAY,CAAC;gBACzB,6CAA6C;gBAC7C,8CAA8C;gBAC9C,CAAC,CACG,QAAQ,CAAC,gBAAgB,CAAC,KAAK,KAAK;oBACpC,QAAQ,CAAC,gBAAgB,CAAC,KAAK,IAAI,CACtC,CACR;iBACA,GAAG,CAAC,KAAK,EAAE,QAAQ,EAAE,EAAE;gBACpB,MAAM,iBAAiB,GAAG,aAAa,CAAC,MAAM,CAC1C,CAAC,YAAY,EAAE,EAAE,CACb,YAAY,CAAC,OAAO,CAAC;oBACjB,QAAQ,CAAC,OAAO,CAAC;oBACrB,YAAY,CAAC,YAAY,CAAC;wBACtB,QAAQ,CAAC,YAAY,CAAC;oBAC1B,YAAY,CAAC,aAAa,CAAC;wBACvB,QAAQ,CAAC,aAAa,CAAC,CAClC,CAAA;gBAED,MAAM,iBAAiB,GAAG,iBAAiB,CAAC,MAAM,CAC9C,CAAC,UAAU,EAAE,EAAE,CACX,UAAU,CAAC,iBAAiB,CAAC,KAAK,GAAG,CAC5C,CAAA;gBACD,MAAM,qBAAqB,GACvB,iBAAiB,CAAC,KAAK,CAAC,CAAC,gBAAgB,EAAE,EAAE;oBACzC,OAAO,aAAa,CAAC,IAAI,CACrB,CAAC,YAAY,EAAE,EAAE,CACb,YAAY,CAAC,OAAO,CAAC;wBACjB,QAAQ,CAAC,OAAO,CAAC;wBACrB,YAAY,CAAC,YAAY,CAAC;4BACtB,QAAQ,CAAC,YAAY,CAAC;wBAC1B,YAAY,CAAC,aAAa,CAAC;4BACvB,QAAQ,CAAC,aAAa,CAAC;wBAC3B,YAAY,CAAC,iBAAiB,CAAC;4BAC3B,gBAAgB,CACZ,iBAAiB,CACpB;wBACL,YAAY,CAAC,iBAAiB,CAAC;4BAC3B,GAAG,CACd,CAAA;gBACL,CAAC,CAAC,CAAA;gBAEN,MAAM,WAAW,GAAG,IAAI,yBAAW,EAAE,CAAA;gBACrC,WAAW,CAAC,IAAI,GAAG,QAAQ,CAAC,aAAa,CAAC,CAAA;gBAC1C,WAAW,CAAC,IAAI;oBACZ,QAAQ,CAAC,WAAW,CAAC,CAAC,WAAW,EAAE,CAAA;gBACvC,IAAI,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;oBACpC,WAAW,CAAC,IAAI,GAAG,WAAW,CAAC,IAAI,CAAC,OAAO,CACvC,YAAY,EACZ,EAAE,CACL,CAAA;gBAEL,+CAA+C;gBAC/C,IACI,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,OAAO,CACrC,WAAW,CAAC,IAAkB,CACjC,KAAK,CAAC,CAAC,EACV,CAAC;oBACC,MAAM,MAAM,GACR,WAAW,CAAC,IAAI,KAAK,KAAK;wBACtB,CAAC,CAAC,QAAQ,CAAC,aAAa,CAAC;wBACzB,CAAC,CAAC,QAAQ,CAAC,sBAAsB,CAAC,CAAA;oBAC1C,WAAW,CAAC,MAAM;wBACd,MAAM;4BACN,CAAC,IAAI,CAAC,qBAAqB,CACvB,KAAK,EACL,WAAW,EACX,MAAM,CACT;4BACG,CAAC,CAAC,MAAM,CAAC,QAAQ,EAAE;4BACnB,CAAC,CAAC,EAAE,CAAA;gBAChB,CAAC;gBAED,IACI,WAAW,CAAC,IAAI,KAAK,QAAQ;oBAC7B,WAAW,CAAC,IAAI,KAAK,OAAO,EAC9B,CAAC;oBACC,IACI,QAAQ,CAAC,gBAAgB,CAAC,KAAK,IAAI;wBACnC,CAAC,IAAI,CAAC,wBAAwB,CAC1B,KAAK,EACL,WAAW,EACX,QAAQ,CAAC,gBAAgB,CAAC,CAC7B;wBAED,WAAW,CAAC,SAAS;4BACjB,QAAQ,CAAC,gBAAgB,CAAC,CAAA;oBAClC,IACI,QAAQ,CAAC,YAAY,CAAC,KAAK,IAAI;wBAC/B,CAAC,IAAI,CAAC,oBAAoB,CACtB,KAAK,EACL,WAAW,EACX,QAAQ,CAAC,YAAY,CAAC,CACzB;wBAED,WAAW,CAAC,KAAK,GAAG,QAAQ,CAAC,YAAY,CAAC,CAAA;gBAClD,CAAC;qBAAM,IACH,CAAC,WAAW,CAAC,IAAI,KAAK,WAAW;oBAC7B,WAAW,CAAC,IAAI;wBACZ,0BAA0B;oBAC9B,WAAW,CAAC,IAAI;wBACZ,gCAAgC,CAAC;oBACzC,QAAQ,CAAC,YAAY,CAAC,KAAK,IAAI,EACjC,CAAC;oBACC,WAAW,CAAC,SAAS;wBACjB,CAAC,IAAI,CAAC,wBAAwB,CAC1B,KAAK,EACL,WAAW,EACX,QAAQ,CAAC,YAAY,CAAC,CACzB;4BACG,CAAC,CAAC,QAAQ,CAAC,YAAY,CAAC;4BACxB,CAAC,CAAC,SAAS,CAAA;gBACvB,CAAC;gBAED,WAAW,CAAC,OAAO;oBACf,QAAQ,CAAC,cAAc,CAAC,KAAK,IAAI;wBACjC,QAAQ,CAAC,cAAc,CAAC,KAAK,SAAS;wBACtC,QAAQ,CAAC,gBAAgB,CAAC,KAAK,IAAI;wBACnC,QAAQ,CAAC,cAAc,CAAC,CAAC,IAAI,EAAE,KAAK,MAAM;wBACtC,CAAC,CAAC,CAAC,WAAW,CAAC,OAAO;4BAChB,QAAQ,CAAC,cAAc,CAAC,CAAC,IAAI,EAAE,CAAC;wBACtC,CAAC,CAAC,SAAS,CAAA;gBAEnB,MAAM,iBAAiB,GAAG,iBAAiB,CAAC,IAAI,CAC5C,CAAC,UAAU,EAAE,EAAE,CACX,UAAU,CAAC,iBAAiB,CAAC,KAAK,GAAG,CAC5C,CAAA;gBACD,IAAI,iBAAiB,EAAE,CAAC;oBACpB,WAAW,CAAC,SAAS,GAAG,IAAI,CAAA;oBAC5B,0DAA0D;oBAC1D,MAAM,yBAAyB,GAC3B,aAAa,CAAC,MAAM,CAChB,CAAC,UAAU,EAAE,EAAE,CACX,UAAU,CAAC,OAAO,CAAC;wBACf,QAAQ,CAAC,OAAO,CAAC;wBACrB,UAAU,CAAC,YAAY,CAAC;4BACpB,QAAQ,CAAC,YAAY,CAAC;wBAC1B,UAAU,CAAC,aAAa,CAAC;4BACrB,QAAQ,CAAC,aAAa,CAAC;wBAC3B,UAAU,CAAC,iBAAiB,CAAC;4BACzB,GAAG,CACd,CAAA;oBAEL,2BAA2B;oBAC3B,MAAM,WAAW,GACb,yBAAyB,CAAC,GAAG,CACzB,CAAC,UAAU,EAAE,EAAE,CACX,UAAU,CAAC,aAAa,CAAC,CAChC,CAAA;oBACL,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAA;oBAEzC,4CAA4C;oBAC5C,MAAM,MAAM,GACR,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,KAAK,EACL,WAAW,CACd,CAAA;oBAEL,4EAA4E;oBAC5E,IACI,iBAAiB,CAAC,iBAAiB,CAAC;wBACpC,MAAM,EACR,CAAC;wBACC,WAAW,CAAC,wBAAwB;4BAChC,iBAAiB,CAAC,iBAAiB,CAAC,CAAA;oBAC5C,CAAC;gBACL,CAAC;gBAED,WAAW,CAAC,UAAU;oBAClB,QAAQ,CAAC,UAAU,CAAC,KAAK,GAAG,CAAA;gBAChC,WAAW,CAAC,QAAQ;oBAChB,iBAAiB,CAAC,MAAM,GAAG,CAAC;wBAC5B,CAAC,qBAAqB,CAAA;gBAC1B,WAAW,CAAC,WAAW;oBACnB,QAAQ,CAAC,iBAAiB,CAAC,KAAK,KAAK,CAAA;gBACzC,IAAI,WAAW,CAAC,WAAW,EAAE,CAAC;oBAC1B,WAAW,CAAC,kBAAkB,GAAG,WAAW,CAAA;oBAC5C,WAAW,CAAC,OAAO,GAAG,SAAS,CAAA;gBACnC,CAAC;gBACD,WAAW,CAAC,OAAO,GAAG,EAAE,CAAA,CAAC,OAAO;gBAEhC,IAAI,QAAQ,CAAC,gBAAgB,CAAC,KAAK,KAAK,EAAE,CAAC;oBACvC,WAAW,CAAC,aAAa,GAAG,SAAS,CAAA;oBAErC,MAAM,iBAAiB,GACnB,IAAI,CAAC,wBAAwB,CAAC;wBAC1B,KAAK,EAAE,OAAO,CAAC,YAAY,CAAC;wBAC5B,IAAI,EAAE,qCAAiB,CAAC,gBAAgB;wBACxC,IAAI,EAAE,WAAW,CAAC,IAAI;qBACzB,CAAC,CAAA;oBAEN,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,KAAK,CAC5B,iBAAiB,CAAC,KAAK,EACvB,iBAAiB,CAAC,UAAU,CAC/B,CAAA;oBACD,IAAI,OAAO,CAAC,CAAC,CAAC,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;wBACjC,WAAW,CAAC,YAAY,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAA;oBAC/C,CAAC;yBAAM,CAAC;wBACJ,WAAW,CAAC,YAAY,GAAG,EAAE,CAAA;oBACjC,CAAC;gBACL,CAAC;gBAED,OAAO,WAAW,CAAA;YACtB,CAAC,CAAC,CACT,CAAA;YAED,yFAAyF;YACzF,MAAM,sBAAsB,GAAG,mBAAQ,CAAC,IAAI,CACxC,aAAa,CAAC,MAAM,CAAC,CAAC,YAAY,EAAE,EAAE;gBAClC,OAAO,CACH,YAAY,CAAC,YAAY,CAAC;oBACtB,OAAO,CAAC,YAAY,CAAC;oBACzB,YAAY,CAAC,OAAO,CAAC,KAAK,OAAO,CAAC,OAAO,CAAC;oBAC1C,YAAY,CAAC,iBAAiB,CAAC,KAAK,GAAG,CAC1C,CAAA;YACL,CAAC,CAAC,EACF,CAAC,YAAY,EAAE,EAAE,CAAC,YAAY,CAAC,iBAAiB,CAAC,CACpD,CAAA;YAED,KAAK,CAAC,OAAO,GAAG,sBAAsB,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE;gBACtD,MAAM,OAAO,GAAG,aAAa,CAAC,MAAM,CAChC,CAAC,GAAG,EAAE,EAAE,CACJ,GAAG,CAAC,iBAAiB,CAAC;oBACtB,UAAU,CAAC,iBAAiB,CAAC,CACpC,CAAA;gBACD,OAAO,IAAI,yBAAW,CAAC;oBACnB,IAAI,EAAE,UAAU,CAAC,iBAAiB,CAAC;oBACnC,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC;iBACpD,CAAC,CAAA;YACN,CAAC,CAAC,CAAA;YAEF,uFAAuF;YACvF,MAAM,qBAAqB,GAAG,mBAAQ,CAAC,IAAI,CACvC,aAAa,CAAC,MAAM,CAAC,CAAC,YAAY,EAAE,EAAE;gBAClC,OAAO,CACH,YAAY,CAAC,YAAY,CAAC;oBACtB,OAAO,CAAC,YAAY,CAAC;oBACzB,YAAY,CAAC,OAAO,CAAC,KAAK,OAAO,CAAC,OAAO,CAAC;oBAC1C,YAAY,CAAC,iBAAiB,CAAC,KAAK,GAAG,CAC1C,CAAA;YACL,CAAC,CAAC,EACF,CAAC,YAAY,EAAE,EAAE,CAAC,YAAY,CAAC,iBAAiB,CAAC,CACpD,CAAA;YAED,KAAK,CAAC,MAAM,GAAG,qBAAqB,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE;gBACpD,MAAM,MAAM,GAAG,aAAa,CAAC,MAAM,CAC/B,CAAC,GAAG,EAAE,EAAE,CACJ,GAAG,CAAC,YAAY,CAAC,KAAK,UAAU,CAAC,YAAY,CAAC;oBAC9C,GAAG,CAAC,OAAO,CAAC,KAAK,UAAU,CAAC,OAAO,CAAC;oBACpC,GAAG,CAAC,iBAAiB,CAAC;wBAClB,UAAU,CAAC,iBAAiB,CAAC,CACxC,CAAA;gBACD,OAAO,IAAI,uBAAU,CAAC;oBAClB,IAAI,EAAE,UAAU,CAAC,iBAAiB,CAAC;oBACnC,WAAW,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC;oBAChD,UAAU,EAAE,UAAU,CAAC,kBAAkB,CAAC;iBAC7C,CAAC,CAAA;YACN,CAAC,CAAC,CAAA;YAEF,kGAAkG;YAClG,MAAM,0BAA0B,GAAG,mBAAQ,CAAC,IAAI,CAC5C,aAAa,CAAC,MAAM,CAChB,CAAC,YAAY,EAAE,EAAE,CACb,YAAY,CAAC,OAAO,CAAC,KAAK,OAAO,CAAC,OAAO,CAAC;gBAC1C,YAAY,CAAC,YAAY,CAAC;oBACtB,OAAO,CAAC,YAAY,CAAC,CAChC,EACD,CAAC,YAAY,EAAE,EAAE,CAAC,YAAY,CAAC,iBAAiB,CAAC,CACpD,CAAA;YAED,KAAK,CAAC,WAAW,GAAG,0BAA0B,CAAC,GAAG,CAC9C,CAAC,YAAY,EAAE,EAAE;gBACb,MAAM,WAAW,GAAG,aAAa,CAAC,MAAM,CACpC,CAAC,IAAI,EAAE,EAAE,CACL,IAAI,CAAC,YAAY,CAAC;oBACd,YAAY,CAAC,YAAY,CAAC;oBAC9B,IAAI,CAAC,OAAO,CAAC,KAAK,YAAY,CAAC,OAAO,CAAC;oBACvC,IAAI,CAAC,iBAAiB,CAAC;wBACnB,YAAY,CAAC,iBAAiB,CAAC,CAC1C,CAAA;gBACD,OAAO,IAAI,iCAAe,CAAC;oBACvB,IAAI,EAAE,YAAY,CAAC,iBAAiB,CAAC;oBACrC,WAAW,EAAE,WAAW,CAAC,GAAG,CACxB,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,CAChC;oBACD,kBAAkB,EAAE,KAAK,CAAC,QAAQ;oBAClC,gBAAgB,EAAE,YAAY,CAAC,OAAO,CAAC;oBACvC,mBAAmB,EACf,YAAY,CAAC,uBAAuB,CAAC;oBACzC,qBAAqB,EAAE,WAAW,CAAC,GAAG,CAClC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAC3C;oBACD,QAAQ,EAAE,YAAY,CAAC,WAAW,CAAC;oBACnC,QAAQ,EAAE,WAAW,EAAE,0FAA0F;iBACpH,CAAC,CAAA;YACN,CAAC,CACJ,CAAA;YAED,yDAAyD;YACzD,2DAA2D;YAC3D,EAAE;YACF,wDAAwD;YACxD,4DAA4D;YAC5D,2DAA2D;YAC3D,6BAA6B;YAC7B,MAAM,uBAAuB,GAAG,SAAS;iBACpC,MAAM,CACH,CAAC,QAAQ,EAAE,EAAE,CACT,QAAQ,CAAC,OAAO,CAAC,KAAK,OAAO,CAAC,OAAO,CAAC;gBACtC,QAAQ,CAAC,YAAY,CAAC,KAAK,OAAO,CAAC,YAAY,CAAC;gBAChD,QAAQ,CAAC,gBAAgB,CAAC,KAAK,KAAK;gBACpC,QAAQ,CAAC,gBAAgB,CAAC,KAAK,IAAI,CAC1C;iBACA,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE;gBACf,MAAM,kBAAkB,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,EAAE,CACnD,CAAC,CAAC,cAAc,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,CACtD,CAAA;gBAED,IAAI,CAAC,kBAAkB;oBAAE,OAAO,GAAG,CAAA;gBAEnC,OAAO;oBACH,GAAG,GAAG;oBACN,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,EACd,kBAAkB,CAAC,aAAa,CAAC;iBACxC,CAAA;YACL,CAAC,EAAE,EAAE,CAAC,CAAA;YAEV,oDAAoD;YACpD,KAAK,CAAC,OAAO,GAAG,SAAS;iBACpB,MAAM,CACH,CAAC,OAAO,EAAE,EAAE,CACR,OAAO,CAAC,YAAY,CAAC,KAAK,OAAO,CAAC,YAAY,CAAC;gBAC/C,OAAO,CAAC,OAAO,CAAC,KAAK,OAAO,CAAC,OAAO,CAAC,CAC5C;iBACA,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE;gBACb,EAAE;gBACF,MAAM,WAAW,GAAG,OAAO,CAAC,cAAc,CAAC;qBACtC,KAAK,CAAC,GAAG,CAAC;qBACV,GAAG,CACA,CACI,UAAgD,EAClD,EAAE,CACA,uBAAuB,CAAC,UAAU,CAAC;oBACnC,UAAU,CACjB,CAAA;gBAEL,OAAO,IAAI,uBAAU,CAAC;oBAClB,IAAI,EAAE,OAAO,CAAC,YAAY,CAAC;oBAC3B,WAAW;oBACX,QAAQ,EAAE,OAAO,CAAC,YAAY,CAAC,KAAK,QAAQ;iBAC/C,CAAC,CAAA;YACN,CAAC,CAAC,CAAA;YAEN,OAAO,KAAK,CAAA;QAChB,CAAC,CAAC,CACL,CAAA;IACL,CAAC;IAED;;OAEG;IACO,cAAc,CAAC,KAAY,EAAE,iBAA2B;QAC9D,MAAM,iBAAiB,GAAG,KAAK,CAAC,OAAO;aAClC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;aAClD,IAAI,CAAC,IAAI,CAAC,CAAA;QACf,IAAI,GAAG,GAAG,gBAAgB,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,iBAAiB,EAAE,CAAA;QAExE,KAAK,CAAC,OAAO;aACR,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC;aACnC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;YAChB,MAAM,aAAa,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CACpC,CAAC,MAAM,EAAE,EAAE,CACP,MAAM,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC;gBAC/B,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,IAAI,CAC5C,CAAA;YACD,IAAI,CAAC,aAAa;gBACd,KAAK,CAAC,OAAO,CAAC,IAAI,CACd,IAAI,yBAAW,CAAC;oBACZ,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,oBAAoB,CACrD,KAAK,EACL,CAAC,MAAM,CAAC,IAAI,CAAC,CAChB;oBACD,WAAW,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC;iBAC7B,CAAC,CACL,CAAA;QACT,CAAC,CAAC,CAAA;QAEN,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC3B,MAAM,UAAU,GAAG,KAAK,CAAC,OAAO;iBAC3B,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;gBACZ,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI;oBAC1B,CAAC,CAAC,MAAM,CAAC,IAAI;oBACb,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,oBAAoB,CAC/C,KAAK,EACL,MAAM,CAAC,WAAW,CACrB,CAAA;gBACP,MAAM,WAAW,GAAG,MAAM,CAAC,WAAW;qBACjC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,IAAI,UAAU,GAAG,CAAC;qBACtC,IAAI,CAAC,IAAI,CAAC,CAAA;gBACf,OAAO,eAAe,UAAU,aAAa,WAAW,GAAG,CAAA;YAC/D,CAAC,CAAC;iBACD,IAAI,CAAC,IAAI,CAAC,CAAA;YAEf,GAAG,IAAI,KAAK,UAAU,EAAE,CAAA;QAC5B,CAAC;QAED,IAAI,KAAK,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC1B,MAAM,SAAS,GAAG,KAAK,CAAC,MAAM;iBACzB,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;gBACX,MAAM,SAAS,GAAG,KAAK,CAAC,IAAI;oBACxB,CAAC,CAAC,KAAK,CAAC,IAAI;oBACZ,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,mBAAmB,CAC9C,KAAK,EACL,KAAK,CAAC,UAAW,CACpB,CAAA;gBACP,OAAO,eAAe,SAAS,YAAY,KAAK,CAAC,UAAU,GAAG,CAAA;YAClE,CAAC,CAAC;iBACD,IAAI,CAAC,IAAI,CAAC,CAAA;YAEf,GAAG,IAAI,KAAK,SAAS,EAAE,CAAA;QAC3B,CAAC;QAED,IAAI,KAAK,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,IAAI,iBAAiB,EAAE,CAAC;YACpD,MAAM,cAAc,GAAG,KAAK,CAAC,WAAW;iBACnC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE;gBACR,MAAM,WAAW,GAAG,EAAE,CAAC,WAAW;qBAC7B,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,IAAI,UAAU,GAAG,CAAC;qBACtC,IAAI,CAAC,IAAI,CAAC,CAAA;gBACf,IAAI,CAAC,EAAE,CAAC,IAAI;oBACR,EAAE,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACnD,KAAK,EACL,EAAE,CAAC,WAAW,EACd,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,EACrB,EAAE,CAAC,qBAAqB,CAC3B,CAAA;gBACL,MAAM,qBAAqB,GAAG,EAAE,CAAC,qBAAqB;qBACjD,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,IAAI,UAAU,GAAG,CAAC;qBACtC,IAAI,CAAC,IAAI,CAAC,CAAA;gBACf,IAAI,UAAU,GAAG,eACb,EAAE,CAAC,IACP,kBAAkB,WAAW,gBAAgB,IAAI,CAAC,UAAU,CACxD,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,CACxB,KAAK,qBAAqB,GAAG,CAAA;gBAC9B,IAAI,EAAE,CAAC,QAAQ,IAAI,EAAE,CAAC,QAAQ,KAAK,WAAW,EAAE,CAAC;oBAC7C,uFAAuF;oBACvF,UAAU,IAAI,cAAc,EAAE,CAAC,QAAQ,EAAE,CAAA;gBAC7C,CAAC;gBACD,OAAO,UAAU,CAAA;YACrB,CAAC,CAAC;iBACD,IAAI,CAAC,IAAI,CAAC,CAAA;YAEf,GAAG,IAAI,KAAK,cAAc,EAAE,CAAA;QAChC,CAAC;QAED,MAAM,cAAc,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CACvC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,SAAS,CAC/B,CAAA;QACD,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC5B,MAAM,cAAc,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC,wBAAwB;gBAC7D,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,wBAAwB;gBAC5C,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,KAAK,EACL,cAAc,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAC9C,CAAA;YAEP,MAAM,WAAW,GAAG,cAAc;iBAC7B,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,MAAM,CAAC,IAAI,GAAG,CAAC;iBACnC,IAAI,CAAC,IAAI,CAAC,CAAA;YACf,GAAG,IAAI,iBAAiB,cAAc,kBAAkB,WAAW,GAAG,CAAA;QAC1E,CAAC;QAED,GAAG,IAAI,GAAG,CAAA;QAEV,OAAO,IAAI,aAAK,CAAC,GAAG,CAAC,CAAA;IACzB,CAAC;IAED;;OAEG;IACO,YAAY,CAClB,WAA2B,EAC3B,OAAiB;QAEjB,MAAM,KAAK,GAAG,OAAO;YACjB,CAAC,CAAC,wBAAwB,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE;YACxD,CAAC,CAAC,cAAc,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE,CAAA;QAClD,OAAO,IAAI,aAAK,CAAC,KAAK,CAAC,CAAA;IAC3B,CAAC;IAES,aAAa,CAAC,IAAU;QAC9B,MAAM,kBAAkB,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE,CAAA;QACnE,IAAI,OAAO,IAAI,CAAC,UAAU,KAAK,QAAQ,EAAE,CAAC;YACtC,OAAO,IAAI,aAAK,CACZ,UAAU,kBAAkB,QAAQ,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OACrD,IAAI,CAAC,UACT,EAAE,CACL,CAAA;QACL,CAAC;aAAM,CAAC;YACJ,OAAO,IAAI,aAAK,CACZ,UAAU,kBAAkB,QAAQ,IAAI,CAAC,UAAU,CAC/C,IAAI,CACP,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,QAAQ,EAAE,EAAE,CACxD,CAAA;QACL,CAAC;IACL,CAAC;IAES,uBAAuB,CAAC,IAAU;QACxC,MAAM,UAAU,GACZ,OAAO,IAAI,CAAC,UAAU,KAAK,QAAQ;YAC/B,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE;YACxB,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,QAAQ,EAAE,CAAA;QACrD,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY;YAC1B,CAAC,CAAC,qCAAiB,CAAC,iBAAiB;YACrC,CAAC,CAAC,qCAAiB,CAAC,IAAI,CAAA;QAC5B,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA;QAC9D,OAAO,IAAI,CAAC,wBAAwB,CAAC;YACjC,IAAI,EAAE,IAAI;YACV,IAAI,EAAE,SAAS;YACf,MAAM,EAAE,MAAM;YACd,KAAK,EAAE,UAAU;SACpB,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACO,WAAW,CAAC,IAAU;QAC5B,MAAM,kBAAkB,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE,CAAA;QACnE,OAAO,IAAI,aAAK,CACZ,QAAQ,kBAAkB,QAAQ,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAC5D,CAAA;IACL,CAAC;IAED;;OAEG;IACO,uBAAuB,CAAC,IAAU;QACxC,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY;YAC1B,CAAC,CAAC,qCAAiB,CAAC,iBAAiB;YACrC,CAAC,CAAC,qCAAiB,CAAC,IAAI,CAAA;QAC5B,OAAO,IAAI,CAAC,wBAAwB,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,CAAA;IACnE,CAAC;IAED;;OAEG;IACO,cAAc,CAAC,KAAY,EAAE,KAAiB;QACpD,MAAM,OAAO,GAAG,KAAK,CAAC,WAAW;aAC5B,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,IAAI,UAAU,GAAG,CAAC;aACtC,IAAI,CAAC,IAAI,CAAC,CAAA;QACf,OAAO,IAAI,aAAK,CACZ,UAAU,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,UACrC,KAAK,CAAC,IACV,QAAQ,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,OAAO,GAAG,CAChD,CAAA;IACL,CAAC;IAED;;OAEG;IACO,YAAY,CAAC,WAAgC;QACnD,MAAM,SAAS,GAAG,iCAAe,CAAC,YAAY,CAAC,WAAW,CAAC;YACvD,CAAC,CAAC,WAAW,CAAC,IAAI;YAClB,CAAC,CAAC,WAAW,CAAA;QACjB,OAAO,IAAI,aAAK,CAAC,eAAe,SAAS,GAAG,CAAC,CAAA;IACjD,CAAC;IAED;;OAEG;IACO,mBAAmB,CACzB,KAAY,EACZ,WAAqB,EACrB,cAAuB;QAEvB,MAAM,cAAc,GAAG,cAAc;YACjC,CAAC,CAAC,cAAc;YAChB,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CAAC,KAAK,EAAE,WAAW,CAAC,CAAA;QAEvE,MAAM,iBAAiB,GAAG,WAAW;aAChC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,IAAI,UAAU,GAAG,CAAC;aACtC,IAAI,CAAC,IAAI,CAAC,CAAA;QAEf,OAAO,IAAI,aAAK,CACZ,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,oBAAoB,cAAc,kBAAkB,iBAAiB,GAAG,CAC5E,CAAA;IACL,CAAC;IAED;;OAEG;IACO,iBAAiB,CAAC,KAAY;QACpC,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,MAAM;YAC5B,MAAM,IAAI,oBAAY,CAAC,SAAS,KAAK,uBAAuB,CAAC,CAAA;QAEjE,MAAM,WAAW,GAAG,KAAK,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;QACrE,MAAM,cAAc,GAAG,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,wBAAwB,CAAA;QACvE,MAAM,cAAc,GAAG,cAAc;YACjC,CAAC,CAAC,cAAc;YAChB,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CAAC,KAAK,EAAE,WAAW,CAAC,CAAA;QAEvE,OAAO,IAAI,aAAK,CACZ,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,qBAAqB,cAAc,GAAG,CAC1C,CAAA;IACL,CAAC;IAED;;OAEG;IACO,yBAAyB,CAC/B,KAAY,EACZ,gBAA6B;QAE7B,MAAM,WAAW,GAAG,gBAAgB,CAAC,WAAW;aAC3C,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,GAAG,GAAG,MAAM,GAAG,GAAG,CAAC;aACnC,IAAI,CAAC,IAAI,CAAC,CAAA;QACf,OAAO,IAAI,aAAK,CACZ,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,oBACjC,gBAAgB,CAAC,IACrB,aAAa,WAAW,GAAG,CAC9B,CAAA;IACL,CAAC;IAED;;OAEG;IACO,uBAAuB,CAC7B,KAAY,EACZ,YAAkC;QAElC,MAAM,UAAU,GAAG,iCAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAC1D,CAAC,CAAC,YAAY,CAAC,IAAI;YACnB,CAAC,CAAC,YAAY,CAAA;QAClB,OAAO,IAAI,aAAK,CACZ,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,qBAAqB,UAAU,GAAG,CACtC,CAAA;IACL,CAAC;IAED;;OAEG;IACO,wBAAwB,CAC9B,KAAY,EACZ,eAA2B;QAE3B,OAAO,IAAI,aAAK,CACZ,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,oBACjC,eAAe,CAAC,IACpB,YAAY,eAAe,CAAC,UAAU,GAAG,CAC5C,CAAA;IACL,CAAC;IAED;;OAEG;IACO,sBAAsB,CAC5B,KAAY,EACZ,WAAgC;QAEhC,MAAM,SAAS,GAAG,iCAAe,CAAC,YAAY,CAAC,WAAW,CAAC;YACvD,CAAC,CAAC,WAAW,CAAC,IAAI;YAClB,CAAC,CAAC,WAAW,CAAA;QACjB,OAAO,IAAI,aAAK,CACZ,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,qBAAqB,SAAS,GAAG,CACrC,CAAA;IACL,CAAC;IAED;;OAEG;IACO,mBAAmB,CACzB,KAAY,EACZ,UAA2B;QAE3B,MAAM,WAAW,GAAG,UAAU,CAAC,WAAW;aACrC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,GAAG,GAAG,MAAM,GAAG,GAAG,CAAC;aACnC,IAAI,CAAC,IAAI,CAAC,CAAA;QACf,MAAM,qBAAqB,GAAG,UAAU,CAAC,qBAAqB;aACzD,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,GAAG,GAAG,MAAM,GAAG,GAAG,CAAC;aACnC,IAAI,CAAC,GAAG,CAAC,CAAA;QACd,IAAI,GAAG,GACH,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,oBACjC,UAAU,CAAC,IACf,kBAAkB,WAAW,IAAI;YACjC,cAAc,IAAI,CAAC,UAAU,CACzB,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,CAChC,KAAK,qBAAqB,GAAG,CAAA;QAClC,uFAAuF;QACvF,IAAI,UAAU,CAAC,QAAQ,IAAI,UAAU,CAAC,QAAQ,KAAK,WAAW,EAAE,CAAC;YAC7D,GAAG,IAAI,cAAc,UAAU,CAAC,QAAQ,EAAE,CAAA;QAC9C,CAAC;QACD,OAAO,IAAI,aAAK,CAAC,GAAG,CAAC,CAAA;IACzB,CAAC;IAED;;OAEG;IACO,iBAAiB,CACvB,KAAY,EACZ,gBAA0C;QAE1C,MAAM,cAAc,GAAG,iCAAe,CAAC,iBAAiB,CACpD,gBAAgB,CACnB;YACG,CAAC,CAAC,gBAAgB,CAAC,IAAI;YACvB,CAAC,CAAC,gBAAgB,CAAA;QACtB,OAAO,IAAI,aAAK,CACZ,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,qBAAqB,cAAc,GAAG,CAC1C,CAAA;IACL,CAAC;IAED;;OAEG;IACO,oBAAoB,CAAC,MAAmB;QAC9C,IAAI,CAAC,GACD,IAAI,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA;QACvE,IAAI,MAAM,CAAC,OAAO;YAAE,CAAC,IAAI,iBAAiB,GAAG,MAAM,CAAC,OAAO,CAAA;QAC3D,IAAI,MAAM,CAAC,SAAS;YAAE,CAAC,IAAI,WAAW,GAAG,MAAM,CAAC,SAAS,CAAA;QAEzD,IAAI,MAAM,CAAC,YAAY;YAAE,CAAC,IAAI,QAAQ,MAAM,CAAC,YAAY,WAAW,CAAA;QAEpE,IAAI,MAAM,CAAC,OAAO,KAAK,SAAS,IAAI,MAAM,CAAC,OAAO,KAAK,IAAI;YACvD,yCAAyC;YACzC,CAAC,IAAI,WAAW,GAAG,MAAM,CAAC,OAAO,CAAA;QACrC,IAAI,MAAM,CAAC,UAAU,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW;YACjD,2CAA2C;YAC3C,CAAC,IAAI,WAAW,CAAA;QACpB,IACI,MAAM,CAAC,WAAW,KAAK,IAAI;YAC3B,MAAM,CAAC,kBAAkB,KAAK,WAAW;YAEzC,CAAC,IAAI,mCAAmC,CAAA;QAE5C,OAAO,CAAC,CAAA;IACZ,CAAC;IAED;;OAEG;IACO,UAAU,CAAC,MAA6B;QAC9C,sCAAsC;QACtC,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA;QAEhE,IAAI,MAAM,IAAI,MAAM,KAAK,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;YAC1C,OAAO,IAAI,MAAM,MAAM,SAAS,GAAG,CAAA;QACvC,CAAC;QAED,OAAO,IAAI,SAAS,GAAG,CAAA;IAC3B,CAAC;IAED;;OAEG;IACH,kBAAkB,CACd,WAA2B,EAC3B,OAAgB;QAEhB,MAAM,IAAI,oBAAY,CAClB,sDAAsD,CACzD,CAAA;IACL,CAAC;CACJ;AAtlGD,8CAslGC", "file": "OracleQueryRunner.js", "sourcesContent": ["import { ObjectLiteral } from \"../../common/ObjectLiteral\"\nimport { TypeORMError } from \"../../error\"\nimport { QueryFailedError } from \"../../error/QueryFailedError\"\nimport { QueryRunnerAlreadyReleasedError } from \"../../error/QueryRunnerAlreadyReleasedError\"\nimport { TransactionNotStartedError } from \"../../error/TransactionNotStartedError\"\nimport { ReadStream } from \"../../platform/PlatformTools\"\nimport { BaseQueryRunner } from \"../../query-runner/BaseQueryRunner\"\nimport { QueryResult } from \"../../query-runner/QueryResult\"\nimport { QueryRunner } from \"../../query-runner/QueryRunner\"\nimport { Table } from \"../../schema-builder/table/Table\"\nimport { TableCheck } from \"../../schema-builder/table/TableCheck\"\nimport { TableColumn } from \"../../schema-builder/table/TableColumn\"\nimport { TableExclusion } from \"../../schema-builder/table/TableExclusion\"\nimport { TableForeignKey } from \"../../schema-builder/table/TableForeignKey\"\nimport { TableIndex } from \"../../schema-builder/table/TableIndex\"\nimport { TableUnique } from \"../../schema-builder/table/TableUnique\"\nimport { View } from \"../../schema-builder/view/View\"\nimport { Broadcaster } from \"../../subscriber/Broadcaster\"\nimport { BroadcasterResult } from \"../../subscriber/BroadcasterResult\"\nimport { InstanceChecker } from \"../../util/InstanceChecker\"\nimport { OrmUtils } from \"../../util/OrmUtils\"\nimport { Query } from \"../Query\"\nimport { ColumnType } from \"../types/ColumnTypes\"\nimport { IsolationLevel } from \"../types/IsolationLevel\"\nimport { MetadataTableType } from \"../types/MetadataTableType\"\nimport { ReplicationMode } from \"../types/ReplicationMode\"\nimport { OracleDriver } from \"./OracleDriver\"\n\n/**\n * Runs queries on a single oracle database connection.\n */\nexport class OracleQueryRunner extends BaseQueryRunner implements QueryRunner {\n    // -------------------------------------------------------------------------\n    // Public Implemented Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Database driver used by connection.\n     */\n    driver: OracleDriver\n\n    // -------------------------------------------------------------------------\n    // Protected Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Promise used to obtain a database connection for a first time.\n     */\n    protected databaseConnectionPromise: Promise<any>\n\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(driver: OracleDriver, mode: ReplicationMode) {\n        super()\n        this.driver = driver\n        this.connection = driver.connection\n        this.broadcaster = new Broadcaster(this)\n        this.mode = mode\n    }\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Creates/uses database connection from the connection pool to perform further operations.\n     * Returns obtained database connection.\n     */\n    connect(): Promise<any> {\n        if (this.databaseConnection)\n            return Promise.resolve(this.databaseConnection)\n\n        if (this.databaseConnectionPromise)\n            return this.databaseConnectionPromise\n\n        if (this.mode === \"slave\" && this.driver.isReplicated) {\n            this.databaseConnectionPromise = this.driver\n                .obtainSlaveConnection()\n                .then((connection) => {\n                    this.databaseConnection = connection\n                    return this.databaseConnection\n                })\n        } else {\n            // master\n            this.databaseConnectionPromise = this.driver\n                .obtainMasterConnection()\n                .then((connection) => {\n                    this.databaseConnection = connection\n                    return this.databaseConnection\n                })\n        }\n\n        return this.databaseConnectionPromise\n    }\n\n    /**\n     * Releases used database connection.\n     * You cannot use query runner methods once its released.\n     */\n    async release(): Promise<void> {\n        this.isReleased = true\n\n        if (!this.databaseConnection) {\n            return\n        }\n\n        await this.databaseConnection.close()\n    }\n\n    /**\n     * Starts transaction.\n     */\n    async startTransaction(\n        isolationLevel: IsolationLevel = \"READ COMMITTED\",\n    ): Promise<void> {\n        if (this.isReleased) throw new QueryRunnerAlreadyReleasedError()\n\n        // await this.query(\"START TRANSACTION\");\n        if (\n            isolationLevel !== \"SERIALIZABLE\" &&\n            isolationLevel !== \"READ COMMITTED\"\n        ) {\n            throw new TypeORMError(\n                `Oracle only supports SERIALIZABLE and READ COMMITTED isolation`,\n            )\n        }\n\n        this.isTransactionActive = true\n        try {\n            await this.broadcaster.broadcast(\"BeforeTransactionStart\")\n        } catch (err) {\n            this.isTransactionActive = false\n            throw err\n        }\n\n        if (this.transactionDepth === 0) {\n            await this.query(\n                \"SET TRANSACTION ISOLATION LEVEL \" + isolationLevel,\n            )\n        } else {\n            await this.query(`SAVEPOINT typeorm_${this.transactionDepth}`)\n        }\n        this.transactionDepth += 1\n\n        await this.broadcaster.broadcast(\"AfterTransactionStart\")\n    }\n\n    /**\n     * Commits transaction.\n     * Error will be thrown if transaction was not started.\n     */\n    async commitTransaction(): Promise<void> {\n        if (!this.isTransactionActive) throw new TransactionNotStartedError()\n\n        await this.broadcaster.broadcast(\"BeforeTransactionCommit\")\n\n        if (this.transactionDepth === 1) {\n            await this.query(\"COMMIT\")\n            this.isTransactionActive = false\n        }\n        this.transactionDepth -= 1\n\n        await this.broadcaster.broadcast(\"AfterTransactionCommit\")\n    }\n\n    /**\n     * Rollbacks transaction.\n     * Error will be thrown if transaction was not started.\n     */\n    async rollbackTransaction(): Promise<void> {\n        if (!this.isTransactionActive) throw new TransactionNotStartedError()\n\n        await this.broadcaster.broadcast(\"BeforeTransactionRollback\")\n\n        if (this.transactionDepth > 1) {\n            await this.query(\n                `ROLLBACK TO SAVEPOINT typeorm_${this.transactionDepth - 1}`,\n            )\n        } else {\n            await this.query(\"ROLLBACK\")\n            this.isTransactionActive = false\n        }\n        this.transactionDepth -= 1\n\n        await this.broadcaster.broadcast(\"AfterTransactionRollback\")\n    }\n\n    /**\n     * Executes a given SQL query.\n     */\n    async query(\n        query: string,\n        parameters?: any[],\n        useStructuredResult = false,\n    ): Promise<any> {\n        if (this.isReleased) throw new QueryRunnerAlreadyReleasedError()\n\n        const databaseConnection = await this.connect()\n\n        this.driver.connection.logger.logQuery(query, parameters, this)\n        await this.broadcaster.broadcast(\"BeforeQuery\", query, parameters)\n\n        const broadcasterResult = new BroadcasterResult()\n        const queryStartTime = Date.now()\n\n        try {\n            const executionOptions = {\n                autoCommit: !this.isTransactionActive,\n                outFormat: this.driver.oracle.OUT_FORMAT_OBJECT,\n            }\n\n            const raw = await databaseConnection.execute(\n                query,\n                parameters || {},\n                executionOptions,\n            )\n\n            // log slow queries if maxQueryExecution time is set\n            const maxQueryExecutionTime =\n                this.driver.options.maxQueryExecutionTime\n            const queryEndTime = Date.now()\n            const queryExecutionTime = queryEndTime - queryStartTime\n\n            this.broadcaster.broadcastAfterQueryEvent(\n                broadcasterResult,\n                query,\n                parameters,\n                true,\n                queryExecutionTime,\n                raw,\n                undefined,\n            )\n\n            if (\n                maxQueryExecutionTime &&\n                queryExecutionTime > maxQueryExecutionTime\n            )\n                this.driver.connection.logger.logQuerySlow(\n                    queryExecutionTime,\n                    query,\n                    parameters,\n                    this,\n                )\n\n            const result = new QueryResult()\n\n            result.raw =\n                raw.rows ||\n                raw.outBinds ||\n                raw.rowsAffected ||\n                raw.implicitResults\n\n            if (raw?.hasOwnProperty(\"rows\") && Array.isArray(raw.rows)) {\n                result.records = raw.rows\n            }\n\n            if (\n                raw?.hasOwnProperty(\"outBinds\") &&\n                Array.isArray(raw.outBinds)\n            ) {\n                result.records = raw.outBinds\n            }\n\n            if (\n                raw?.hasOwnProperty(\"implicitResults\") &&\n                Array.isArray(raw.implicitResults)\n            ) {\n                result.records = raw.implicitResults\n            }\n\n            if (raw?.hasOwnProperty(\"rowsAffected\")) {\n                result.affected = raw.rowsAffected\n            }\n\n            if (useStructuredResult) {\n                return result\n            } else {\n                return result.raw\n            }\n        } catch (err) {\n            this.driver.connection.logger.logQueryError(\n                err,\n                query,\n                parameters,\n                this,\n            )\n            this.broadcaster.broadcastAfterQueryEvent(\n                broadcasterResult,\n                query,\n                parameters,\n                false,\n                undefined,\n                undefined,\n                err,\n            )\n\n            throw new QueryFailedError(query, parameters, err)\n        } finally {\n            await broadcasterResult.wait()\n        }\n    }\n\n    /**\n     * Returns raw data stream.\n     */\n    async stream(\n        query: string,\n        parameters?: any[],\n        onEnd?: Function,\n        onError?: Function,\n    ): Promise<ReadStream> {\n        if (this.isReleased) {\n            throw new QueryRunnerAlreadyReleasedError()\n        }\n\n        const executionOptions = {\n            autoCommit: !this.isTransactionActive,\n            outFormat: this.driver.oracle.OUT_FORMAT_OBJECT,\n        }\n\n        const databaseConnection = await this.connect()\n\n        this.driver.connection.logger.logQuery(query, parameters, this)\n\n        try {\n            const stream = databaseConnection.queryStream(\n                query,\n                parameters,\n                executionOptions,\n            )\n            if (onEnd) {\n                stream.on(\"end\", onEnd)\n            }\n\n            if (onError) {\n                stream.on(\"error\", onError)\n            }\n\n            return stream\n        } catch (err) {\n            this.driver.connection.logger.logQueryError(\n                err,\n                query,\n                parameters,\n                this,\n            )\n            throw new QueryFailedError(query, parameters, err)\n        }\n    }\n\n    /**\n     * Returns all available database names including system databases.\n     */\n    async getDatabases(): Promise<string[]> {\n        return Promise.resolve([])\n    }\n\n    /**\n     * Returns all available schema names including system schemas.\n     * If database parameter specified, returns schemas of that database.\n     */\n    async getSchemas(database?: string): Promise<string[]> {\n        return Promise.resolve([])\n    }\n\n    /**\n     * Checks if database with the given name exist.\n     */\n    async hasDatabase(database: string): Promise<boolean> {\n        try {\n            const query = await this.query(\n                `SELECT 1 AS \"exists\" FROM global_name@\"${database}\"`,\n            )\n\n            return query.length > 0\n        } catch (e) {\n            return false\n        }\n    }\n\n    /**\n     * Loads currently using database\n     */\n    async getCurrentDatabase(): Promise<undefined> {\n        const query = await this.query(\n            `SELECT SYS_CONTEXT('USERENV','DB_NAME') AS \"db_name\" FROM dual`,\n        )\n        return query[0][\"db_name\"]\n    }\n\n    /**\n     * Checks if schema with the given name exist.\n     */\n    async hasSchema(schema: string): Promise<boolean> {\n        return Promise.resolve(false)\n    }\n\n    /**\n     * Loads currently using database schema\n     */\n    async getCurrentSchema(): Promise<string> {\n        const query = await this.query(\n            `SELECT SYS_CONTEXT('USERENV','CURRENT_SCHEMA') AS \"schema_name\" FROM dual`,\n        )\n        return query[0][\"schema_name\"]\n    }\n\n    /**\n     * Checks if table with the given name exist in the database.\n     */\n    async hasTable(tableOrName: Table | string): Promise<boolean> {\n        const { tableName } = this.driver.parseTableName(tableOrName)\n        const sql = `SELECT \"TABLE_NAME\" FROM \"USER_TABLES\" WHERE \"TABLE_NAME\" = '${tableName}'`\n        const result = await this.query(sql)\n        return result.length ? true : false\n    }\n\n    /**\n     * Checks if column with the given name exist in the given table.\n     */\n    async hasColumn(\n        tableOrName: Table | string,\n        columnName: string,\n    ): Promise<boolean> {\n        const { tableName } = this.driver.parseTableName(tableOrName)\n        const sql = `SELECT \"COLUMN_NAME\" FROM \"USER_TAB_COLS\" WHERE \"TABLE_NAME\" = '${tableName}' AND \"COLUMN_NAME\" = '${columnName}'`\n        const result = await this.query(sql)\n        return result.length ? true : false\n    }\n\n    /**\n     * Creates a new database.\n     */\n    async createDatabase(\n        database: string,\n        ifNotExist?: boolean,\n    ): Promise<void> {\n        // Even with `IF NOT EXISTS` we get:\n        //   ORA-01501: CREATE DATABASE failed\n        //   ORA-01100: database already mounted\n        if (ifNotExist) {\n            try {\n                await this.query(`CREATE DATABASE IF NOT EXISTS \"${database}\";`)\n            } catch (e) {\n                // if (e instanceof QueryFailedError) {\n                if (e.message.includes(\"ORA-01100: database already mounted\")) {\n                    return\n                }\n                // }\n\n                throw e\n            }\n        } else {\n            await this.query(`CREATE DATABASE \"${database}\"`)\n        }\n    }\n\n    /**\n     * Drops database.\n     */\n    async dropDatabase(database: string, ifExist?: boolean): Promise<void> {\n        return Promise.resolve()\n    }\n\n    /**\n     * Creates a new table schema.\n     */\n    async createSchema(\n        schemaPath: string,\n        ifNotExist?: boolean,\n    ): Promise<void> {\n        throw new TypeORMError(\n            `Schema create queries are not supported by Oracle driver.`,\n        )\n    }\n\n    /**\n     * Drops table schema.\n     */\n    async dropSchema(schemaPath: string, ifExist?: boolean): Promise<void> {\n        throw new TypeORMError(\n            `Schema drop queries are not supported by Oracle driver.`,\n        )\n    }\n\n    /**\n     * Creates a new table.\n     */\n    async createTable(\n        table: Table,\n        ifNotExist: boolean = false,\n        createForeignKeys: boolean = true,\n        createIndices: boolean = true,\n    ): Promise<void> {\n        if (ifNotExist) {\n            const isTableExist = await this.hasTable(table)\n            if (isTableExist) return Promise.resolve()\n        }\n        const upQueries: Query[] = []\n        const downQueries: Query[] = []\n\n        upQueries.push(this.createTableSql(table, createForeignKeys))\n        downQueries.push(this.dropTableSql(table))\n\n        // if createForeignKeys is true, we must drop created foreign keys in down query.\n        // createTable does not need separate method to create foreign keys, because it create fk's in the same query with table creation.\n        if (createForeignKeys)\n            table.foreignKeys.forEach((foreignKey) =>\n                downQueries.push(this.dropForeignKeySql(table, foreignKey)),\n            )\n\n        if (createIndices) {\n            table.indices.forEach((index) => {\n                // new index may be passed without name. In this case we generate index name manually.\n                if (!index.name)\n                    index.name = this.connection.namingStrategy.indexName(\n                        table,\n                        index.columnNames,\n                        index.where,\n                    )\n                upQueries.push(this.createIndexSql(table, index))\n                downQueries.push(this.dropIndexSql(index))\n            })\n        }\n\n        // if table have column with generated type, we must add the expression to the metadata table\n        const generatedColumns = table.columns.filter(\n            (column) => column.generatedType && column.asExpression,\n        )\n\n        for (const column of generatedColumns) {\n            const insertQuery = this.insertTypeormMetadataSql({\n                table: table.name,\n                type: MetadataTableType.GENERATED_COLUMN,\n                name: column.name,\n                value: column.asExpression,\n            })\n\n            const deleteQuery = this.deleteTypeormMetadataSql({\n                table: table.name,\n                type: MetadataTableType.GENERATED_COLUMN,\n                name: column.name,\n            })\n\n            upQueries.push(insertQuery)\n            downQueries.push(deleteQuery)\n        }\n\n        await this.executeQueries(upQueries, downQueries)\n    }\n\n    /**\n     * Drops the table.\n     */\n    async dropTable(\n        tableOrName: Table | string,\n        ifExist?: boolean,\n        dropForeignKeys: boolean = true,\n        dropIndices: boolean = true,\n    ): Promise<void> {\n        // It needs because if table does not exist and dropForeignKeys or dropIndices is true, we don't need\n        // to perform drop queries for foreign keys and indices.\n        if (ifExist) {\n            const isTableExist = await this.hasTable(tableOrName)\n            if (!isTableExist) return Promise.resolve()\n        }\n\n        // if dropTable called with dropForeignKeys = true, we must create foreign keys in down query.\n        const createForeignKeys: boolean = dropForeignKeys\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        const upQueries: Query[] = []\n        const downQueries: Query[] = []\n\n        if (dropIndices) {\n            table.indices.forEach((index) => {\n                upQueries.push(this.dropIndexSql(index))\n                downQueries.push(this.createIndexSql(table, index))\n            })\n        }\n\n        // if dropForeignKeys is true, we just drop the table, otherwise we also drop table foreign keys.\n        // createTable does not need separate method to create foreign keys, because it create fk's in the same query with table creation.\n        if (dropForeignKeys)\n            table.foreignKeys.forEach((foreignKey) =>\n                upQueries.push(this.dropForeignKeySql(table, foreignKey)),\n            )\n\n        upQueries.push(this.dropTableSql(table))\n        downQueries.push(this.createTableSql(table, createForeignKeys))\n\n        // if table had columns with generated type, we must remove the expression from the metadata table\n        const generatedColumns = table.columns.filter(\n            (column) => column.generatedType && column.asExpression,\n        )\n\n        for (const column of generatedColumns) {\n            const deleteQuery = this.deleteTypeormMetadataSql({\n                table: table.name,\n                type: MetadataTableType.GENERATED_COLUMN,\n                name: column.name,\n            })\n\n            const insertQuery = this.insertTypeormMetadataSql({\n                table: table.name,\n                type: MetadataTableType.GENERATED_COLUMN,\n                name: column.name,\n                value: column.asExpression,\n            })\n\n            upQueries.push(deleteQuery)\n            downQueries.push(insertQuery)\n        }\n\n        await this.executeQueries(upQueries, downQueries)\n    }\n\n    /**\n     * Creates a new view.\n     */\n    async createView(\n        view: View,\n        syncWithMetadata: boolean = false,\n    ): Promise<void> {\n        const upQueries: Query[] = []\n        const downQueries: Query[] = []\n        upQueries.push(this.createViewSql(view))\n        if (syncWithMetadata) upQueries.push(this.insertViewDefinitionSql(view))\n        downQueries.push(this.dropViewSql(view))\n        if (syncWithMetadata)\n            downQueries.push(this.deleteViewDefinitionSql(view))\n        await this.executeQueries(upQueries, downQueries)\n    }\n\n    /**\n     * Drops the view.\n     */\n    async dropView(target: View | string): Promise<void> {\n        const viewName = InstanceChecker.isView(target) ? target.name : target\n        const view = await this.getCachedView(viewName)\n\n        const upQueries: Query[] = []\n        const downQueries: Query[] = []\n        upQueries.push(this.deleteViewDefinitionSql(view))\n        upQueries.push(this.dropViewSql(view))\n        downQueries.push(this.insertViewDefinitionSql(view))\n        downQueries.push(this.createViewSql(view))\n        await this.executeQueries(upQueries, downQueries)\n    }\n\n    /**\n     * Renames the given table.\n     */\n    async renameTable(\n        oldTableOrName: Table | string,\n        newTableName: string,\n    ): Promise<void> {\n        const upQueries: Query[] = []\n        const downQueries: Query[] = []\n        const oldTable = InstanceChecker.isTable(oldTableOrName)\n            ? oldTableOrName\n            : await this.getCachedTable(oldTableOrName)\n        const newTable = oldTable.clone()\n\n        const { database: dbName, tableName: oldTableName } =\n            this.driver.parseTableName(oldTable)\n\n        newTable.name = dbName ? `${dbName}.${newTableName}` : newTableName\n\n        // rename table\n        upQueries.push(\n            new Query(\n                `ALTER TABLE ${this.escapePath(\n                    oldTable,\n                )} RENAME TO \"${newTableName}\"`,\n            ),\n        )\n        downQueries.push(\n            new Query(\n                `ALTER TABLE ${this.escapePath(\n                    newTable,\n                )} RENAME TO \"${oldTableName}\"`,\n            ),\n        )\n\n        // rename primary key constraint\n        if (\n            newTable.primaryColumns.length > 0 &&\n            !newTable.primaryColumns[0].primaryKeyConstraintName\n        ) {\n            const columnNames = newTable.primaryColumns.map(\n                (column) => column.name,\n            )\n\n            const oldPkName = this.connection.namingStrategy.primaryKeyName(\n                oldTable,\n                columnNames,\n            )\n            const newPkName = this.connection.namingStrategy.primaryKeyName(\n                newTable,\n                columnNames,\n            )\n\n            // build queries\n            upQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(\n                        newTable,\n                    )} RENAME CONSTRAINT \"${oldPkName}\" TO \"${newPkName}\"`,\n                ),\n            )\n            downQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(\n                        newTable,\n                    )} RENAME CONSTRAINT \"${newPkName}\" TO \"${oldPkName}\"`,\n                ),\n            )\n        }\n\n        // rename unique constraints\n        newTable.uniques.forEach((unique) => {\n            const oldUniqueName =\n                this.connection.namingStrategy.uniqueConstraintName(\n                    oldTable,\n                    unique.columnNames,\n                )\n\n            // Skip renaming if Unique has user defined constraint name\n            if (unique.name !== oldUniqueName) return\n\n            // build new constraint name\n            const newUniqueName =\n                this.connection.namingStrategy.uniqueConstraintName(\n                    newTable,\n                    unique.columnNames,\n                )\n\n            // build queries\n            upQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(\n                        newTable,\n                    )} RENAME CONSTRAINT \"${\n                        unique.name\n                    }\" TO \"${newUniqueName}\"`,\n                ),\n            )\n            downQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(\n                        newTable,\n                    )} RENAME CONSTRAINT \"${newUniqueName}\" TO \"${\n                        unique.name\n                    }\"`,\n                ),\n            )\n\n            // replace constraint name\n            unique.name = newUniqueName\n        })\n\n        // rename index constraints\n        newTable.indices.forEach((index) => {\n            const oldIndexName = this.connection.namingStrategy.indexName(\n                oldTable,\n                index.columnNames,\n                index.where,\n            )\n\n            // Skip renaming if Index has user defined constraint name\n            if (index.name !== oldIndexName) return\n\n            // build new constraint name\n            const newIndexName = this.connection.namingStrategy.indexName(\n                newTable,\n                index.columnNames,\n                index.where,\n            )\n\n            // build queries\n            upQueries.push(\n                new Query(\n                    `ALTER INDEX \"${index.name}\" RENAME TO \"${newIndexName}\"`,\n                ),\n            )\n            downQueries.push(\n                new Query(\n                    `ALTER INDEX \"${newIndexName}\" RENAME TO \"${index.name}\"`,\n                ),\n            )\n\n            // replace constraint name\n            index.name = newIndexName\n        })\n\n        // rename foreign key constraints\n        newTable.foreignKeys.forEach((foreignKey) => {\n            const oldForeignKeyName =\n                this.connection.namingStrategy.foreignKeyName(\n                    oldTable,\n                    foreignKey.columnNames,\n                    this.getTablePath(foreignKey),\n                    foreignKey.referencedColumnNames,\n                )\n\n            // Skip renaming if foreign key has user defined constraint name\n            if (foreignKey.name !== oldForeignKeyName) return\n\n            // build new constraint name\n            const newForeignKeyName =\n                this.connection.namingStrategy.foreignKeyName(\n                    newTable,\n                    foreignKey.columnNames,\n                    this.getTablePath(foreignKey),\n                    foreignKey.referencedColumnNames,\n                )\n\n            // build queries\n            upQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(\n                        newTable,\n                    )} RENAME CONSTRAINT \"${\n                        foreignKey.name\n                    }\" TO \"${newForeignKeyName}\"`,\n                ),\n            )\n            downQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(\n                        newTable,\n                    )} RENAME CONSTRAINT \"${newForeignKeyName}\" TO \"${\n                        foreignKey.name\n                    }\"`,\n                ),\n            )\n\n            // replace constraint name\n            foreignKey.name = newForeignKeyName\n        })\n\n        await this.executeQueries(upQueries, downQueries)\n\n        // rename old table and replace it in cached tabled;\n        oldTable.name = newTable.name\n        this.replaceCachedTable(oldTable, newTable)\n    }\n\n    /**\n     * Creates a new column from the column in the table.\n     */\n    async addColumn(\n        tableOrName: Table | string,\n        column: TableColumn,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        const clonedTable = table.clone()\n        const upQueries: Query[] = []\n        const downQueries: Query[] = []\n\n        upQueries.push(\n            new Query(\n                `ALTER TABLE ${this.escapePath(\n                    table,\n                )} ADD ${this.buildCreateColumnSql(column)}`,\n            ),\n        )\n        downQueries.push(\n            new Query(\n                `ALTER TABLE ${this.escapePath(table)} DROP COLUMN \"${\n                    column.name\n                }\"`,\n            ),\n        )\n\n        // create or update primary key constraint\n        if (column.isPrimary) {\n            const primaryColumns = clonedTable.primaryColumns\n            // if table already have primary key, me must drop it and recreate again\n            if (primaryColumns.length > 0) {\n                const pkName = primaryColumns[0].primaryKeyConstraintName\n                    ? primaryColumns[0].primaryKeyConstraintName\n                    : this.connection.namingStrategy.primaryKeyName(\n                          clonedTable,\n                          primaryColumns.map((column) => column.name),\n                      )\n\n                const columnNames = primaryColumns\n                    .map((column) => `\"${column.name}\"`)\n                    .join(\", \")\n\n                upQueries.push(\n                    new Query(\n                        `ALTER TABLE ${this.escapePath(\n                            table,\n                        )} DROP CONSTRAINT \"${pkName}\"`,\n                    ),\n                )\n                downQueries.push(\n                    new Query(\n                        `ALTER TABLE ${this.escapePath(\n                            table,\n                        )} ADD CONSTRAINT \"${pkName}\" PRIMARY KEY (${columnNames})`,\n                    ),\n                )\n            }\n\n            primaryColumns.push(column)\n            const pkName = primaryColumns[0].primaryKeyConstraintName\n                ? primaryColumns[0].primaryKeyConstraintName\n                : this.connection.namingStrategy.primaryKeyName(\n                      clonedTable,\n                      primaryColumns.map((column) => column.name),\n                  )\n\n            const columnNames = primaryColumns\n                .map((column) => `\"${column.name}\"`)\n                .join(\", \")\n\n            upQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(\n                        table,\n                    )} ADD CONSTRAINT \"${pkName}\" PRIMARY KEY (${columnNames})`,\n                ),\n            )\n            downQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(\n                        table,\n                    )} DROP CONSTRAINT \"${pkName}\"`,\n                ),\n            )\n        }\n\n        // create column index\n        const columnIndex = clonedTable.indices.find(\n            (index) =>\n                index.columnNames.length === 1 &&\n                index.columnNames[0] === column.name,\n        )\n        if (columnIndex) {\n            clonedTable.indices.splice(\n                clonedTable.indices.indexOf(columnIndex),\n                1,\n            )\n            upQueries.push(this.createIndexSql(table, columnIndex))\n            downQueries.push(this.dropIndexSql(columnIndex))\n        }\n\n        // create unique constraint\n        if (column.isUnique) {\n            const uniqueConstraint = new TableUnique({\n                name: this.connection.namingStrategy.uniqueConstraintName(\n                    table,\n                    [column.name],\n                ),\n                columnNames: [column.name],\n            })\n            clonedTable.uniques.push(uniqueConstraint)\n            upQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(table)} ADD CONSTRAINT \"${\n                        uniqueConstraint.name\n                    }\" UNIQUE (\"${column.name}\")`,\n                ),\n            )\n            downQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(table)} DROP CONSTRAINT \"${\n                        uniqueConstraint.name\n                    }\"`,\n                ),\n            )\n        }\n\n        if (column.generatedType && column.asExpression) {\n            const insertQuery = this.insertTypeormMetadataSql({\n                table: table.name,\n                type: MetadataTableType.GENERATED_COLUMN,\n                name: column.name,\n                value: column.asExpression,\n            })\n\n            const deleteQuery = this.deleteTypeormMetadataSql({\n                table: table.name,\n                type: MetadataTableType.GENERATED_COLUMN,\n                name: column.name,\n            })\n\n            upQueries.push(insertQuery)\n            downQueries.push(deleteQuery)\n        }\n\n        await this.executeQueries(upQueries, downQueries)\n\n        clonedTable.addColumn(column)\n        this.replaceCachedTable(table, clonedTable)\n    }\n\n    /**\n     * Creates a new columns from the column in the table.\n     */\n    async addColumns(\n        tableOrName: Table | string,\n        columns: TableColumn[],\n    ): Promise<void> {\n        for (const column of columns) {\n            await this.addColumn(tableOrName, column)\n        }\n    }\n\n    /**\n     * Renames column in the given table.\n     */\n    async renameColumn(\n        tableOrName: Table | string,\n        oldTableColumnOrName: TableColumn | string,\n        newTableColumnOrName: TableColumn | string,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        const oldColumn = InstanceChecker.isTableColumn(oldTableColumnOrName)\n            ? oldTableColumnOrName\n            : table.columns.find((c) => c.name === oldTableColumnOrName)\n        if (!oldColumn)\n            throw new TypeORMError(\n                `Column \"${oldTableColumnOrName}\" was not found in the ${this.escapePath(\n                    table,\n                )} table.`,\n            )\n\n        let newColumn: TableColumn | undefined = undefined\n        if (InstanceChecker.isTableColumn(newTableColumnOrName)) {\n            newColumn = newTableColumnOrName\n        } else {\n            newColumn = oldColumn.clone()\n            newColumn.name = newTableColumnOrName\n        }\n\n        await this.changeColumn(table, oldColumn, newColumn)\n    }\n\n    /**\n     * Changes a column in the table.\n     */\n    async changeColumn(\n        tableOrName: Table | string,\n        oldTableColumnOrName: TableColumn | string,\n        newColumn: TableColumn,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        let clonedTable = table.clone()\n        const upQueries: Query[] = []\n        const downQueries: Query[] = []\n\n        const oldColumn = InstanceChecker.isTableColumn(oldTableColumnOrName)\n            ? oldTableColumnOrName\n            : table.columns.find(\n                  (column) => column.name === oldTableColumnOrName,\n              )\n        if (!oldColumn)\n            throw new TypeORMError(\n                `Column \"${oldTableColumnOrName}\" was not found in the ${this.escapePath(\n                    table,\n                )} table.`,\n            )\n\n        if (\n            (newColumn.isGenerated !== oldColumn.isGenerated &&\n                newColumn.generationStrategy !== \"uuid\") ||\n            oldColumn.type !== newColumn.type ||\n            oldColumn.length !== newColumn.length ||\n            oldColumn.generatedType !== newColumn.generatedType ||\n            oldColumn.asExpression !== newColumn.asExpression\n        ) {\n            // Oracle does not support changing of IDENTITY column, so we must drop column and recreate it again.\n            // Also, we recreate column if column type changed\n            await this.dropColumn(table, oldColumn)\n            await this.addColumn(table, newColumn)\n\n            // update cloned table\n            clonedTable = table.clone()\n        } else {\n            if (newColumn.name !== oldColumn.name) {\n                // rename column\n                upQueries.push(\n                    new Query(\n                        `ALTER TABLE ${this.escapePath(table)} RENAME COLUMN \"${\n                            oldColumn.name\n                        }\" TO \"${newColumn.name}\"`,\n                    ),\n                )\n                downQueries.push(\n                    new Query(\n                        `ALTER TABLE ${this.escapePath(table)} RENAME COLUMN \"${\n                            newColumn.name\n                        }\" TO \"${oldColumn.name}\"`,\n                    ),\n                )\n\n                // rename column primary key constraint\n                if (\n                    oldColumn.isPrimary === true &&\n                    !oldColumn.primaryKeyConstraintName\n                ) {\n                    const primaryColumns = clonedTable.primaryColumns\n\n                    // build old primary constraint name\n                    const columnNames = primaryColumns.map(\n                        (column) => column.name,\n                    )\n                    const oldPkName =\n                        this.connection.namingStrategy.primaryKeyName(\n                            clonedTable,\n                            columnNames,\n                        )\n\n                    // replace old column name with new column name\n                    columnNames.splice(columnNames.indexOf(oldColumn.name), 1)\n                    columnNames.push(newColumn.name)\n\n                    // build new primary constraint name\n                    const newPkName =\n                        this.connection.namingStrategy.primaryKeyName(\n                            clonedTable,\n                            columnNames,\n                        )\n\n                    upQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} RENAME CONSTRAINT \"${oldPkName}\" TO \"${newPkName}\"`,\n                        ),\n                    )\n                    downQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} RENAME CONSTRAINT \"${newPkName}\" TO \"${oldPkName}\"`,\n                        ),\n                    )\n                }\n\n                // rename unique constraints\n                clonedTable.findColumnUniques(oldColumn).forEach((unique) => {\n                    const oldUniqueName =\n                        this.connection.namingStrategy.uniqueConstraintName(\n                            clonedTable,\n                            unique.columnNames,\n                        )\n\n                    // Skip renaming if Unique has user defined constraint name\n                    if (unique.name !== oldUniqueName) return\n\n                    // build new constraint name\n                    unique.columnNames.splice(\n                        unique.columnNames.indexOf(oldColumn.name),\n                        1,\n                    )\n                    unique.columnNames.push(newColumn.name)\n                    const newUniqueName =\n                        this.connection.namingStrategy.uniqueConstraintName(\n                            clonedTable,\n                            unique.columnNames,\n                        )\n\n                    // build queries\n                    upQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} RENAME CONSTRAINT \"${\n                                unique.name\n                            }\" TO \"${newUniqueName}\"`,\n                        ),\n                    )\n                    downQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} RENAME CONSTRAINT \"${newUniqueName}\" TO \"${\n                                unique.name\n                            }\"`,\n                        ),\n                    )\n\n                    // replace constraint name\n                    unique.name = newUniqueName\n                })\n\n                // rename index constraints\n                clonedTable.findColumnIndices(oldColumn).forEach((index) => {\n                    const oldIndexName =\n                        this.connection.namingStrategy.indexName(\n                            clonedTable,\n                            index.columnNames,\n                            index.where,\n                        )\n\n                    // Skip renaming if Index has user defined constraint name\n                    if (index.name !== oldIndexName) return\n\n                    // build new constraint name\n                    index.columnNames.splice(\n                        index.columnNames.indexOf(oldColumn.name),\n                        1,\n                    )\n                    index.columnNames.push(newColumn.name)\n                    const newIndexName =\n                        this.connection.namingStrategy.indexName(\n                            clonedTable,\n                            index.columnNames,\n                            index.where,\n                        )\n\n                    // build queries\n                    upQueries.push(\n                        new Query(\n                            `ALTER INDEX \"${index.name}\" RENAME TO \"${newIndexName}\"`,\n                        ),\n                    )\n                    downQueries.push(\n                        new Query(\n                            `ALTER INDEX \"${newIndexName}\" RENAME TO \"${index.name}\"`,\n                        ),\n                    )\n\n                    // replace constraint name\n                    index.name = newIndexName\n                })\n\n                // rename foreign key constraints\n                clonedTable\n                    .findColumnForeignKeys(oldColumn)\n                    .forEach((foreignKey) => {\n                        const foreignKeyName =\n                            this.connection.namingStrategy.foreignKeyName(\n                                clonedTable,\n                                foreignKey.columnNames,\n                                this.getTablePath(foreignKey),\n                                foreignKey.referencedColumnNames,\n                            )\n\n                        // Skip renaming if foreign key has user defined constraint name\n                        if (foreignKey.name !== foreignKeyName) return\n\n                        // build new constraint name\n                        foreignKey.columnNames.splice(\n                            foreignKey.columnNames.indexOf(oldColumn.name),\n                            1,\n                        )\n                        foreignKey.columnNames.push(newColumn.name)\n                        const newForeignKeyName =\n                            this.connection.namingStrategy.foreignKeyName(\n                                clonedTable,\n                                foreignKey.columnNames,\n                                this.getTablePath(foreignKey),\n                                foreignKey.referencedColumnNames,\n                            )\n\n                        // build queries\n                        upQueries.push(\n                            new Query(\n                                `ALTER TABLE ${this.escapePath(\n                                    table,\n                                )} RENAME CONSTRAINT \"${\n                                    foreignKey.name\n                                }\" TO \"${newForeignKeyName}\"`,\n                            ),\n                        )\n                        downQueries.push(\n                            new Query(\n                                `ALTER TABLE ${this.escapePath(\n                                    table,\n                                )} RENAME CONSTRAINT \"${newForeignKeyName}\" TO \"${\n                                    foreignKey.name\n                                }\"`,\n                            ),\n                        )\n\n                        // replace constraint name\n                        foreignKey.name = newForeignKeyName\n                    })\n\n                // rename old column in the Table object\n                const oldTableColumn = clonedTable.columns.find(\n                    (column) => column.name === oldColumn.name,\n                )\n                clonedTable.columns[\n                    clonedTable.columns.indexOf(oldTableColumn!)\n                ].name = newColumn.name\n                oldColumn.name = newColumn.name\n            }\n\n            if (this.isColumnChanged(oldColumn, newColumn, true)) {\n                let defaultUp: string = \"\"\n                let defaultDown: string = \"\"\n                let nullableUp: string = \"\"\n                let nullableDown: string = \"\"\n\n                // changing column default\n                if (\n                    newColumn.default !== null &&\n                    newColumn.default !== undefined\n                ) {\n                    defaultUp = `DEFAULT ${newColumn.default}`\n\n                    if (\n                        oldColumn.default !== null &&\n                        oldColumn.default !== undefined\n                    ) {\n                        defaultDown = `DEFAULT ${oldColumn.default}`\n                    } else {\n                        defaultDown = \"DEFAULT NULL\"\n                    }\n                } else if (\n                    oldColumn.default !== null &&\n                    oldColumn.default !== undefined\n                ) {\n                    defaultUp = \"DEFAULT NULL\"\n                    defaultDown = `DEFAULT ${oldColumn.default}`\n                }\n\n                // changing column isNullable property\n                if (newColumn.isNullable !== oldColumn.isNullable) {\n                    if (newColumn.isNullable === true) {\n                        nullableUp = \"NULL\"\n                        nullableDown = \"NOT NULL\"\n                    } else {\n                        nullableUp = \"NOT NULL\"\n                        nullableDown = \"NULL\"\n                    }\n                }\n\n                upQueries.push(\n                    new Query(\n                        `ALTER TABLE ${this.escapePath(table)} MODIFY \"${\n                            oldColumn.name\n                        }\" ${this.connection.driver.createFullType(\n                            newColumn,\n                        )} ${defaultUp} ${nullableUp}`,\n                    ),\n                )\n                downQueries.push(\n                    new Query(\n                        `ALTER TABLE ${this.escapePath(table)} MODIFY \"${\n                            oldColumn.name\n                        }\" ${this.connection.driver.createFullType(\n                            oldColumn,\n                        )} ${defaultDown} ${nullableDown}`,\n                    ),\n                )\n            }\n\n            if (newColumn.isPrimary !== oldColumn.isPrimary) {\n                const primaryColumns = clonedTable.primaryColumns\n\n                // if primary column state changed, we must always drop existed constraint.\n                if (primaryColumns.length > 0) {\n                    const pkName = primaryColumns[0].primaryKeyConstraintName\n                        ? primaryColumns[0].primaryKeyConstraintName\n                        : this.connection.namingStrategy.primaryKeyName(\n                              clonedTable,\n                              primaryColumns.map((column) => column.name),\n                          )\n\n                    const columnNames = primaryColumns\n                        .map((column) => `\"${column.name}\"`)\n                        .join(\", \")\n\n                    upQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} DROP CONSTRAINT \"${pkName}\"`,\n                        ),\n                    )\n                    downQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} ADD CONSTRAINT \"${pkName}\" PRIMARY KEY (${columnNames})`,\n                        ),\n                    )\n                }\n\n                if (newColumn.isPrimary === true) {\n                    primaryColumns.push(newColumn)\n                    // update column in table\n                    const column = clonedTable.columns.find(\n                        (column) => column.name === newColumn.name,\n                    )\n                    column!.isPrimary = true\n                    const pkName = primaryColumns[0].primaryKeyConstraintName\n                        ? primaryColumns[0].primaryKeyConstraintName\n                        : this.connection.namingStrategy.primaryKeyName(\n                              clonedTable,\n                              primaryColumns.map((column) => column.name),\n                          )\n\n                    const columnNames = primaryColumns\n                        .map((column) => `\"${column.name}\"`)\n                        .join(\", \")\n\n                    upQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} ADD CONSTRAINT \"${pkName}\" PRIMARY KEY (${columnNames})`,\n                        ),\n                    )\n                    downQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} DROP CONSTRAINT \"${pkName}\"`,\n                        ),\n                    )\n                } else {\n                    const primaryColumn = primaryColumns.find(\n                        (c) => c.name === newColumn.name,\n                    )\n                    primaryColumns.splice(\n                        primaryColumns.indexOf(primaryColumn!),\n                        1,\n                    )\n\n                    // update column in table\n                    const column = clonedTable.columns.find(\n                        (column) => column.name === newColumn.name,\n                    )\n                    column!.isPrimary = false\n\n                    // if we have another primary keys, we must recreate constraint.\n                    if (primaryColumns.length > 0) {\n                        const pkName = primaryColumns[0]\n                            .primaryKeyConstraintName\n                            ? primaryColumns[0].primaryKeyConstraintName\n                            : this.connection.namingStrategy.primaryKeyName(\n                                  clonedTable,\n                                  primaryColumns.map((column) => column.name),\n                              )\n\n                        const columnNames = primaryColumns\n                            .map((column) => `\"${column.name}\"`)\n                            .join(\", \")\n\n                        upQueries.push(\n                            new Query(\n                                `ALTER TABLE ${this.escapePath(\n                                    table,\n                                )} ADD CONSTRAINT \"${pkName}\" PRIMARY KEY (${columnNames})`,\n                            ),\n                        )\n                        downQueries.push(\n                            new Query(\n                                `ALTER TABLE ${this.escapePath(\n                                    table,\n                                )} DROP CONSTRAINT \"${pkName}\"`,\n                            ),\n                        )\n                    }\n                }\n            }\n\n            if (newColumn.isUnique !== oldColumn.isUnique) {\n                if (newColumn.isUnique === true) {\n                    const uniqueConstraint = new TableUnique({\n                        name: this.connection.namingStrategy.uniqueConstraintName(\n                            table,\n                            [newColumn.name],\n                        ),\n                        columnNames: [newColumn.name],\n                    })\n                    clonedTable.uniques.push(uniqueConstraint)\n                    upQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} ADD CONSTRAINT \"${\n                                uniqueConstraint.name\n                            }\" UNIQUE (\"${newColumn.name}\")`,\n                        ),\n                    )\n                    downQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} DROP CONSTRAINT \"${uniqueConstraint.name}\"`,\n                        ),\n                    )\n                } else {\n                    const uniqueConstraint = clonedTable.uniques.find(\n                        (unique) => {\n                            return (\n                                unique.columnNames.length === 1 &&\n                                !!unique.columnNames.find(\n                                    (columnName) =>\n                                        columnName === newColumn.name,\n                                )\n                            )\n                        },\n                    )\n                    clonedTable.uniques.splice(\n                        clonedTable.uniques.indexOf(uniqueConstraint!),\n                        1,\n                    )\n                    upQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} DROP CONSTRAINT \"${uniqueConstraint!.name}\"`,\n                        ),\n                    )\n                    downQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} ADD CONSTRAINT \"${\n                                uniqueConstraint!.name\n                            }\" UNIQUE (\"${newColumn.name}\")`,\n                        ),\n                    )\n                }\n            }\n\n            await this.executeQueries(upQueries, downQueries)\n            this.replaceCachedTable(table, clonedTable)\n        }\n    }\n\n    /**\n     * Changes a column in the table.\n     */\n    async changeColumns(\n        tableOrName: Table | string,\n        changedColumns: { newColumn: TableColumn; oldColumn: TableColumn }[],\n    ): Promise<void> {\n        for (const { oldColumn, newColumn } of changedColumns) {\n            await this.changeColumn(tableOrName, oldColumn, newColumn)\n        }\n    }\n\n    /**\n     * Drops column in the table.\n     */\n    async dropColumn(\n        tableOrName: Table | string,\n        columnOrName: TableColumn | string,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        const column = InstanceChecker.isTableColumn(columnOrName)\n            ? columnOrName\n            : table.findColumnByName(columnOrName)\n        if (!column)\n            throw new TypeORMError(\n                `Column \"${columnOrName}\" was not found in table ${this.escapePath(\n                    table,\n                )}`,\n            )\n\n        const clonedTable = table.clone()\n        const upQueries: Query[] = []\n        const downQueries: Query[] = []\n\n        // drop primary key constraint\n        if (column.isPrimary) {\n            const pkName = column.primaryKeyConstraintName\n                ? column.primaryKeyConstraintName\n                : this.connection.namingStrategy.primaryKeyName(\n                      clonedTable,\n                      clonedTable.primaryColumns.map((column) => column.name),\n                  )\n\n            const columnNames = clonedTable.primaryColumns\n                .map((primaryColumn) => `\"${primaryColumn.name}\"`)\n                .join(\", \")\n\n            upQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(\n                        clonedTable,\n                    )} DROP CONSTRAINT \"${pkName}\"`,\n                ),\n            )\n            downQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(\n                        clonedTable,\n                    )} ADD CONSTRAINT \"${pkName}\" PRIMARY KEY (${columnNames})`,\n                ),\n            )\n\n            // update column in table\n            const tableColumn = clonedTable.findColumnByName(column.name)\n            tableColumn!.isPrimary = false\n\n            // if primary key have multiple columns, we must recreate it without dropped column\n            if (clonedTable.primaryColumns.length > 0) {\n                const pkName = clonedTable.primaryColumns[0]\n                    .primaryKeyConstraintName\n                    ? clonedTable.primaryColumns[0].primaryKeyConstraintName\n                    : this.connection.namingStrategy.primaryKeyName(\n                          clonedTable,\n                          clonedTable.primaryColumns.map(\n                              (column) => column.name,\n                          ),\n                      )\n\n                const columnNames = clonedTable.primaryColumns\n                    .map((primaryColumn) => `\"${primaryColumn.name}\"`)\n                    .join(\", \")\n\n                upQueries.push(\n                    new Query(\n                        `ALTER TABLE ${this.escapePath(\n                            clonedTable,\n                        )} ADD CONSTRAINT \"${pkName}\" PRIMARY KEY (${columnNames})`,\n                    ),\n                )\n                downQueries.push(\n                    new Query(\n                        `ALTER TABLE ${this.escapePath(\n                            clonedTable,\n                        )} DROP CONSTRAINT \"${pkName}\"`,\n                    ),\n                )\n            }\n        }\n\n        // drop column index\n        const columnIndex = clonedTable.indices.find(\n            (index) =>\n                index.columnNames.length === 1 &&\n                index.columnNames[0] === column.name,\n        )\n        if (columnIndex) {\n            upQueries.push(this.dropIndexSql(columnIndex))\n            downQueries.push(this.createIndexSql(table, columnIndex))\n        }\n\n        // drop column check\n        const columnCheck = clonedTable.checks.find(\n            (check) =>\n                !!check.columnNames &&\n                check.columnNames.length === 1 &&\n                check.columnNames[0] === column.name,\n        )\n        if (columnCheck) {\n            clonedTable.checks.splice(\n                clonedTable.checks.indexOf(columnCheck),\n                1,\n            )\n            upQueries.push(this.dropCheckConstraintSql(table, columnCheck))\n            downQueries.push(this.createCheckConstraintSql(table, columnCheck))\n        }\n\n        // drop column unique\n        const columnUnique = clonedTable.uniques.find(\n            (unique) =>\n                unique.columnNames.length === 1 &&\n                unique.columnNames[0] === column.name,\n        )\n        if (columnUnique) {\n            clonedTable.uniques.splice(\n                clonedTable.uniques.indexOf(columnUnique),\n                1,\n            )\n            upQueries.push(this.dropUniqueConstraintSql(table, columnUnique))\n            downQueries.push(\n                this.createUniqueConstraintSql(table, columnUnique),\n            )\n        }\n\n        upQueries.push(\n            new Query(\n                `ALTER TABLE ${this.escapePath(table)} DROP COLUMN \"${\n                    column.name\n                }\"`,\n            ),\n        )\n        downQueries.push(\n            new Query(\n                `ALTER TABLE ${this.escapePath(\n                    table,\n                )} ADD ${this.buildCreateColumnSql(column)}`,\n            ),\n        )\n\n        if (column.generatedType && column.asExpression) {\n            const deleteQuery = this.deleteTypeormMetadataSql({\n                table: table.name,\n                type: MetadataTableType.GENERATED_COLUMN,\n                name: column.name,\n            })\n            const insertQuery = this.insertTypeormMetadataSql({\n                table: table.name,\n                type: MetadataTableType.GENERATED_COLUMN,\n                name: column.name,\n                value: column.asExpression,\n            })\n\n            upQueries.push(deleteQuery)\n            downQueries.push(insertQuery)\n        }\n\n        await this.executeQueries(upQueries, downQueries)\n\n        clonedTable.removeColumn(column)\n        this.replaceCachedTable(table, clonedTable)\n    }\n\n    /**\n     * Drops the columns in the table.\n     */\n    async dropColumns(\n        tableOrName: Table | string,\n        columns: TableColumn[] | string[],\n    ): Promise<void> {\n        for (const column of columns) {\n            await this.dropColumn(tableOrName, column)\n        }\n    }\n\n    /**\n     * Creates a new primary key.\n     */\n    async createPrimaryKey(\n        tableOrName: Table | string,\n        columnNames: string[],\n        constraintName?: string,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        const clonedTable = table.clone()\n\n        const up = this.createPrimaryKeySql(table, columnNames, constraintName)\n\n        // mark columns as primary, because dropPrimaryKeySql build constraint name from table primary column names.\n        clonedTable.columns.forEach((column) => {\n            if (columnNames.find((columnName) => columnName === column.name))\n                column.isPrimary = true\n        })\n        const down = this.dropPrimaryKeySql(clonedTable)\n\n        await this.executeQueries(up, down)\n        this.replaceCachedTable(table, clonedTable)\n    }\n\n    /**\n     * Updates composite primary keys.\n     */\n    async updatePrimaryKeys(\n        tableOrName: Table | string,\n        columns: TableColumn[],\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        const columnNames = columns.map((column) => column.name)\n        const clonedTable = table.clone()\n        const upQueries: Query[] = []\n        const downQueries: Query[] = []\n\n        // if table already have primary columns, we must drop them.\n        const primaryColumns = clonedTable.primaryColumns\n        if (primaryColumns.length > 0) {\n            const pkName = primaryColumns[0].primaryKeyConstraintName\n                ? primaryColumns[0].primaryKeyConstraintName\n                : this.connection.namingStrategy.primaryKeyName(\n                      clonedTable,\n                      primaryColumns.map((column) => column.name),\n                  )\n\n            const columnNamesString = primaryColumns\n                .map((column) => `\"${column.name}\"`)\n                .join(\", \")\n\n            upQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(\n                        table,\n                    )} DROP CONSTRAINT \"${pkName}\"`,\n                ),\n            )\n            downQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(\n                        table,\n                    )} ADD CONSTRAINT \"${pkName}\" PRIMARY KEY (${columnNamesString})`,\n                ),\n            )\n        }\n\n        // update columns in table.\n        clonedTable.columns\n            .filter((column) => columnNames.indexOf(column.name) !== -1)\n            .forEach((column) => (column.isPrimary = true))\n\n        const pkName = primaryColumns[0].primaryKeyConstraintName\n            ? primaryColumns[0].primaryKeyConstraintName\n            : this.connection.namingStrategy.primaryKeyName(\n                  clonedTable,\n                  columnNames,\n              )\n\n        const columnNamesString = columnNames\n            .map((columnName) => `\"${columnName}\"`)\n            .join(\", \")\n        upQueries.push(\n            new Query(\n                `ALTER TABLE ${this.escapePath(\n                    table,\n                )} ADD CONSTRAINT \"${pkName}\" PRIMARY KEY (${columnNamesString})`,\n            ),\n        )\n        downQueries.push(\n            new Query(\n                `ALTER TABLE ${this.escapePath(\n                    table,\n                )} DROP CONSTRAINT \"${pkName}\"`,\n            ),\n        )\n\n        await this.executeQueries(upQueries, downQueries)\n        this.replaceCachedTable(table, clonedTable)\n    }\n\n    /**\n     * Drops a primary key.\n     */\n    async dropPrimaryKey(\n        tableOrName: Table | string,\n        constraintName?: string,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        const up = this.dropPrimaryKeySql(table)\n        const down = this.createPrimaryKeySql(\n            table,\n            table.primaryColumns.map((column) => column.name),\n            constraintName,\n        )\n        await this.executeQueries(up, down)\n        table.primaryColumns.forEach((column) => {\n            column.isPrimary = false\n        })\n    }\n\n    /**\n     * Creates a new unique constraint.\n     */\n    async createUniqueConstraint(\n        tableOrName: Table | string,\n        uniqueConstraint: TableUnique,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n\n        // new unique constraint may be passed without name. In this case we generate unique name manually.\n        if (!uniqueConstraint.name)\n            uniqueConstraint.name =\n                this.connection.namingStrategy.uniqueConstraintName(\n                    table,\n                    uniqueConstraint.columnNames,\n                )\n\n        const up = this.createUniqueConstraintSql(table, uniqueConstraint)\n        const down = this.dropUniqueConstraintSql(table, uniqueConstraint)\n        await this.executeQueries(up, down)\n        table.addUniqueConstraint(uniqueConstraint)\n    }\n\n    /**\n     * Creates a new unique constraints.\n     */\n    async createUniqueConstraints(\n        tableOrName: Table | string,\n        uniqueConstraints: TableUnique[],\n    ): Promise<void> {\n        const promises = uniqueConstraints.map((uniqueConstraint) =>\n            this.createUniqueConstraint(tableOrName, uniqueConstraint),\n        )\n        await Promise.all(promises)\n    }\n\n    /**\n     * Drops an unique constraint.\n     */\n    async dropUniqueConstraint(\n        tableOrName: Table | string,\n        uniqueOrName: TableUnique | string,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        const uniqueConstraint = InstanceChecker.isTableUnique(uniqueOrName)\n            ? uniqueOrName\n            : table.uniques.find((u) => u.name === uniqueOrName)\n        if (!uniqueConstraint)\n            throw new TypeORMError(\n                `Supplied unique constraint was not found in table ${table.name}`,\n            )\n\n        const up = this.dropUniqueConstraintSql(table, uniqueConstraint)\n        const down = this.createUniqueConstraintSql(table, uniqueConstraint)\n        await this.executeQueries(up, down)\n        table.removeUniqueConstraint(uniqueConstraint)\n    }\n\n    /**\n     * Creates an unique constraints.\n     */\n    async dropUniqueConstraints(\n        tableOrName: Table | string,\n        uniqueConstraints: TableUnique[],\n    ): Promise<void> {\n        const promises = uniqueConstraints.map((uniqueConstraint) =>\n            this.dropUniqueConstraint(tableOrName, uniqueConstraint),\n        )\n        await Promise.all(promises)\n    }\n\n    /**\n     * Creates new check constraint.\n     */\n    async createCheckConstraint(\n        tableOrName: Table | string,\n        checkConstraint: TableCheck,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n\n        // new unique constraint may be passed without name. In this case we generate unique name manually.\n        if (!checkConstraint.name)\n            checkConstraint.name =\n                this.connection.namingStrategy.checkConstraintName(\n                    table,\n                    checkConstraint.expression!,\n                )\n\n        const up = this.createCheckConstraintSql(table, checkConstraint)\n        const down = this.dropCheckConstraintSql(table, checkConstraint)\n        await this.executeQueries(up, down)\n        table.addCheckConstraint(checkConstraint)\n    }\n\n    /**\n     * Creates new check constraints.\n     */\n    async createCheckConstraints(\n        tableOrName: Table | string,\n        checkConstraints: TableCheck[],\n    ): Promise<void> {\n        const promises = checkConstraints.map((checkConstraint) =>\n            this.createCheckConstraint(tableOrName, checkConstraint),\n        )\n        await Promise.all(promises)\n    }\n\n    /**\n     * Drops check constraint.\n     */\n    async dropCheckConstraint(\n        tableOrName: Table | string,\n        checkOrName: TableCheck | string,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        const checkConstraint = InstanceChecker.isTableCheck(checkOrName)\n            ? checkOrName\n            : table.checks.find((c) => c.name === checkOrName)\n        if (!checkConstraint)\n            throw new TypeORMError(\n                `Supplied check constraint was not found in table ${table.name}`,\n            )\n\n        const up = this.dropCheckConstraintSql(table, checkConstraint)\n        const down = this.createCheckConstraintSql(table, checkConstraint)\n        await this.executeQueries(up, down)\n        table.removeCheckConstraint(checkConstraint)\n    }\n\n    /**\n     * Drops check constraints.\n     */\n    async dropCheckConstraints(\n        tableOrName: Table | string,\n        checkConstraints: TableCheck[],\n    ): Promise<void> {\n        const promises = checkConstraints.map((checkConstraint) =>\n            this.dropCheckConstraint(tableOrName, checkConstraint),\n        )\n        await Promise.all(promises)\n    }\n\n    /**\n     * Creates a new exclusion constraint.\n     */\n    async createExclusionConstraint(\n        tableOrName: Table | string,\n        exclusionConstraint: TableExclusion,\n    ): Promise<void> {\n        throw new TypeORMError(`Oracle does not support exclusion constraints.`)\n    }\n\n    /**\n     * Creates a new exclusion constraints.\n     */\n    async createExclusionConstraints(\n        tableOrName: Table | string,\n        exclusionConstraints: TableExclusion[],\n    ): Promise<void> {\n        throw new TypeORMError(`Oracle does not support exclusion constraints.`)\n    }\n\n    /**\n     * Drops exclusion constraint.\n     */\n    async dropExclusionConstraint(\n        tableOrName: Table | string,\n        exclusionOrName: TableExclusion | string,\n    ): Promise<void> {\n        throw new TypeORMError(`Oracle does not support exclusion constraints.`)\n    }\n\n    /**\n     * Drops exclusion constraints.\n     */\n    async dropExclusionConstraints(\n        tableOrName: Table | string,\n        exclusionConstraints: TableExclusion[],\n    ): Promise<void> {\n        throw new TypeORMError(`Oracle does not support exclusion constraints.`)\n    }\n\n    /**\n     * Creates a new foreign key.\n     */\n    async createForeignKey(\n        tableOrName: Table | string,\n        foreignKey: TableForeignKey,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n\n        // new FK may be passed without name. In this case we generate FK name manually.\n        if (!foreignKey.name)\n            foreignKey.name = this.connection.namingStrategy.foreignKeyName(\n                table,\n                foreignKey.columnNames,\n                this.getTablePath(foreignKey),\n                foreignKey.referencedColumnNames,\n            )\n\n        const up = this.createForeignKeySql(table, foreignKey)\n        const down = this.dropForeignKeySql(table, foreignKey)\n        await this.executeQueries(up, down)\n        table.addForeignKey(foreignKey)\n    }\n\n    /**\n     * Creates a new foreign keys.\n     */\n    async createForeignKeys(\n        tableOrName: Table | string,\n        foreignKeys: TableForeignKey[],\n    ): Promise<void> {\n        const promises = foreignKeys.map((foreignKey) =>\n            this.createForeignKey(tableOrName, foreignKey),\n        )\n        await Promise.all(promises)\n    }\n\n    /**\n     * Drops a foreign key from the table.\n     */\n    async dropForeignKey(\n        tableOrName: Table | string,\n        foreignKeyOrName: TableForeignKey | string,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        const foreignKey = InstanceChecker.isTableForeignKey(foreignKeyOrName)\n            ? foreignKeyOrName\n            : table.foreignKeys.find((fk) => fk.name === foreignKeyOrName)\n        if (!foreignKey)\n            throw new TypeORMError(\n                `Supplied foreign key was not found in table ${table.name}`,\n            )\n\n        const up = this.dropForeignKeySql(table, foreignKey)\n        const down = this.createForeignKeySql(table, foreignKey)\n        await this.executeQueries(up, down)\n        table.removeForeignKey(foreignKey)\n    }\n\n    /**\n     * Drops a foreign keys from the table.\n     */\n    async dropForeignKeys(\n        tableOrName: Table | string,\n        foreignKeys: TableForeignKey[],\n    ): Promise<void> {\n        const promises = foreignKeys.map((foreignKey) =>\n            this.dropForeignKey(tableOrName, foreignKey),\n        )\n        await Promise.all(promises)\n    }\n\n    /**\n     * Creates a new index.\n     */\n    async createIndex(\n        tableOrName: Table | string,\n        index: TableIndex,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n\n        // new index may be passed without name. In this case we generate index name manually.\n        if (!index.name) index.name = this.generateIndexName(table, index)\n\n        const up = this.createIndexSql(table, index)\n        const down = this.dropIndexSql(index)\n        await this.executeQueries(up, down)\n        table.addIndex(index)\n    }\n\n    /**\n     * Creates a new indices\n     */\n    async createIndices(\n        tableOrName: Table | string,\n        indices: TableIndex[],\n    ): Promise<void> {\n        const promises = indices.map((index) =>\n            this.createIndex(tableOrName, index),\n        )\n        await Promise.all(promises)\n    }\n\n    /**\n     * Drops an index from the table.\n     */\n    async dropIndex(\n        tableOrName: Table | string,\n        indexOrName: TableIndex | string,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        const index = InstanceChecker.isTableIndex(indexOrName)\n            ? indexOrName\n            : table.indices.find((i) => i.name === indexOrName)\n        if (!index)\n            throw new TypeORMError(\n                `Supplied index ${indexOrName} was not found in table ${table.name}`,\n            )\n        // old index may be passed without name. In this case we generate index name manually.\n        if (!index.name) index.name = this.generateIndexName(table, index)\n\n        const up = this.dropIndexSql(index)\n        const down = this.createIndexSql(table, index)\n        await this.executeQueries(up, down)\n        table.removeIndex(index)\n    }\n\n    /**\n     * Drops an indices from the table.\n     */\n    async dropIndices(\n        tableOrName: Table | string,\n        indices: TableIndex[],\n    ): Promise<void> {\n        const promises = indices.map((index) =>\n            this.dropIndex(tableOrName, index),\n        )\n        await Promise.all(promises)\n    }\n\n    /**\n     * Clears all table contents.\n     * Note: this operation uses SQL's TRUNCATE query which cannot be reverted in transactions.\n     */\n    async clearTable(tableName: string): Promise<void> {\n        await this.query(`TRUNCATE TABLE ${this.escapePath(tableName)}`)\n    }\n\n    /**\n     * Removes all tables from the currently connected database.\n     */\n    async clearDatabase(): Promise<void> {\n        const isAnotherTransactionActive = this.isTransactionActive\n        if (!isAnotherTransactionActive) await this.startTransaction()\n        try {\n            // drop views\n            const dropViewsQuery = `SELECT 'DROP VIEW \"' || VIEW_NAME || '\"' AS \"query\" FROM \"USER_VIEWS\"`\n            const dropViewQueries: ObjectLiteral[] = await this.query(\n                dropViewsQuery,\n            )\n            await Promise.all(\n                dropViewQueries.map((query) => this.query(query[\"query\"])),\n            )\n\n            // drop materialized views\n            const dropMatViewsQuery = `SELECT 'DROP MATERIALIZED VIEW \"' || MVIEW_NAME || '\"' AS \"query\" FROM \"USER_MVIEWS\"`\n            const dropMatViewQueries: ObjectLiteral[] = await this.query(\n                dropMatViewsQuery,\n            )\n            await Promise.all(\n                dropMatViewQueries.map((query) => this.query(query[\"query\"])),\n            )\n\n            // drop tables\n            const dropTablesQuery = `SELECT 'DROP TABLE \"' || TABLE_NAME || '\" CASCADE CONSTRAINTS' AS \"query\" FROM \"USER_TABLES\"`\n            const dropTableQueries: ObjectLiteral[] = await this.query(\n                dropTablesQuery,\n            )\n            await Promise.all(\n                dropTableQueries.map((query) => this.query(query[\"query\"])),\n            )\n            if (!isAnotherTransactionActive) await this.commitTransaction()\n        } catch (error) {\n            try {\n                // we throw original error even if rollback thrown an error\n                if (!isAnotherTransactionActive)\n                    await this.rollbackTransaction()\n            } catch (rollbackError) {}\n            throw error\n        }\n    }\n\n    // -------------------------------------------------------------------------\n    // Protected Methods\n    // -------------------------------------------------------------------------\n\n    protected async loadViews(viewNames?: string[]): Promise<View[]> {\n        const hasTable = await this.hasTable(this.getTypeormMetadataTableName())\n        if (!hasTable) {\n            return []\n        }\n\n        if (!viewNames) {\n            viewNames = []\n        }\n\n        const currentDatabase = await this.getCurrentDatabase()\n        const currentSchema = await this.getCurrentSchema()\n\n        const viewsCondition = viewNames\n            .map((viewName) => this.driver.parseTableName(viewName))\n            .map(({ schema, tableName }) => {\n                if (!schema) {\n                    schema = this.driver.options.schema || currentSchema\n                }\n\n                return `(\"T\".\"schema\" = '${schema}' AND \"T\".\"name\" = '${tableName}')`\n            })\n            .join(\" OR \")\n\n        let query =\n            `SELECT \"T\".* FROM ${this.escapePath(\n                this.getTypeormMetadataTableName(),\n            )} \"T\" ` +\n            `INNER JOIN \"USER_OBJECTS\" \"O\" ON \"O\".\"OBJECT_NAME\" = \"T\".\"name\" AND \"O\".\"OBJECT_TYPE\" IN ( 'MATERIALIZED VIEW', 'VIEW' ) ` +\n            `WHERE \"T\".\"type\" IN ('${MetadataTableType.MATERIALIZED_VIEW}', '${MetadataTableType.VIEW}')`\n        if (viewsCondition.length > 0) query += ` AND ${viewsCondition}`\n\n        const dbViews = await this.query(query)\n        return dbViews.map((dbView: any) => {\n            const parsedName = this.driver.parseTableName(dbView[\"name\"])\n\n            const view = new View()\n            view.database =\n                parsedName.database || dbView[\"database\"] || currentDatabase\n            view.schema = parsedName.schema || dbView[\"schema\"] || currentSchema\n            view.name = parsedName.tableName\n            view.expression = dbView[\"value\"]\n            view.materialized =\n                dbView[\"type\"] === MetadataTableType.MATERIALIZED_VIEW\n            return view\n        })\n    }\n\n    /**\n     * Loads all tables (with given names) from the database and creates a Table from them.\n     */\n    protected async loadTables(tableNames?: string[]): Promise<Table[]> {\n        if (tableNames && tableNames.length === 0) {\n            return []\n        }\n\n        const dbTables: { TABLE_NAME: string; OWNER: string }[] = []\n\n        const currentSchema = await this.getCurrentSchema()\n        const currentDatabase = await this.getCurrentDatabase()\n\n        if (!tableNames) {\n            const tablesSql = `SELECT \"TABLE_NAME\", \"OWNER\" FROM \"ALL_TABLES\"`\n            dbTables.push(...(await this.query(tablesSql)))\n        } else {\n            const tablesCondition = tableNames\n                .map((tableName) => {\n                    const parts = tableName.split(\".\")\n\n                    if (parts.length >= 3) {\n                        const [, schema, name] = parts\n                        return `(\"OWNER\" = '${schema}' AND \"TABLE_NAME\" = '${name}')`\n                    } else if (parts.length === 2) {\n                        const [schema, name] = parts\n                        return `(\"OWNER\" = '${schema}' AND \"TABLE_NAME\" = '${name}')`\n                    } else if (parts.length === 1) {\n                        const [name] = parts\n                        return `(\"TABLE_NAME\" = '${name}')`\n                    } else {\n                        return `(1=0)`\n                    }\n                })\n                .join(\" OR \")\n            const tablesSql = `SELECT \"TABLE_NAME\", \"OWNER\" FROM \"ALL_TABLES\" WHERE ${tablesCondition}`\n            dbTables.push(...(await this.query(tablesSql)))\n        }\n\n        // if tables were not found in the db, no need to proceed\n        if (dbTables.length === 0) {\n            return []\n        }\n\n        // load tables, columns, indices and foreign keys\n        const columnsCondition = dbTables\n            .map(({ TABLE_NAME, OWNER }) => {\n                return `(\"C\".\"OWNER\" = '${OWNER}' AND \"C\".\"TABLE_NAME\" = '${TABLE_NAME}')`\n            })\n            .join(\" OR \")\n        const columnsSql = `SELECT * FROM \"ALL_TAB_COLS\" \"C\" WHERE (${columnsCondition})`\n\n        const indicesSql =\n            `SELECT \"C\".\"INDEX_NAME\", \"C\".\"OWNER\", \"C\".\"TABLE_NAME\", \"C\".\"UNIQUENESS\", ` +\n            `LISTAGG (\"COL\".\"COLUMN_NAME\", ',') WITHIN GROUP (ORDER BY \"COL\".\"COLUMN_NAME\") AS \"COLUMN_NAMES\" ` +\n            `FROM \"ALL_INDEXES\" \"C\" ` +\n            `INNER JOIN \"ALL_IND_COLUMNS\" \"COL\" ON \"COL\".\"INDEX_OWNER\" = \"C\".\"OWNER\" AND \"COL\".\"INDEX_NAME\" = \"C\".\"INDEX_NAME\" ` +\n            `LEFT JOIN \"ALL_CONSTRAINTS\" \"CON\" ON \"CON\".\"OWNER\" = \"C\".\"OWNER\" AND \"CON\".\"CONSTRAINT_NAME\" = \"C\".\"INDEX_NAME\" ` +\n            `WHERE (${columnsCondition}) AND \"CON\".\"CONSTRAINT_NAME\" IS NULL ` +\n            `GROUP BY \"C\".\"INDEX_NAME\", \"C\".\"OWNER\", \"C\".\"TABLE_NAME\", \"C\".\"UNIQUENESS\"`\n\n        const foreignKeysSql =\n            `SELECT \"C\".\"CONSTRAINT_NAME\", \"C\".\"OWNER\", \"C\".\"TABLE_NAME\", \"COL\".\"COLUMN_NAME\", \"REF_COL\".\"TABLE_NAME\" AS \"REFERENCED_TABLE_NAME\", ` +\n            `\"REF_COL\".\"COLUMN_NAME\" AS \"REFERENCED_COLUMN_NAME\", \"C\".\"DELETE_RULE\" AS \"ON_DELETE\" ` +\n            `FROM \"ALL_CONSTRAINTS\" \"C\" ` +\n            `INNER JOIN \"ALL_CONS_COLUMNS\" \"COL\" ON \"COL\".\"OWNER\" = \"C\".\"OWNER\" AND \"COL\".\"CONSTRAINT_NAME\" = \"C\".\"CONSTRAINT_NAME\" ` +\n            `INNER JOIN \"ALL_CONS_COLUMNS\" \"REF_COL\" ON \"REF_COL\".\"OWNER\" = \"C\".\"R_OWNER\" AND \"REF_COL\".\"CONSTRAINT_NAME\" = \"C\".\"R_CONSTRAINT_NAME\" AND \"REF_COL\".\"POSITION\" = \"COL\".\"POSITION\" ` +\n            `WHERE (${columnsCondition}) AND \"C\".\"CONSTRAINT_TYPE\" = 'R'`\n\n        const constraintsSql =\n            `SELECT \"C\".\"CONSTRAINT_NAME\", \"C\".\"CONSTRAINT_TYPE\", \"C\".\"OWNER\", \"C\".\"TABLE_NAME\", \"COL\".\"COLUMN_NAME\", \"C\".\"SEARCH_CONDITION\" ` +\n            `FROM \"ALL_CONSTRAINTS\" \"C\" ` +\n            `INNER JOIN \"ALL_CONS_COLUMNS\" \"COL\" ON \"COL\".\"OWNER\" = \"C\".\"OWNER\" AND \"COL\".\"CONSTRAINT_NAME\" = \"C\".\"CONSTRAINT_NAME\" ` +\n            `WHERE (${columnsCondition}) AND \"C\".\"CONSTRAINT_TYPE\" IN ('C', 'U', 'P') AND \"C\".\"GENERATED\" = 'USER NAME'`\n\n        const [\n            dbColumns,\n            dbIndices,\n            dbForeignKeys,\n            dbConstraints,\n        ]: ObjectLiteral[][] = await Promise.all([\n            this.query(columnsSql),\n            this.query(indicesSql),\n            this.query(foreignKeysSql),\n            this.query(constraintsSql),\n        ])\n\n        // create tables for loaded tables\n        return await Promise.all(\n            dbTables.map(async (dbTable) => {\n                const table = new Table()\n                const owner =\n                    dbTable[\"OWNER\"] === currentSchema &&\n                    (!this.driver.options.schema ||\n                        this.driver.options.schema === currentSchema)\n                        ? undefined\n                        : dbTable[\"OWNER\"]\n                table.database = currentDatabase\n                table.schema = dbTable[\"OWNER\"]\n                table.name = this.driver.buildTableName(\n                    dbTable[\"TABLE_NAME\"],\n                    owner,\n                )\n\n                // create columns from the loaded columns\n                table.columns = await Promise.all(\n                    dbColumns\n                        .filter(\n                            (dbColumn) =>\n                                dbColumn[\"OWNER\"] === dbTable[\"OWNER\"] &&\n                                dbColumn[\"TABLE_NAME\"] ===\n                                    dbTable[\"TABLE_NAME\"] &&\n                                // Filter out auto-generated virtual columns,\n                                // since TypeORM will have no info about them.\n                                !(\n                                    dbColumn[\"VIRTUAL_COLUMN\"] === \"YES\" &&\n                                    dbColumn[\"USER_GENERATED\"] === \"NO\"\n                                ),\n                        )\n                        .map(async (dbColumn) => {\n                            const columnConstraints = dbConstraints.filter(\n                                (dbConstraint) =>\n                                    dbConstraint[\"OWNER\"] ===\n                                        dbColumn[\"OWNER\"] &&\n                                    dbConstraint[\"TABLE_NAME\"] ===\n                                        dbColumn[\"TABLE_NAME\"] &&\n                                    dbConstraint[\"COLUMN_NAME\"] ===\n                                        dbColumn[\"COLUMN_NAME\"],\n                            )\n\n                            const uniqueConstraints = columnConstraints.filter(\n                                (constraint) =>\n                                    constraint[\"CONSTRAINT_TYPE\"] === \"U\",\n                            )\n                            const isConstraintComposite =\n                                uniqueConstraints.every((uniqueConstraint) => {\n                                    return dbConstraints.some(\n                                        (dbConstraint) =>\n                                            dbConstraint[\"OWNER\"] ===\n                                                dbColumn[\"OWNER\"] &&\n                                            dbConstraint[\"TABLE_NAME\"] ===\n                                                dbColumn[\"TABLE_NAME\"] &&\n                                            dbConstraint[\"COLUMN_NAME\"] !==\n                                                dbColumn[\"COLUMN_NAME\"] &&\n                                            dbConstraint[\"CONSTRAINT_NAME\"] ===\n                                                uniqueConstraint[\n                                                    \"CONSTRAINT_NAME\"\n                                                ] &&\n                                            dbConstraint[\"CONSTRAINT_TYPE\"] ===\n                                                \"U\",\n                                    )\n                                })\n\n                            const tableColumn = new TableColumn()\n                            tableColumn.name = dbColumn[\"COLUMN_NAME\"]\n                            tableColumn.type =\n                                dbColumn[\"DATA_TYPE\"].toLowerCase()\n                            if (tableColumn.type.indexOf(\"(\") !== -1)\n                                tableColumn.type = tableColumn.type.replace(\n                                    /\\([0-9]*\\)/,\n                                    \"\",\n                                )\n\n                            // check only columns that have length property\n                            if (\n                                this.driver.withLengthColumnTypes.indexOf(\n                                    tableColumn.type as ColumnType,\n                                ) !== -1\n                            ) {\n                                const length =\n                                    tableColumn.type === \"raw\"\n                                        ? dbColumn[\"DATA_LENGTH\"]\n                                        : dbColumn[\"CHAR_COL_DECL_LENGTH\"]\n                                tableColumn.length =\n                                    length &&\n                                    !this.isDefaultColumnLength(\n                                        table,\n                                        tableColumn,\n                                        length,\n                                    )\n                                        ? length.toString()\n                                        : \"\"\n                            }\n\n                            if (\n                                tableColumn.type === \"number\" ||\n                                tableColumn.type === \"float\"\n                            ) {\n                                if (\n                                    dbColumn[\"DATA_PRECISION\"] !== null &&\n                                    !this.isDefaultColumnPrecision(\n                                        table,\n                                        tableColumn,\n                                        dbColumn[\"DATA_PRECISION\"],\n                                    )\n                                )\n                                    tableColumn.precision =\n                                        dbColumn[\"DATA_PRECISION\"]\n                                if (\n                                    dbColumn[\"DATA_SCALE\"] !== null &&\n                                    !this.isDefaultColumnScale(\n                                        table,\n                                        tableColumn,\n                                        dbColumn[\"DATA_SCALE\"],\n                                    )\n                                )\n                                    tableColumn.scale = dbColumn[\"DATA_SCALE\"]\n                            } else if (\n                                (tableColumn.type === \"timestamp\" ||\n                                    tableColumn.type ===\n                                        \"timestamp with time zone\" ||\n                                    tableColumn.type ===\n                                        \"timestamp with local time zone\") &&\n                                dbColumn[\"DATA_SCALE\"] !== null\n                            ) {\n                                tableColumn.precision =\n                                    !this.isDefaultColumnPrecision(\n                                        table,\n                                        tableColumn,\n                                        dbColumn[\"DATA_SCALE\"],\n                                    )\n                                        ? dbColumn[\"DATA_SCALE\"]\n                                        : undefined\n                            }\n\n                            tableColumn.default =\n                                dbColumn[\"DATA_DEFAULT\"] !== null &&\n                                dbColumn[\"DATA_DEFAULT\"] !== undefined &&\n                                dbColumn[\"VIRTUAL_COLUMN\"] === \"NO\" &&\n                                dbColumn[\"DATA_DEFAULT\"].trim() !== \"NULL\"\n                                    ? (tableColumn.default =\n                                          dbColumn[\"DATA_DEFAULT\"].trim())\n                                    : undefined\n\n                            const primaryConstraint = columnConstraints.find(\n                                (constraint) =>\n                                    constraint[\"CONSTRAINT_TYPE\"] === \"P\",\n                            )\n                            if (primaryConstraint) {\n                                tableColumn.isPrimary = true\n                                // find another columns involved in primary key constraint\n                                const anotherPrimaryConstraints =\n                                    dbConstraints.filter(\n                                        (constraint) =>\n                                            constraint[\"OWNER\"] ===\n                                                dbColumn[\"OWNER\"] &&\n                                            constraint[\"TABLE_NAME\"] ===\n                                                dbColumn[\"TABLE_NAME\"] &&\n                                            constraint[\"COLUMN_NAME\"] !==\n                                                dbColumn[\"COLUMN_NAME\"] &&\n                                            constraint[\"CONSTRAINT_TYPE\"] ===\n                                                \"P\",\n                                    )\n\n                                // collect all column names\n                                const columnNames =\n                                    anotherPrimaryConstraints.map(\n                                        (constraint) =>\n                                            constraint[\"COLUMN_NAME\"],\n                                    )\n                                columnNames.push(dbColumn[\"COLUMN_NAME\"])\n\n                                // build default primary key constraint name\n                                const pkName =\n                                    this.connection.namingStrategy.primaryKeyName(\n                                        table,\n                                        columnNames,\n                                    )\n\n                                // if primary key has user-defined constraint name, write it in table column\n                                if (\n                                    primaryConstraint[\"CONSTRAINT_NAME\"] !==\n                                    pkName\n                                ) {\n                                    tableColumn.primaryKeyConstraintName =\n                                        primaryConstraint[\"CONSTRAINT_NAME\"]\n                                }\n                            }\n\n                            tableColumn.isNullable =\n                                dbColumn[\"NULLABLE\"] === \"Y\"\n                            tableColumn.isUnique =\n                                uniqueConstraints.length > 0 &&\n                                !isConstraintComposite\n                            tableColumn.isGenerated =\n                                dbColumn[\"IDENTITY_COLUMN\"] === \"YES\"\n                            if (tableColumn.isGenerated) {\n                                tableColumn.generationStrategy = \"increment\"\n                                tableColumn.default = undefined\n                            }\n                            tableColumn.comment = \"\" // todo\n\n                            if (dbColumn[\"VIRTUAL_COLUMN\"] === \"YES\") {\n                                tableColumn.generatedType = \"VIRTUAL\"\n\n                                const asExpressionQuery =\n                                    this.selectTypeormMetadataSql({\n                                        table: dbTable[\"TABLE_NAME\"],\n                                        type: MetadataTableType.GENERATED_COLUMN,\n                                        name: tableColumn.name,\n                                    })\n\n                                const results = await this.query(\n                                    asExpressionQuery.query,\n                                    asExpressionQuery.parameters,\n                                )\n                                if (results[0] && results[0].value) {\n                                    tableColumn.asExpression = results[0].value\n                                } else {\n                                    tableColumn.asExpression = \"\"\n                                }\n                            }\n\n                            return tableColumn\n                        }),\n                )\n\n                // find unique constraints of table, group them by constraint name and build TableUnique.\n                const tableUniqueConstraints = OrmUtils.uniq(\n                    dbConstraints.filter((dbConstraint) => {\n                        return (\n                            dbConstraint[\"TABLE_NAME\"] ===\n                                dbTable[\"TABLE_NAME\"] &&\n                            dbConstraint[\"OWNER\"] === dbTable[\"OWNER\"] &&\n                            dbConstraint[\"CONSTRAINT_TYPE\"] === \"U\"\n                        )\n                    }),\n                    (dbConstraint) => dbConstraint[\"CONSTRAINT_NAME\"],\n                )\n\n                table.uniques = tableUniqueConstraints.map((constraint) => {\n                    const uniques = dbConstraints.filter(\n                        (dbC) =>\n                            dbC[\"CONSTRAINT_NAME\"] ===\n                            constraint[\"CONSTRAINT_NAME\"],\n                    )\n                    return new TableUnique({\n                        name: constraint[\"CONSTRAINT_NAME\"],\n                        columnNames: uniques.map((u) => u[\"COLUMN_NAME\"]),\n                    })\n                })\n\n                // find check constraints of table, group them by constraint name and build TableCheck.\n                const tableCheckConstraints = OrmUtils.uniq(\n                    dbConstraints.filter((dbConstraint) => {\n                        return (\n                            dbConstraint[\"TABLE_NAME\"] ===\n                                dbTable[\"TABLE_NAME\"] &&\n                            dbConstraint[\"OWNER\"] === dbTable[\"OWNER\"] &&\n                            dbConstraint[\"CONSTRAINT_TYPE\"] === \"C\"\n                        )\n                    }),\n                    (dbConstraint) => dbConstraint[\"CONSTRAINT_NAME\"],\n                )\n\n                table.checks = tableCheckConstraints.map((constraint) => {\n                    const checks = dbConstraints.filter(\n                        (dbC) =>\n                            dbC[\"TABLE_NAME\"] === constraint[\"TABLE_NAME\"] &&\n                            dbC[\"OWNER\"] === constraint[\"OWNER\"] &&\n                            dbC[\"CONSTRAINT_NAME\"] ===\n                                constraint[\"CONSTRAINT_NAME\"],\n                    )\n                    return new TableCheck({\n                        name: constraint[\"CONSTRAINT_NAME\"],\n                        columnNames: checks.map((c) => c[\"COLUMN_NAME\"]),\n                        expression: constraint[\"SEARCH_CONDITION\"],\n                    })\n                })\n\n                // find foreign key constraints of table, group them by constraint name and build TableForeignKey.\n                const tableForeignKeyConstraints = OrmUtils.uniq(\n                    dbForeignKeys.filter(\n                        (dbForeignKey) =>\n                            dbForeignKey[\"OWNER\"] === dbTable[\"OWNER\"] &&\n                            dbForeignKey[\"TABLE_NAME\"] ===\n                                dbTable[\"TABLE_NAME\"],\n                    ),\n                    (dbForeignKey) => dbForeignKey[\"CONSTRAINT_NAME\"],\n                )\n\n                table.foreignKeys = tableForeignKeyConstraints.map(\n                    (dbForeignKey) => {\n                        const foreignKeys = dbForeignKeys.filter(\n                            (dbFk) =>\n                                dbFk[\"TABLE_NAME\"] ===\n                                    dbForeignKey[\"TABLE_NAME\"] &&\n                                dbFk[\"OWNER\"] === dbForeignKey[\"OWNER\"] &&\n                                dbFk[\"CONSTRAINT_NAME\"] ===\n                                    dbForeignKey[\"CONSTRAINT_NAME\"],\n                        )\n                        return new TableForeignKey({\n                            name: dbForeignKey[\"CONSTRAINT_NAME\"],\n                            columnNames: foreignKeys.map(\n                                (dbFk) => dbFk[\"COLUMN_NAME\"],\n                            ),\n                            referencedDatabase: table.database,\n                            referencedSchema: dbForeignKey[\"OWNER\"],\n                            referencedTableName:\n                                dbForeignKey[\"REFERENCED_TABLE_NAME\"],\n                            referencedColumnNames: foreignKeys.map(\n                                (dbFk) => dbFk[\"REFERENCED_COLUMN_NAME\"],\n                            ),\n                            onDelete: dbForeignKey[\"ON_DELETE\"],\n                            onUpdate: \"NO ACTION\", // Oracle does not have onUpdate option in FK's, but we need it for proper synchronization\n                        })\n                    },\n                )\n\n                // Attempt to map auto-generated virtual columns to their\n                // referenced columns, through its 'DATA_DEFAULT' property.\n                //\n                // An example of this happening is when a column of type\n                // TIMESTAMP WITH TIME ZONE is indexed. Oracle will create a\n                // virtual column of type TIMESTAMP with a default value of\n                // SYS_EXTRACT_UTC(<column>).\n                const autoGenVirtualDbColumns = dbColumns\n                    .filter(\n                        (dbColumn) =>\n                            dbColumn[\"OWNER\"] === dbTable[\"OWNER\"] &&\n                            dbColumn[\"TABLE_NAME\"] === dbTable[\"TABLE_NAME\"] &&\n                            dbColumn[\"VIRTUAL_COLUMN\"] === \"YES\" &&\n                            dbColumn[\"USER_GENERATED\"] === \"NO\",\n                    )\n                    .reduce((acc, x) => {\n                        const referencedDbColumn = dbColumns.find((dbColumn) =>\n                            x[\"DATA_DEFAULT\"].includes(dbColumn[\"COLUMN_NAME\"]),\n                        )\n\n                        if (!referencedDbColumn) return acc\n\n                        return {\n                            ...acc,\n                            [x[\"COLUMN_NAME\"]]:\n                                referencedDbColumn[\"COLUMN_NAME\"],\n                        }\n                    }, {})\n\n                // create TableIndex objects from the loaded indices\n                table.indices = dbIndices\n                    .filter(\n                        (dbIndex) =>\n                            dbIndex[\"TABLE_NAME\"] === dbTable[\"TABLE_NAME\"] &&\n                            dbIndex[\"OWNER\"] === dbTable[\"OWNER\"],\n                    )\n                    .map((dbIndex) => {\n                        //\n                        const columnNames = dbIndex[\"COLUMN_NAMES\"]\n                            .split(\",\")\n                            .map(\n                                (\n                                    columnName: keyof typeof autoGenVirtualDbColumns,\n                                ) =>\n                                    autoGenVirtualDbColumns[columnName] ??\n                                    columnName,\n                            )\n\n                        return new TableIndex({\n                            name: dbIndex[\"INDEX_NAME\"],\n                            columnNames,\n                            isUnique: dbIndex[\"UNIQUENESS\"] === \"UNIQUE\",\n                        })\n                    })\n\n                return table\n            }),\n        )\n    }\n\n    /**\n     * Builds and returns SQL for create table.\n     */\n    protected createTableSql(table: Table, createForeignKeys?: boolean): Query {\n        const columnDefinitions = table.columns\n            .map((column) => this.buildCreateColumnSql(column))\n            .join(\", \")\n        let sql = `CREATE TABLE ${this.escapePath(table)} (${columnDefinitions}`\n\n        table.columns\n            .filter((column) => column.isUnique)\n            .forEach((column) => {\n                const isUniqueExist = table.uniques.some(\n                    (unique) =>\n                        unique.columnNames.length === 1 &&\n                        unique.columnNames[0] === column.name,\n                )\n                if (!isUniqueExist)\n                    table.uniques.push(\n                        new TableUnique({\n                            name: this.connection.namingStrategy.uniqueConstraintName(\n                                table,\n                                [column.name],\n                            ),\n                            columnNames: [column.name],\n                        }),\n                    )\n            })\n\n        if (table.uniques.length > 0) {\n            const uniquesSql = table.uniques\n                .map((unique) => {\n                    const uniqueName = unique.name\n                        ? unique.name\n                        : this.connection.namingStrategy.uniqueConstraintName(\n                              table,\n                              unique.columnNames,\n                          )\n                    const columnNames = unique.columnNames\n                        .map((columnName) => `\"${columnName}\"`)\n                        .join(\", \")\n                    return `CONSTRAINT \"${uniqueName}\" UNIQUE (${columnNames})`\n                })\n                .join(\", \")\n\n            sql += `, ${uniquesSql}`\n        }\n\n        if (table.checks.length > 0) {\n            const checksSql = table.checks\n                .map((check) => {\n                    const checkName = check.name\n                        ? check.name\n                        : this.connection.namingStrategy.checkConstraintName(\n                              table,\n                              check.expression!,\n                          )\n                    return `CONSTRAINT \"${checkName}\" CHECK (${check.expression})`\n                })\n                .join(\", \")\n\n            sql += `, ${checksSql}`\n        }\n\n        if (table.foreignKeys.length > 0 && createForeignKeys) {\n            const foreignKeysSql = table.foreignKeys\n                .map((fk) => {\n                    const columnNames = fk.columnNames\n                        .map((columnName) => `\"${columnName}\"`)\n                        .join(\", \")\n                    if (!fk.name)\n                        fk.name = this.connection.namingStrategy.foreignKeyName(\n                            table,\n                            fk.columnNames,\n                            this.getTablePath(fk),\n                            fk.referencedColumnNames,\n                        )\n                    const referencedColumnNames = fk.referencedColumnNames\n                        .map((columnName) => `\"${columnName}\"`)\n                        .join(\", \")\n                    let constraint = `CONSTRAINT \"${\n                        fk.name\n                    }\" FOREIGN KEY (${columnNames}) REFERENCES ${this.escapePath(\n                        this.getTablePath(fk),\n                    )} (${referencedColumnNames})`\n                    if (fk.onDelete && fk.onDelete !== \"NO ACTION\") {\n                        // Oracle does not support NO ACTION, but we set NO ACTION by default in EntityMetadata\n                        constraint += ` ON DELETE ${fk.onDelete}`\n                    }\n                    return constraint\n                })\n                .join(\", \")\n\n            sql += `, ${foreignKeysSql}`\n        }\n\n        const primaryColumns = table.columns.filter(\n            (column) => column.isPrimary,\n        )\n        if (primaryColumns.length > 0) {\n            const primaryKeyName = primaryColumns[0].primaryKeyConstraintName\n                ? primaryColumns[0].primaryKeyConstraintName\n                : this.connection.namingStrategy.primaryKeyName(\n                      table,\n                      primaryColumns.map((column) => column.name),\n                  )\n\n            const columnNames = primaryColumns\n                .map((column) => `\"${column.name}\"`)\n                .join(\", \")\n            sql += `, CONSTRAINT \"${primaryKeyName}\" PRIMARY KEY (${columnNames})`\n        }\n\n        sql += `)`\n\n        return new Query(sql)\n    }\n\n    /**\n     * Builds drop table sql.\n     */\n    protected dropTableSql(\n        tableOrName: Table | string,\n        ifExist?: boolean,\n    ): Query {\n        const query = ifExist\n            ? `DROP TABLE IF EXISTS ${this.escapePath(tableOrName)}`\n            : `DROP TABLE ${this.escapePath(tableOrName)}`\n        return new Query(query)\n    }\n\n    protected createViewSql(view: View): Query {\n        const materializedClause = view.materialized ? \"MATERIALIZED \" : \"\"\n        if (typeof view.expression === \"string\") {\n            return new Query(\n                `CREATE ${materializedClause}VIEW ${this.escapePath(view)} AS ${\n                    view.expression\n                }`,\n            )\n        } else {\n            return new Query(\n                `CREATE ${materializedClause}VIEW ${this.escapePath(\n                    view,\n                )} AS ${view.expression(this.connection).getQuery()}`,\n            )\n        }\n    }\n\n    protected insertViewDefinitionSql(view: View): Query {\n        const expression =\n            typeof view.expression === \"string\"\n                ? view.expression.trim()\n                : view.expression(this.connection).getQuery()\n        const type = view.materialized\n            ? MetadataTableType.MATERIALIZED_VIEW\n            : MetadataTableType.VIEW\n        const { schema, tableName } = this.driver.parseTableName(view)\n        return this.insertTypeormMetadataSql({\n            type: type,\n            name: tableName,\n            schema: schema,\n            value: expression,\n        })\n    }\n\n    /**\n     * Builds drop view sql.\n     */\n    protected dropViewSql(view: View): Query {\n        const materializedClause = view.materialized ? \"MATERIALIZED \" : \"\"\n        return new Query(\n            `DROP ${materializedClause}VIEW ${this.escapePath(view)}`,\n        )\n    }\n\n    /**\n     * Builds remove view sql.\n     */\n    protected deleteViewDefinitionSql(view: View): Query {\n        const type = view.materialized\n            ? MetadataTableType.MATERIALIZED_VIEW\n            : MetadataTableType.VIEW\n        return this.deleteTypeormMetadataSql({ type, name: view.name })\n    }\n\n    /**\n     * Builds create index sql.\n     */\n    protected createIndexSql(table: Table, index: TableIndex): Query {\n        const columns = index.columnNames\n            .map((columnName) => `\"${columnName}\"`)\n            .join(\", \")\n        return new Query(\n            `CREATE ${index.isUnique ? \"UNIQUE \" : \"\"}INDEX \"${\n                index.name\n            }\" ON ${this.escapePath(table)} (${columns})`,\n        )\n    }\n\n    /**\n     * Builds drop index sql.\n     */\n    protected dropIndexSql(indexOrName: TableIndex | string): Query {\n        const indexName = InstanceChecker.isTableIndex(indexOrName)\n            ? indexOrName.name\n            : indexOrName\n        return new Query(`DROP INDEX \"${indexName}\"`)\n    }\n\n    /**\n     * Builds create primary key sql.\n     */\n    protected createPrimaryKeySql(\n        table: Table,\n        columnNames: string[],\n        constraintName?: string,\n    ): Query {\n        const primaryKeyName = constraintName\n            ? constraintName\n            : this.connection.namingStrategy.primaryKeyName(table, columnNames)\n\n        const columnNamesString = columnNames\n            .map((columnName) => `\"${columnName}\"`)\n            .join(\", \")\n\n        return new Query(\n            `ALTER TABLE ${this.escapePath(\n                table,\n            )} ADD CONSTRAINT \"${primaryKeyName}\" PRIMARY KEY (${columnNamesString})`,\n        )\n    }\n\n    /**\n     * Builds drop primary key sql.\n     */\n    protected dropPrimaryKeySql(table: Table): Query {\n        if (!table.primaryColumns.length)\n            throw new TypeORMError(`Table ${table} has no primary keys.`)\n\n        const columnNames = table.primaryColumns.map((column) => column.name)\n        const constraintName = table.primaryColumns[0].primaryKeyConstraintName\n        const primaryKeyName = constraintName\n            ? constraintName\n            : this.connection.namingStrategy.primaryKeyName(table, columnNames)\n\n        return new Query(\n            `ALTER TABLE ${this.escapePath(\n                table,\n            )} DROP CONSTRAINT \"${primaryKeyName}\"`,\n        )\n    }\n\n    /**\n     * Builds create unique constraint sql.\n     */\n    protected createUniqueConstraintSql(\n        table: Table,\n        uniqueConstraint: TableUnique,\n    ): Query {\n        const columnNames = uniqueConstraint.columnNames\n            .map((column) => `\"` + column + `\"`)\n            .join(\", \")\n        return new Query(\n            `ALTER TABLE ${this.escapePath(table)} ADD CONSTRAINT \"${\n                uniqueConstraint.name\n            }\" UNIQUE (${columnNames})`,\n        )\n    }\n\n    /**\n     * Builds drop unique constraint sql.\n     */\n    protected dropUniqueConstraintSql(\n        table: Table,\n        uniqueOrName: TableUnique | string,\n    ): Query {\n        const uniqueName = InstanceChecker.isTableUnique(uniqueOrName)\n            ? uniqueOrName.name\n            : uniqueOrName\n        return new Query(\n            `ALTER TABLE ${this.escapePath(\n                table,\n            )} DROP CONSTRAINT \"${uniqueName}\"`,\n        )\n    }\n\n    /**\n     * Builds create check constraint sql.\n     */\n    protected createCheckConstraintSql(\n        table: Table,\n        checkConstraint: TableCheck,\n    ): Query {\n        return new Query(\n            `ALTER TABLE ${this.escapePath(table)} ADD CONSTRAINT \"${\n                checkConstraint.name\n            }\" CHECK (${checkConstraint.expression})`,\n        )\n    }\n\n    /**\n     * Builds drop check constraint sql.\n     */\n    protected dropCheckConstraintSql(\n        table: Table,\n        checkOrName: TableCheck | string,\n    ): Query {\n        const checkName = InstanceChecker.isTableCheck(checkOrName)\n            ? checkOrName.name\n            : checkOrName\n        return new Query(\n            `ALTER TABLE ${this.escapePath(\n                table,\n            )} DROP CONSTRAINT \"${checkName}\"`,\n        )\n    }\n\n    /**\n     * Builds create foreign key sql.\n     */\n    protected createForeignKeySql(\n        table: Table,\n        foreignKey: TableForeignKey,\n    ): Query {\n        const columnNames = foreignKey.columnNames\n            .map((column) => `\"` + column + `\"`)\n            .join(\", \")\n        const referencedColumnNames = foreignKey.referencedColumnNames\n            .map((column) => `\"` + column + `\"`)\n            .join(\",\")\n        let sql =\n            `ALTER TABLE ${this.escapePath(table)} ADD CONSTRAINT \"${\n                foreignKey.name\n            }\" FOREIGN KEY (${columnNames}) ` +\n            `REFERENCES ${this.escapePath(\n                this.getTablePath(foreignKey),\n            )} (${referencedColumnNames})`\n        // Oracle does not support NO ACTION, but we set NO ACTION by default in EntityMetadata\n        if (foreignKey.onDelete && foreignKey.onDelete !== \"NO ACTION\") {\n            sql += ` ON DELETE ${foreignKey.onDelete}`\n        }\n        return new Query(sql)\n    }\n\n    /**\n     * Builds drop foreign key sql.\n     */\n    protected dropForeignKeySql(\n        table: Table,\n        foreignKeyOrName: TableForeignKey | string,\n    ): Query {\n        const foreignKeyName = InstanceChecker.isTableForeignKey(\n            foreignKeyOrName,\n        )\n            ? foreignKeyOrName.name\n            : foreignKeyOrName\n        return new Query(\n            `ALTER TABLE ${this.escapePath(\n                table,\n            )} DROP CONSTRAINT \"${foreignKeyName}\"`,\n        )\n    }\n\n    /**\n     * Builds a query for create column.\n     */\n    protected buildCreateColumnSql(column: TableColumn) {\n        let c =\n            `\"${column.name}\" ` + this.connection.driver.createFullType(column)\n        if (column.charset) c += \" CHARACTER SET \" + column.charset\n        if (column.collation) c += \" COLLATE \" + column.collation\n\n        if (column.asExpression) c += ` AS (${column.asExpression}) VIRTUAL`\n\n        if (column.default !== undefined && column.default !== null)\n            // DEFAULT must be placed before NOT NULL\n            c += \" DEFAULT \" + column.default\n        if (column.isNullable !== true && !column.isGenerated)\n            // NOT NULL is not supported with GENERATED\n            c += \" NOT NULL\"\n        if (\n            column.isGenerated === true &&\n            column.generationStrategy === \"increment\"\n        )\n            c += \" GENERATED BY DEFAULT AS IDENTITY\"\n\n        return c\n    }\n\n    /**\n     * Escapes given table or view path.\n     */\n    protected escapePath(target: Table | View | string): string {\n        // Ignore database when escaping paths\n        const { schema, tableName } = this.driver.parseTableName(target)\n\n        if (schema && schema !== this.driver.schema) {\n            return `\"${schema}\".\"${tableName}\"`\n        }\n\n        return `\"${tableName}\"`\n    }\n\n    /**\n     * Change table comment.\n     */\n    changeTableComment(\n        tableOrName: Table | string,\n        comment?: string,\n    ): Promise<void> {\n        throw new TypeORMError(\n            `oracle driver does not support change table comment.`,\n        )\n    }\n}\n"], "sourceRoot": "../.."}