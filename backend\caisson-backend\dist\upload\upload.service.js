"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UploadService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const media_entity_1 = require("../entities/media.entity");
const fs = require("fs");
const path = require("path");
let UploadService = class UploadService {
    mediaModel;
    configService;
    constructor(mediaModel, configService) {
        this.mediaModel = mediaModel;
        this.configService = configService;
    }
    async uploadFile(file, title, description) {
        if (!file) {
            throw new common_1.BadRequestException('No file provided');
        }
        let mediaType;
        if (file.mimetype.startsWith('image/')) {
            mediaType = media_entity_1.MediaType.IMAGE;
        }
        else if (file.mimetype.startsWith('video/')) {
            mediaType = media_entity_1.MediaType.VIDEO;
        }
        else {
            mediaType = media_entity_1.MediaType.DOCUMENT;
        }
        const baseUrl = `${this.configService.get('FRONTEND_URL')}:${this.configService.get('PORT')}`;
        const fileUrl = `${baseUrl}/uploads/${file.filename}`;
        const media = new this.mediaModel({
            title: title || file.originalname,
            description,
            filename: file.filename,
            originalName: file.originalname,
            mimeType: file.mimetype,
            size: file.size,
            url: fileUrl,
            type: mediaType,
        });
        return media.save();
    }
    async uploadMultipleFiles(files, title, description) {
        const uploadPromises = files.map((file, index) => this.uploadFile(file, `${title || 'File'} ${index + 1}`, description));
        return Promise.all(uploadPromises);
    }
    async deleteFile(id) {
        const media = await this.mediaModel.findById(id).exec();
        if (!media) {
            throw new common_1.BadRequestException('File not found');
        }
        const filePath = path.join(this.configService.get('UPLOAD_DEST') || './uploads', media.filename);
        try {
            if (fs.existsSync(filePath)) {
                fs.unlinkSync(filePath);
            }
        }
        catch (error) {
            console.error('Error deleting file:', error);
        }
        await this.mediaModel.findByIdAndDelete(id).exec();
    }
    async findAll() {
        return this.mediaModel.find().sort({ createdAt: -1 }).exec();
    }
    async findOne(id) {
        const media = await this.mediaModel.findById(id).exec();
        if (!media) {
            throw new common_1.BadRequestException('File not found');
        }
        return media;
    }
    validateFile(file) {
        const maxSize = this.configService.get('MAX_FILE_SIZE') || 10485760;
        if (file.size > maxSize) {
            throw new common_1.BadRequestException('File size too large');
        }
        const allowedMimeTypes = [
            'image/jpeg',
            'image/png',
            'image/gif',
            'image/webp',
            'video/mp4',
            'video/webm',
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        ];
        if (!allowedMimeTypes.includes(file.mimetype)) {
            throw new common_1.BadRequestException('File type not allowed');
        }
    }
};
exports.UploadService = UploadService;
exports.UploadService = UploadService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(media_entity_1.Media.name)),
    __metadata("design:paramtypes", [mongoose_2.Model,
        config_1.ConfigService])
], UploadService);
//# sourceMappingURL=upload.service.js.map