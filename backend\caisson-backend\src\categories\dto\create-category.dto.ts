import { IsString, IsNotEmpty, IsOptional } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateCategoryDto {
  @ApiProperty({ example: 'Coffrets tunnel pour volets roulants' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({ example: 'coffrets' })
  @IsString()
  @IsNotEmpty()
  slug: string;

  @ApiPropertyOptional({
    example: 'Coffrets tunnel pour volets roulants et autres applications',
  })
  @IsString()
  @IsOptional()
  description?: string;
}
