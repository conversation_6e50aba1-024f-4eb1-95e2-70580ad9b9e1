{"version": 3, "sources": ["../../src/entity-schema/EntitySchemaColumnOptions.ts"], "names": [], "mappings": "", "file": "EntitySchemaColumnOptions.js", "sourcesContent": ["import { ColumnType } from \"../driver/types/ColumnTypes\"\nimport { ValueTransformer } from \"../decorator/options/ValueTransformer\"\nimport { SpatialColumnOptions } from \"../decorator/options/SpatialColumnOptions\"\nimport { EntitySchemaColumnForeignKeyOptions } from \"./EntitySchemaColumnForeignKeyOptions\"\n\nexport interface EntitySchemaColumnOptions extends SpatialColumnOptions {\n    /**\n     * Indicates if this column is a primary column.\n     */\n    primary?: boolean\n\n    /**\n     * Indicates if this column is of type ObjectId\n     */\n    objectId?: boolean\n\n    /**\n     * Indicates if this column is a created date column.\n     */\n    createDate?: boolean\n\n    /**\n     * Indicates if this column is an update date column.\n     */\n    updateDate?: boolean\n\n    /**\n     * Indicates if this column is a delete date column.\n     */\n    deleteDate?: boolean\n\n    /**\n     * Indicates if this column is a version column.\n     */\n    version?: boolean\n\n    /**\n     * Indicates if this column is a treeChildrenCount column.\n     */\n    treeChildrenCount?: boolean\n\n    /**\n     * Indicates if this column is a treeLevel column.\n     */\n    treeLevel?: boolean\n\n    /**\n     * Column type. Must be one of the value from the ColumnTypes class.\n     */\n    type: ColumnType\n\n    /**\n     * Column name in the database.\n     */\n    name?: string\n\n    /**\n     * Column type's length. For example type = \"string\" and length = 100 means that ORM will create a column with\n     * type varchar(100).\n     */\n    length?: string | number\n\n    /**\n     * Column type's display width. Used only on some column types in MySQL.\n     * For example, INT(4) specifies an INT with a display width of four digits.\n     */\n    width?: number\n\n    /**\n     * Indicates if column's value can be set to NULL.\n     */\n    nullable?: boolean\n\n    /**\n     * Indicates if column value is not updated by \"save\" operation.\n     * It means you'll be able to write this value only when you first time insert the object.\n     * Default value is \"false\".\n     *\n     * @deprecated Please use the `update` option instead.  Careful, it takes\n     * the opposite value to readonly.\n     *\n     */\n    readonly?: boolean\n\n    /**\n     * Indicates if column value is updated by \"save\" operation.\n     * If false you'll be able to write this value only when you first time insert the object.\n     * Default value is \"true\".\n     */\n    update?: boolean\n\n    /**\n     * Indicates if column is always selected by QueryBuilder and find operations.\n     * Default value is \"true\".\n     */\n    select?: boolean\n\n    /**\n     * Indicates if column is inserted by default.\n     * Default value is \"true\".\n     */\n    insert?: boolean\n\n    /**\n     * Specifies if this column will use AUTO_INCREMENT or not (e.g. generated number).\n     */\n    generated?: true | \"increment\" | \"uuid\" | \"rowid\"\n\n    /**\n     * Specifies if column's value must be unique or not.\n     */\n    unique?: boolean\n\n    /**\n     * Extra column definition. Should be used only in emergency situations. Note that if you'll use this property\n     * auto schema generation will not work properly anymore. Avoid using it.\n     */\n    columnDefinition?: string\n\n    /**\n     * Column comment.\n     */\n    comment?: string\n\n    /**\n     * Default database value.\n     */\n    default?: any\n\n    /**\n     * ON UPDATE trigger. Works only for MySQL.\n     */\n    onUpdate?: string\n\n    /**\n     * The precision for a decimal (exact numeric) column (applies only for decimal column), which is the maximum\n     * number of digits that are stored for the values.\n     */\n    precision?: number\n\n    /**\n     * The scale for a decimal (exact numeric) column (applies only for decimal column), which represents the number\n     * of digits to the right of the decimal point and must not be greater than precision.\n     */\n    scale?: number\n\n    /**\n     * Puts ZEROFILL attribute on to numeric column. Works only for MySQL.\n     * If you specify ZEROFILL for a numeric column, MySQL automatically adds the UNSIGNED attribute to the column\n     */\n    zerofill?: boolean\n\n    /**\n     * Puts UNSIGNED attribute on to numeric column. Works only for MySQL.\n     */\n    unsigned?: boolean\n\n    /**\n     * Defines a column character set.\n     * Not supported by all database types.\n     */\n    charset?: string\n\n    /**\n     * Defines a column collation.\n     */\n    collation?: string\n\n    /**\n     * Array of possible enumerated values.\n     */\n    enum?: any[] | Object\n\n    /**\n     * Exact name of enum\n     */\n    enumName?: string\n\n    /**\n     * Generated column expression.\n     */\n    asExpression?: string\n\n    /**\n     * Generated column type.\n     */\n    generatedType?: \"VIRTUAL\" | \"STORED\"\n\n    /**\n     * Return type of HSTORE column.\n     * Returns value as string or as object.\n     */\n    hstoreType?: \"object\" | \"string\"\n\n    /**\n     * Indicates if this column is an array.\n     * Can be simply set to true or array length can be specified.\n     * Supported only by postgres.\n     */\n    array?: boolean\n\n    /**\n     * Specifies a value transformer that is to be used to (un)marshal\n     * this column when reading or writing to the database.\n     */\n    transformer?: ValueTransformer | ValueTransformer[]\n\n    /**\n     * Name of the primary key constraint.\n     */\n    primaryKeyConstraintName?: string\n\n    /**\n     * Foreign key options of this column.\n     */\n    foreignKey?: EntitySchemaColumnForeignKeyOptions\n}\n"], "sourceRoot": ".."}