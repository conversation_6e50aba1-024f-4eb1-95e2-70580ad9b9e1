{"version": 3, "sources": ["../../src/schema-builder/MongoSchemaBuilder.ts"], "names": [], "mappings": ";;;AAGA,uDAAmD;AAGnD;;;;;;;;;;;;;GAaG;AACH,MAAa,kBAAkB;IAC3B,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YAAsB,UAAsB;QAAtB,eAAU,GAAV,UAAU,CAAY;IAAG,CAAC;IAEhD,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;OAEG;IACH,KAAK,CAAC,KAAK;QACP,MAAM,WAAW,GACb,IAAI,CAAC,UAAU,CAAC,iBAAiB,EAAsB,CAAA;QAC3D,MAAM,QAAQ,GAAmB,EAAE,CAAA;QACnC,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;YACjD,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;gBAC/B,MAAM,OAAO,GAAyB,MAAM,CAAC,MAAM,CAC/C,EAAE,EACF;oBACI,IAAI,EAAE,KAAK,CAAC,IAAI;oBAChB,MAAM,EAAE,KAAK,CAAC,QAAQ;oBACtB,MAAM,EAAE,KAAK,CAAC,QAAQ;oBACtB,UAAU,EAAE,KAAK,CAAC,YAAY;iBACjC,EACD,KAAK,CAAC,kBAAkB,KAAK,SAAS;oBAClC,CAAC,CAAC,EAAE;oBACJ,CAAC,CAAC,EAAE,kBAAkB,EAAE,KAAK,CAAC,kBAAkB,EAAE,CACzD,CAAA;gBACD,QAAQ,CAAC,IAAI,CACT,WAAW,CAAC,qBAAqB,CAC7B,QAAQ,CAAC,SAAS,EAClB,KAAK,CAAC,0BAA0B,EAChC,OAAO,CACV,CACJ,CAAA;YACL,CAAC,CAAC,CAAA;YACF,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;gBAChC,MAAM,OAAO,GAAyB;oBAClC,IAAI,EAAE,MAAM,CAAC,IAAI;oBACjB,MAAM,EAAE,IAAI;iBACf,CAAA;gBACD,QAAQ,CAAC,IAAI,CACT,WAAW,CAAC,qBAAqB,CAC7B,QAAQ,CAAC,SAAS,EAClB,MAAM,CAAC,0BAA0B,EACjC,OAAO,CACV,CACJ,CAAA;YACL,CAAC,CAAC,CAAA;QACN,CAAC,CAAC,CAAA;QACF,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;IAC/B,CAAC;IAED;;OAEG;IACH,GAAG;QACC,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,yBAAW,EAAE,CAAC,CAAA;IAC7C,CAAC;CACJ;AA/DD,gDA+DC", "file": "MongoSchemaBuilder.js", "sourcesContent": ["import { DataSource } from \"../data-source/DataSource\"\nimport { SchemaBuilder } from \"./SchemaBuilder\"\nimport { MongoQueryRunner } from \"../driver/mongodb/MongoQueryRunner\"\nimport { SqlInMemory } from \"../driver/SqlInMemory\"\nimport { CreateIndexesOptions } from \"../driver/mongodb/typings\"\n\n/**\n * Creates complete tables schemas in the database based on the entity metadatas.\n *\n * Steps how schema is being built:\n * 1. load list of all tables with complete column and keys information from the db\n * 2. drop all (old) foreign keys that exist in the table, but does not exist in the metadata\n * 3. create new tables that does not exist in the db, but exist in the metadata\n * 4. drop all columns exist (left old) in the db table, but does not exist in the metadata\n * 5. add columns from metadata which does not exist in the table\n * 6. update all exist columns which metadata has changed\n * 7. update primary keys - update old and create new primary key from changed columns\n * 8. create foreign keys which does not exist in the table yet\n * 9. create indices which are missing in db yet, and drops indices which exist in the db, but does not exist in the metadata anymore\n */\nexport class MongoSchemaBuilder implements SchemaBuilder {\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(protected connection: DataSource) {}\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Creates complete schemas for the given entity metadatas.\n     */\n    async build(): Promise<void> {\n        const queryRunner =\n            this.connection.createQueryRunner() as MongoQueryRunner\n        const promises: Promise<any>[] = []\n        this.connection.entityMetadatas.forEach((metadata) => {\n            metadata.indices.forEach((index) => {\n                const options: CreateIndexesOptions = Object.assign(\n                    {},\n                    {\n                        name: index.name,\n                        unique: index.isUnique,\n                        sparse: index.isSparse,\n                        background: index.isBackground,\n                    },\n                    index.expireAfterSeconds === undefined\n                        ? {}\n                        : { expireAfterSeconds: index.expireAfterSeconds },\n                )\n                promises.push(\n                    queryRunner.createCollectionIndex(\n                        metadata.tableName,\n                        index.columnNamesWithOrderingMap,\n                        options,\n                    ),\n                )\n            })\n            metadata.uniques.forEach((unique) => {\n                const options = <CreateIndexesOptions>{\n                    name: unique.name,\n                    unique: true,\n                }\n                promises.push(\n                    queryRunner.createCollectionIndex(\n                        metadata.tableName,\n                        unique.columnNamesWithOrderingMap,\n                        options,\n                    ),\n                )\n            })\n        })\n        await Promise.all(promises)\n    }\n\n    /**\n     * Returns query to be executed by schema builder.\n     */\n    log(): Promise<SqlInMemory> {\n        return Promise.resolve(new SqlInMemory())\n    }\n}\n"], "sourceRoot": ".."}