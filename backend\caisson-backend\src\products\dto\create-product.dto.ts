import {
  IsString,
  IsNotEmpty,
  IsArray,
  IsBoolean,
  IsOptional,
  IsObject,
  IsUUID,
  IsEnum,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { ProductStatus } from '../../entities/product.entity';

export class CreateProductDto {
  @ApiProperty({ example: 'Coffret Tunnel Volet Roulant' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({ example: 'coffret-tunnel-volet-roulant' })
  @IsString()
  @IsNotEmpty()
  slug: string;

  @ApiProperty({
    example: 'Solution légère et isolante pour l\'installation de volets roulants.',
  })
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiPropertyOptional({
    example: '<p>Description détaillée du produit...</p>',
  })
  @IsString()
  @IsOptional()
  longDescription?: string;

  @ApiProperty({
    example: ['/uploads/image1.jpg', '/uploads/image2.jpg'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  images: string[];

  @ApiProperty({ example: true })
  @IsBoolean()
  inStock: boolean;

  @ApiProperty({
    example: ['Légèreté et facilité d\'installation', 'Excellente isolation thermique'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  features: string[];

  @ApiPropertyOptional({
    example: {
      'Matériau': 'Polystyrène expansé (EPS)',
      'Densité': '30 kg/m³',
    },
  })
  @IsObject()
  @IsOptional()
  specifications?: Record<string, string>;

  @ApiProperty({ example: 'uuid-of-category' })
  @IsUUID()
  @IsNotEmpty()
  categoryId: string;

  @ApiPropertyOptional({
    example: ['uuid-of-tag-1', 'uuid-of-tag-2'],
    type: [String],
  })
  @IsArray()
  @IsUUID('4', { each: true })
  @IsOptional()
  tagIds?: string[];

  @ApiPropertyOptional({
    example: ProductStatus.PUBLISHED,
    enum: ProductStatus,
  })
  @IsEnum(ProductStatus)
  @IsOptional()
  status?: ProductStatus;
}
