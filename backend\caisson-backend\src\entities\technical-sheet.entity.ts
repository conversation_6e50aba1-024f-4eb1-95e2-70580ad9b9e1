import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type TechnicalSheetDocument = TechnicalSheet & Document;

@Schema({ timestamps: true })
export class TechnicalSheet {
  @Prop({ required: true })
  title: string;

  @Prop({ required: true })
  description: string;

  @Prop()
  thumbnail?: string;

  @Prop({ required: true })
  fileUrl: string;

  @Prop({ required: true })
  fileSize: string;

  @Prop({ type: Types.ObjectId, ref: 'Product', required: true })
  product: Types.ObjectId;

  createdAt?: Date;
  updatedAt?: Date;
}

export const TechnicalSheetSchema = SchemaFactory.createForClass(TechnicalSheet);
