{"version": 3, "sources": ["../../src/logger/LoggerFactory.ts"], "names": [], "mappings": ";;;AAEA,+DAA2D;AAC3D,mEAA+D;AAC/D,6CAAyC;AACzC,+CAA2C;AAC3C,qDAAiD;AACjD,qEAAiE;AAEjE;;GAEG;AACH,MAAa,aAAa;IACtB;;OAEG;IACH,MAAM,CACF,MAMY,EACZ,OAAuB;QAEvB,IAAI,yBAAW,CAAC,QAAQ,CAAC,MAAM,CAAC;YAAE,OAAO,MAAgB,CAAA;QAEzD,IAAI,MAAM,EAAE,CAAC;YACT,QAAQ,MAAM,EAAE,CAAC;gBACb,KAAK,gBAAgB;oBACjB,OAAO,IAAI,yCAAmB,CAAC,OAAO,CAAC,CAAA;gBAE3C,KAAK,MAAM;oBACP,OAAO,IAAI,uBAAU,CAAC,OAAO,CAAC,CAAA;gBAElC,KAAK,kBAAkB;oBACnB,OAAO,IAAI,6CAAqB,CAAC,OAAO,CAAC,CAAA;gBAE7C,KAAK,mBAAmB;oBACpB,OAAO,IAAI,+CAAsB,CAAC,OAAO,CAAC,CAAA;gBAE9C,KAAK,OAAO;oBACR,OAAO,IAAI,yBAAW,EAAE,CAAA;YAChC,CAAC;QACL,CAAC;QAED,OAAO,IAAI,6CAAqB,CAAC,OAAO,CAAC,CAAA;IAC7C,CAAC;CACJ;AArCD,sCAqCC", "file": "LoggerFactory.js", "sourcesContent": ["import { Logger } from \"./Logger\"\nimport { LoggerOptions } from \"./LoggerOptions\"\nimport { SimpleConsoleLogger } from \"./SimpleConsoleLogger\"\nimport { AdvancedConsoleLogger } from \"./AdvancedConsoleLogger\"\nimport { FileLogger } from \"./FileLogger\"\nimport { DebugLogger } from \"./DebugLogger\"\nimport { ObjectUtils } from \"../util/ObjectUtils\"\nimport { FormattedConsoleLogger } from \"./FormattedConsoleLogger\"\n\n/**\n * Helps to create logger instances.\n */\nexport class LoggerFactory {\n    /**\n     * Creates a new logger depend on a given connection's driver.\n     */\n    create(\n        logger?:\n            | \"advanced-console\"\n            | \"simple-console\"\n            | \"formatted-console\"\n            | \"file\"\n            | \"debug\"\n            | Logger,\n        options?: LoggerOptions,\n    ): Logger {\n        if (ObjectUtils.isObject(logger)) return logger as Logger\n\n        if (logger) {\n            switch (logger) {\n                case \"simple-console\":\n                    return new SimpleConsoleLogger(options)\n\n                case \"file\":\n                    return new FileLogger(options)\n\n                case \"advanced-console\":\n                    return new AdvancedConsoleLogger(options)\n\n                case \"formatted-console\":\n                    return new FormattedConsoleLogger(options)\n\n                case \"debug\":\n                    return new DebugLogger()\n            }\n        }\n\n        return new AdvancedConsoleLogger(options)\n    }\n}\n"], "sourceRoot": ".."}