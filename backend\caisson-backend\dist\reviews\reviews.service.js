"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReviewsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const review_entity_1 = require("../entities/review.entity");
const product_entity_1 = require("../entities/product.entity");
const products_service_1 = require("../products/products.service");
let ReviewsService = class ReviewsService {
    reviewRepository;
    productRepository;
    productsService;
    constructor(reviewRepository, productRepository, productsService) {
        this.reviewRepository = reviewRepository;
        this.productRepository = productRepository;
        this.productsService = productsService;
    }
    async create(createReviewDto) {
        const { productId, ...reviewData } = createReviewDto;
        const product = await this.productRepository.findOne({
            where: { id: productId },
        });
        if (!product) {
            throw new common_1.NotFoundException('Product not found');
        }
        const review = this.reviewRepository.create({
            ...reviewData,
            product,
        });
        const savedReview = await this.reviewRepository.save(review);
        await this.productsService.updateRating(productId);
        return savedReview;
    }
    async findAll() {
        return this.reviewRepository.find({
            relations: ['product'],
            order: { createdAt: 'DESC' },
        });
    }
    async findByProduct(productId) {
        const product = await this.productRepository.findOne({
            where: { id: productId },
        });
        if (!product) {
            throw new common_1.NotFoundException('Product not found');
        }
        return this.reviewRepository.find({
            where: { product: { id: productId } },
            relations: ['product'],
            order: { createdAt: 'DESC' },
        });
    }
    async findOne(id) {
        const review = await this.reviewRepository.findOne({
            where: { id },
            relations: ['product'],
        });
        if (!review) {
            throw new common_1.NotFoundException('Review not found');
        }
        return review;
    }
    async update(id, updateReviewDto) {
        const review = await this.findOne(id);
        Object.assign(review, updateReviewDto);
        const updatedReview = await this.reviewRepository.save(review);
        if (updateReviewDto.rating !== undefined) {
            await this.productsService.updateRating(review.product.id);
        }
        return updatedReview;
    }
    async remove(id) {
        const review = await this.findOne(id);
        const productId = review.product.id;
        await this.reviewRepository.remove(review);
        await this.productsService.updateRating(productId);
    }
    async getProductRatingStats(productId) {
        const product = await this.productRepository.findOne({
            where: { id: productId },
        });
        if (!product) {
            throw new common_1.NotFoundException('Product not found');
        }
        const stats = await this.reviewRepository
            .createQueryBuilder('review')
            .select('review.rating', 'rating')
            .addSelect('COUNT(*)', 'count')
            .where('review.productId = :productId', { productId })
            .groupBy('review.rating')
            .orderBy('review.rating', 'ASC')
            .getRawMany();
        const totalReviews = await this.reviewRepository.count({
            where: { product: { id: productId } },
        });
        const avgRating = await this.reviewRepository
            .createQueryBuilder('review')
            .select('AVG(review.rating)', 'avg')
            .where('review.productId = :productId', { productId })
            .getRawOne();
        return {
            totalReviews,
            averageRating: parseFloat(avgRating.avg) || 0,
            ratingDistribution: stats.map(stat => ({
                rating: parseInt(stat.rating),
                count: parseInt(stat.count),
            })),
        };
    }
};
exports.ReviewsService = ReviewsService;
exports.ReviewsService = ReviewsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(review_entity_1.Review)),
    __param(1, (0, typeorm_1.InjectRepository)(product_entity_1.Product)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        products_service_1.ProductsService])
], ReviewsService);
//# sourceMappingURL=reviews.service.js.map