"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReviewsService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const review_entity_1 = require("../entities/review.entity");
const product_entity_1 = require("../entities/product.entity");
const products_service_1 = require("../products/products.service");
let ReviewsService = class ReviewsService {
    reviewModel;
    productModel;
    productsService;
    constructor(reviewModel, productModel, productsService) {
        this.reviewModel = reviewModel;
        this.productModel = productModel;
        this.productsService = productsService;
    }
    async create(createReviewDto) {
        const { productId, ...reviewData } = createReviewDto;
        const product = await this.productModel.findById(productId).exec();
        if (!product) {
            throw new common_1.NotFoundException('Product not found');
        }
        const review = new this.reviewModel({
            ...reviewData,
            product: new mongoose_2.Types.ObjectId(productId),
        });
        const savedReview = await review.save();
        await this.productsService.updateRating(productId);
        return savedReview;
    }
    async findAll() {
        return this.reviewModel.find()
            .populate('product')
            .sort({ createdAt: -1 })
            .exec();
    }
    async findByProduct(productId) {
        const product = await this.productModel.findById(productId).exec();
        if (!product) {
            throw new common_1.NotFoundException('Product not found');
        }
        return this.reviewModel.find({
            product: new mongoose_2.Types.ObjectId(productId)
        })
            .populate('product')
            .sort({ createdAt: -1 })
            .exec();
    }
    async findOne(id) {
        const review = await this.reviewModel.findById(id)
            .populate('product')
            .exec();
        if (!review) {
            throw new common_1.NotFoundException('Review not found');
        }
        return review;
    }
    async update(id, updateReviewDto) {
        const review = await this.reviewModel.findByIdAndUpdate(id, updateReviewDto, { new: true }).populate('product').exec();
        if (!review) {
            throw new common_1.NotFoundException('Review not found');
        }
        if (updateReviewDto.rating !== undefined) {
            await this.productsService.updateRating(review.product._id.toString());
        }
        return review;
    }
    async remove(id) {
        const review = await this.reviewModel.findById(id).exec();
        if (!review) {
            throw new common_1.NotFoundException('Review not found');
        }
        const productId = review.product.toString();
        await this.reviewModel.findByIdAndDelete(id).exec();
        await this.productsService.updateRating(productId);
    }
    async getProductRatingStats(productId) {
        const product = await this.productModel.findById(productId).exec();
        if (!product) {
            throw new common_1.NotFoundException('Product not found');
        }
        const stats = await this.reviewModel.aggregate([
            { $match: { product: new mongoose_2.Types.ObjectId(productId) } },
            {
                $group: {
                    _id: '$rating',
                    count: { $sum: 1 }
                }
            },
            { $sort: { _id: 1 } }
        ]);
        const totalReviews = await this.reviewModel.countDocuments({
            product: new mongoose_2.Types.ObjectId(productId)
        });
        const avgRatingResult = await this.reviewModel.aggregate([
            { $match: { product: new mongoose_2.Types.ObjectId(productId) } },
            {
                $group: {
                    _id: null,
                    avgRating: { $avg: '$rating' }
                }
            }
        ]);
        return {
            totalReviews,
            averageRating: avgRatingResult.length > 0 ? avgRatingResult[0].avgRating : 0,
            ratingDistribution: stats.map(stat => ({
                rating: stat._id,
                count: stat.count,
            })),
        };
    }
};
exports.ReviewsService = ReviewsService;
exports.ReviewsService = ReviewsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(review_entity_1.Review.name)),
    __param(1, (0, mongoose_1.InjectModel)(product_entity_1.Product.name)),
    __metadata("design:paramtypes", [mongoose_2.Model,
        mongoose_2.Model,
        products_service_1.ProductsService])
], ReviewsService);
//# sourceMappingURL=reviews.service.js.map