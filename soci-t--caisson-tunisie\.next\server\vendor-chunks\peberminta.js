"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/peberminta";
exports.ids = ["vendor-chunks/peberminta"];
exports.modules = {

/***/ "(action-browser)/./node_modules/peberminta/lib/core.mjs":
/*!**********************************************!*\
  !*** ./node_modules/peberminta/lib/core.mjs ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ab: () => (/* binding */ ab),\n/* harmony export */   abc: () => (/* binding */ abc),\n/* harmony export */   action: () => (/* binding */ action),\n/* harmony export */   ahead: () => (/* binding */ ahead),\n/* harmony export */   all: () => (/* binding */ all),\n/* harmony export */   and: () => (/* binding */ all),\n/* harmony export */   any: () => (/* binding */ any),\n/* harmony export */   chain: () => (/* binding */ chain),\n/* harmony export */   chainReduce: () => (/* binding */ chainReduce),\n/* harmony export */   choice: () => (/* binding */ choice),\n/* harmony export */   condition: () => (/* binding */ condition),\n/* harmony export */   decide: () => (/* binding */ decide),\n/* harmony export */   discard: () => (/* binding */ skip),\n/* harmony export */   eitherOr: () => (/* binding */ otherwise),\n/* harmony export */   emit: () => (/* binding */ emit),\n/* harmony export */   end: () => (/* binding */ end),\n/* harmony export */   eof: () => (/* binding */ end),\n/* harmony export */   error: () => (/* binding */ error),\n/* harmony export */   fail: () => (/* binding */ fail),\n/* harmony export */   flatten: () => (/* binding */ flatten),\n/* harmony export */   flatten1: () => (/* binding */ flatten1),\n/* harmony export */   left: () => (/* binding */ left),\n/* harmony export */   leftAssoc1: () => (/* binding */ leftAssoc1),\n/* harmony export */   leftAssoc2: () => (/* binding */ leftAssoc2),\n/* harmony export */   longest: () => (/* binding */ longest),\n/* harmony export */   lookAhead: () => (/* binding */ ahead),\n/* harmony export */   make: () => (/* binding */ make),\n/* harmony export */   many: () => (/* binding */ many),\n/* harmony export */   many1: () => (/* binding */ many1),\n/* harmony export */   map: () => (/* binding */ map),\n/* harmony export */   map1: () => (/* binding */ map1),\n/* harmony export */   match: () => (/* binding */ match),\n/* harmony export */   middle: () => (/* binding */ middle),\n/* harmony export */   not: () => (/* binding */ not),\n/* harmony export */   of: () => (/* binding */ emit),\n/* harmony export */   option: () => (/* binding */ option),\n/* harmony export */   or: () => (/* binding */ choice),\n/* harmony export */   otherwise: () => (/* binding */ otherwise),\n/* harmony export */   parse: () => (/* binding */ parse),\n/* harmony export */   parserPosition: () => (/* binding */ parserPosition),\n/* harmony export */   peek: () => (/* binding */ peek),\n/* harmony export */   recursive: () => (/* binding */ recursive),\n/* harmony export */   reduceLeft: () => (/* binding */ reduceLeft),\n/* harmony export */   reduceRight: () => (/* binding */ reduceRight),\n/* harmony export */   remainingTokensNumber: () => (/* binding */ remainingTokensNumber),\n/* harmony export */   right: () => (/* binding */ right),\n/* harmony export */   rightAssoc1: () => (/* binding */ rightAssoc1),\n/* harmony export */   rightAssoc2: () => (/* binding */ rightAssoc2),\n/* harmony export */   satisfy: () => (/* binding */ satisfy),\n/* harmony export */   sepBy: () => (/* binding */ sepBy),\n/* harmony export */   sepBy1: () => (/* binding */ sepBy1),\n/* harmony export */   skip: () => (/* binding */ skip),\n/* harmony export */   some: () => (/* binding */ many1),\n/* harmony export */   start: () => (/* binding */ start),\n/* harmony export */   takeUntil: () => (/* binding */ takeUntil),\n/* harmony export */   takeUntilP: () => (/* binding */ takeUntilP),\n/* harmony export */   takeWhile: () => (/* binding */ takeWhile),\n/* harmony export */   takeWhileP: () => (/* binding */ takeWhileP),\n/* harmony export */   token: () => (/* binding */ token),\n/* harmony export */   tryParse: () => (/* binding */ tryParse)\n/* harmony export */ });\n/* harmony import */ var _util_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util.mjs */ \"(action-browser)/./node_modules/peberminta/lib/util.mjs\");\n\n\nfunction emit(value) {\n    return (data, i) => ({\n        matched: true,\n        position: i,\n        value: value\n    });\n}\nfunction make(\nf) {\n    return (data, i) => ({\n        matched: true,\n        position: i,\n        value: f(data, i)\n    });\n}\nfunction action(\nf) {\n    return (data, i) => {\n        f(data, i);\n        return {\n            matched: true,\n            position: i,\n            value: null\n        };\n    };\n}\nfunction fail(\ndata, i) {\n    return { matched: false };\n}\nfunction error(message) {\n    return (data, i) => {\n        throw new Error((message instanceof Function) ? message(data, i) : message);\n    };\n}\nfunction token(\nonToken,\nonEnd) {\n    return (data, i) => {\n        let position = i;\n        let value = undefined;\n        if (i < data.tokens.length) {\n            value = onToken(data.tokens[i], data, i);\n            if (value !== undefined) {\n                position++;\n            }\n        }\n        else {\n            onEnd?.(data, i);\n        }\n        return (value === undefined)\n            ? { matched: false }\n            : {\n                matched: true,\n                position: position,\n                value: value\n            };\n    };\n}\nfunction any(data, i) {\n    return (i < data.tokens.length)\n        ? {\n            matched: true,\n            position: i + 1,\n            value: data.tokens[i]\n        }\n        : { matched: false };\n}\nfunction satisfy(\ntest) {\n    return (data, i) => (i < data.tokens.length && test(data.tokens[i], data, i))\n        ? {\n            matched: true,\n            position: i + 1,\n            value: data.tokens[i]\n        }\n        : { matched: false };\n}\nfunction mapInner(r, f) {\n    return (r.matched) ? ({\n        matched: true,\n        position: r.position,\n        value: f(r.value, r.position)\n    }) : r;\n}\nfunction mapOuter(r, f) {\n    return (r.matched) ? f(r) : r;\n}\nfunction map(p, mapper) {\n    return (data, i) => mapInner(p(data, i), (v, j) => mapper(v, data, i, j));\n}\nfunction map1(p,\nmapper) {\n    return (data, i) => mapOuter(p(data, i), (m) => mapper(m, data, i));\n}\nfunction peek(p, f) {\n    return (data, i) => {\n        const r = p(data, i);\n        f(r, data, i);\n        return r;\n    };\n}\nfunction option(p, def) {\n    return (data, i) => {\n        const r = p(data, i);\n        return (r.matched)\n            ? r\n            : {\n                matched: true,\n                position: i,\n                value: def\n            };\n    };\n}\nfunction not(p) {\n    return (data, i) => {\n        const r = p(data, i);\n        return (r.matched)\n            ? { matched: false }\n            : {\n                matched: true,\n                position: i,\n                value: true\n            };\n    };\n}\nfunction choice(...ps) {\n    return (data, i) => {\n        for (const p of ps) {\n            const result = p(data, i);\n            if (result.matched) {\n                return result;\n            }\n        }\n        return { matched: false };\n    };\n}\nfunction otherwise(pa, pb) {\n    return (data, i) => {\n        const r1 = pa(data, i);\n        return (r1.matched)\n            ? r1\n            : pb(data, i);\n    };\n}\nfunction longest(...ps) {\n    return (data, i) => {\n        let match = undefined;\n        for (const p of ps) {\n            const result = p(data, i);\n            if (result.matched && (!match || match.position < result.position)) {\n                match = result;\n            }\n        }\n        return match || { matched: false };\n    };\n}\nfunction takeWhile(p,\ntest) {\n    return (data, i) => {\n        const values = [];\n        let success = true;\n        do {\n            const r = p(data, i);\n            if (r.matched && test(r.value, values.length + 1, data, i, r.position)) {\n                values.push(r.value);\n                i = r.position;\n            }\n            else {\n                success = false;\n            }\n        } while (success);\n        return {\n            matched: true,\n            position: i,\n            value: values\n        };\n    };\n}\nfunction takeUntil(p,\ntest) {\n    return takeWhile(p, (value, n, data, i, j) => !test(value, n, data, i, j));\n}\nfunction takeWhileP(pValue, pTest) {\n    return takeWhile(pValue, (value, n, data, i) => pTest(data, i).matched);\n}\nfunction takeUntilP(pValue, pTest) {\n    return takeWhile(pValue, (value, n, data, i) => !pTest(data, i).matched);\n}\nfunction many(p) {\n    return takeWhile(p, () => true);\n}\nfunction many1(p) {\n    return ab(p, many(p), (head, tail) => [head, ...tail]);\n}\nfunction ab(pa, pb, join) {\n    return (data, i) => mapOuter(pa(data, i), (ma) => mapInner(pb(data, ma.position), (vb, j) => join(ma.value, vb, data, i, j)));\n}\nfunction left(pa, pb) {\n    return ab(pa, pb, (va) => va);\n}\nfunction right(pa, pb) {\n    return ab(pa, pb, (va, vb) => vb);\n}\nfunction abc(pa, pb, pc, join) {\n    return (data, i) => mapOuter(pa(data, i), (ma) => mapOuter(pb(data, ma.position), (mb) => mapInner(pc(data, mb.position), (vc, j) => join(ma.value, mb.value, vc, data, i, j))));\n}\nfunction middle(pa, pb, pc) {\n    return abc(pa, pb, pc, (ra, rb) => rb);\n}\nfunction all(...ps) {\n    return (data, i) => {\n        const result = [];\n        let position = i;\n        for (const p of ps) {\n            const r1 = p(data, position);\n            if (r1.matched) {\n                result.push(r1.value);\n                position = r1.position;\n            }\n            else {\n                return { matched: false };\n            }\n        }\n        return {\n            matched: true,\n            position: position,\n            value: result\n        };\n    };\n}\nfunction skip(...ps) {\n    return map(all(...ps), () => null);\n}\nfunction flatten(...ps) {\n    return flatten1(all(...ps));\n}\nfunction flatten1(p) {\n    return map(p, (vs) => vs.flatMap((v) => v));\n}\nfunction sepBy1(pValue, pSep) {\n    return ab(pValue, many(right(pSep, pValue)), (head, tail) => [head, ...tail]);\n}\nfunction sepBy(pValue, pSep) {\n    return otherwise(sepBy1(pValue, pSep), emit([]));\n}\nfunction chainReduce(acc,\nf) {\n    return (data, i) => {\n        let loop = true;\n        let acc1 = acc;\n        let pos = i;\n        do {\n            const r = f(acc1, data, pos)(data, pos);\n            if (r.matched) {\n                acc1 = r.value;\n                pos = r.position;\n            }\n            else {\n                loop = false;\n            }\n        } while (loop);\n        return {\n            matched: true,\n            position: pos,\n            value: acc1\n        };\n    };\n}\nfunction reduceLeft(acc, p,\nreducer) {\n    return chainReduce(acc, (acc) => map(p, (v, data, i, j) => reducer(acc, v, data, i, j)));\n}\nfunction reduceRight(p, acc,\nreducer) {\n    return map(many(p), (vs, data, i, j) => vs.reduceRight((acc, v) => reducer(v, acc, data, i, j), acc));\n}\nfunction leftAssoc1(pLeft, pOper) {\n    return chain(pLeft, (v0) => reduceLeft(v0, pOper, (acc, f) => f(acc)));\n}\nfunction rightAssoc1(pOper, pRight) {\n    return ab(reduceRight(pOper, (y) => y, (f, acc) => (y) => f(acc(y))), pRight, (f, v) => f(v));\n}\nfunction leftAssoc2(pLeft, pOper, pRight) {\n    return chain(pLeft, (v0) => reduceLeft(v0, ab(pOper, pRight, (f, y) => [f, y]), (acc, [f, y]) => f(acc, y)));\n}\nfunction rightAssoc2(pLeft, pOper, pRight) {\n    return ab(reduceRight(ab(pLeft, pOper, (x, f) => [x, f]), (y) => y, ([x, f], acc) => (y) => f(x, acc(y))), pRight, (f, v) => f(v));\n}\nfunction condition(cond, pTrue, pFalse) {\n    return (data, i) => (cond(data, i))\n        ? pTrue(data, i)\n        : pFalse(data, i);\n}\nfunction decide(p) {\n    return (data, i) => mapOuter(p(data, i), (m1) => m1.value(data, m1.position));\n}\nfunction chain(p,\nf) {\n    return (data, i) => mapOuter(p(data, i), (m1) => f(m1.value, data, i, m1.position)(data, m1.position));\n}\nfunction ahead(p) {\n    return (data, i) => mapOuter(p(data, i), (m1) => ({\n        matched: true,\n        position: i,\n        value: m1.value\n    }));\n}\nfunction recursive(f) {\n    return function (data, i) {\n        return f()(data, i);\n    };\n}\nfunction start(data, i) {\n    return (i !== 0)\n        ? { matched: false }\n        : {\n            matched: true,\n            position: i,\n            value: true\n        };\n}\nfunction end(data, i) {\n    return (i < data.tokens.length)\n        ? { matched: false }\n        : {\n            matched: true,\n            position: i,\n            value: true\n        };\n}\nfunction remainingTokensNumber(data, i) {\n    return data.tokens.length - i;\n}\nfunction parserPosition(data, i, formatToken, contextTokens = 3) {\n    const len = data.tokens.length;\n    const lowIndex = (0,_util_mjs__WEBPACK_IMPORTED_MODULE_0__.clamp)(0, i - contextTokens, len - contextTokens);\n    const highIndex = (0,_util_mjs__WEBPACK_IMPORTED_MODULE_0__.clamp)(contextTokens, i + 1 + contextTokens, len);\n    const tokensSlice = data.tokens.slice(lowIndex, highIndex);\n    const lines = [];\n    const indexWidth = String(highIndex - 1).length + 1;\n    if (i < 0) {\n        lines.push(`${String(i).padStart(indexWidth)} >>`);\n    }\n    if (0 < lowIndex) {\n        lines.push('...'.padStart(indexWidth + 6));\n    }\n    for (let j = 0; j < tokensSlice.length; j++) {\n        const index = lowIndex + j;\n        lines.push(`${String(index).padStart(indexWidth)} ${(index === i ? '>' : ' ')} ${(0,_util_mjs__WEBPACK_IMPORTED_MODULE_0__.escapeWhitespace)(formatToken(tokensSlice[j]))}`);\n    }\n    if (highIndex < len) {\n        lines.push('...'.padStart(indexWidth + 6));\n    }\n    if (len <= i) {\n        lines.push(`${String(i).padStart(indexWidth)} >>`);\n    }\n    return lines.join('\\n');\n}\nfunction parse(parser, tokens, options, formatToken = JSON.stringify) {\n    const data = { tokens: tokens, options: options };\n    const result = parser(data, 0);\n    if (!result.matched) {\n        throw new Error('No match');\n    }\n    if (result.position < data.tokens.length) {\n        throw new Error(`Partial match. Parsing stopped at:\\n${parserPosition(data, result.position, formatToken)}`);\n    }\n    return result.value;\n}\nfunction tryParse(parser, tokens, options) {\n    const result = parser({ tokens: tokens, options: options }, 0);\n    return (result.matched)\n        ? result.value\n        : undefined;\n}\nfunction match(matcher, tokens, options) {\n    const result = matcher({ tokens: tokens, options: options }, 0);\n    return result.value;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/peberminta/lib/core.mjs\n");

/***/ }),

/***/ "(action-browser)/./node_modules/peberminta/lib/util.mjs":
/*!**********************************************!*\
  !*** ./node_modules/peberminta/lib/util.mjs ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clamp: () => (/* binding */ clamp),\n/* harmony export */   escapeWhitespace: () => (/* binding */ escapeWhitespace)\n/* harmony export */ });\nfunction clamp(left, x, right) {\n    return Math.max(left, Math.min(x, right));\n}\nfunction escapeWhitespace(str) {\n    return str.replace(/(\\t)|(\\r)|(\\n)/g, (m, t, r) => t ? '\\\\t' : r ? '\\\\r' : '\\\\n');\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9wZWJlcm1pbnRhL2xpYi91dGlsLm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFbUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcd2FsaWRcXERlc2t0b3BcXHByb2plY3RzXFxzaXRlIGNhaXNzb25cXHNvY2ktdC0tY2Fpc3Nvbi10dW5pc2llXFxub2RlX21vZHVsZXNcXHBlYmVybWludGFcXGxpYlxcdXRpbC5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gY2xhbXAobGVmdCwgeCwgcmlnaHQpIHtcbiAgICByZXR1cm4gTWF0aC5tYXgobGVmdCwgTWF0aC5taW4oeCwgcmlnaHQpKTtcbn1cbmZ1bmN0aW9uIGVzY2FwZVdoaXRlc3BhY2Uoc3RyKSB7XG4gICAgcmV0dXJuIHN0ci5yZXBsYWNlKC8oXFx0KXwoXFxyKXwoXFxuKS9nLCAobSwgdCwgcikgPT4gdCA/ICdcXFxcdCcgOiByID8gJ1xcXFxyJyA6ICdcXFxcbicpO1xufVxuXG5leHBvcnQgeyBjbGFtcCwgZXNjYXBlV2hpdGVzcGFjZSB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/peberminta/lib/util.mjs\n");

/***/ })

};
;