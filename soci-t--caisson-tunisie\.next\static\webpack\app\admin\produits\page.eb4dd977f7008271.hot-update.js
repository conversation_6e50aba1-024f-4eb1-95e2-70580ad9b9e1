"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/produits/page",{

/***/ "(app-pages-browser)/./app/admin/produits/page.tsx":
/*!*************************************!*\
  !*** ./app/admin/produits/page.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProductsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Edit_MoreHorizontal_Plus_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,MoreHorizontal,Plus,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_MoreHorizontal_Plus_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,MoreHorizontal,Plus,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/ellipsis.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_MoreHorizontal_Plus_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,MoreHorizontal,Plus,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_MoreHorizontal_Plus_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,MoreHorizontal,Plus,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./components/ui/table.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n// Sample product data\nconst initialProducts = [\n    {\n        id: 1,\n        name: \"Coffret Tunnel Volet Roulant\",\n        category: \"coffrets\",\n        status: \"active\"\n    },\n    {\n        id: 4,\n        name: \"Panneau Isolant 2cm\",\n        category: \"panneaux\",\n        status: \"active\"\n    },\n    {\n        id: 5,\n        name: \"Panneau Isolant 3cm\",\n        category: \"panneaux\",\n        status: \"active\"\n    },\n    {\n        id: 6,\n        name: \"Panneau Isolant Sur Mesure\",\n        category: \"panneaux\",\n        status: \"inactive\"\n    },\n    {\n        id: 7,\n        name: \"Fish Box Standard\",\n        category: \"fishbox\",\n        status: \"active\"\n    },\n    {\n        id: 8,\n        name: \"Fish Box Grande Capacité\",\n        category: \"fishbox\",\n        status: \"active\"\n    },\n    {\n        id: 9,\n        name: \"Caisson d'Emballage Personnalisé\",\n        category: \"fishbox\",\n        status: \"inactive\"\n    }\n];\nfunction ProductsPage() {\n    _s();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast)();\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialProducts);\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [categoryFilter, setCategoryFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProductsPage.useEffect\": ()=>{\n            const fetchData = {\n                \"ProductsPage.useEffect.fetchData\": async ()=>{\n                    try {\n                        setLoading(true);\n                        const [productsData, categoriesData] = await Promise.all([\n                            _lib_api__WEBPACK_IMPORTED_MODULE_10__.productApi.getAll(),\n                            _lib_api__WEBPACK_IMPORTED_MODULE_10__.categoryApi.getAll()\n                        ]);\n                        // Transform API data to match the expected format\n                        const transformedProducts = productsData.map({\n                            \"ProductsPage.useEffect.fetchData.transformedProducts\": (product)=>{\n                                var _product_category;\n                                return {\n                                    id: product._id,\n                                    name: product.name,\n                                    category: ((_product_category = product.category) === null || _product_category === void 0 ? void 0 : _product_category.slug) || 'unknown',\n                                    status: product.status === 'published' ? 'active' : 'inactive'\n                                };\n                            }\n                        }[\"ProductsPage.useEffect.fetchData.transformedProducts\"]);\n                        setProducts(transformedProducts.length > 0 ? transformedProducts : initialProducts);\n                        setCategories(categoriesData);\n                    } catch (err) {\n                        console.error('Error fetching data:', err);\n                        toast({\n                            title: \"Erreur\",\n                            description: \"Impossible de charger les données. Utilisation des données de démonstration.\",\n                            variant: \"destructive\"\n                        });\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"ProductsPage.useEffect.fetchData\"];\n            fetchData();\n        }\n    }[\"ProductsPage.useEffect\"], []);\n    const handleDelete = async (id)=>{\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_10__.productApi.delete(id);\n            setProducts(products.filter((product)=>product.id !== id));\n            toast({\n                title: \"Produit supprimé\",\n                description: \"Le produit a été supprimé avec succès.\"\n            });\n        } catch (err) {\n            console.error('Error deleting product:', err);\n            toast({\n                title: \"Erreur\",\n                description: \"Impossible de supprimer le produit.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleStatusChange = async (id, newStatus)=>{\n        try {\n            const apiStatus = newStatus === 'active' ? 'published' : 'draft';\n            await _lib_api__WEBPACK_IMPORTED_MODULE_10__.productApi.update(id, {\n                status: apiStatus\n            });\n            setProducts(products.map((product)=>product.id === id ? {\n                    ...product,\n                    status: newStatus\n                } : product));\n            toast({\n                title: \"Statut mis à jour\",\n                description: \"Le produit est maintenant \".concat(newStatus === \"active\" ? \"actif\" : \"inactif\", \".\")\n            });\n        } catch (err) {\n            console.error('Error updating product status:', err);\n            toast({\n                title: \"Erreur\",\n                description: \"Impossible de mettre à jour le statut du produit.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    // Filter products based on search term, category, and status\n    const filteredProducts = products.filter((product)=>{\n        const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase());\n        const matchesCategory = categoryFilter === \"all\" || product.category === categoryFilter;\n        const matchesStatus = statusFilter === \"all\" || product.status === statusFilter;\n        return matchesSearch && matchesCategory && matchesStatus;\n    });\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-[400px]\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                children: \"Chargement des produits...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                lineNumber: 154,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n            lineNumber: 153,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold\",\n                        children: \"Gestion des produits\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        asChild: true,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/admin/produits/ajouter\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_MoreHorizontal_Plus_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"mr-2 h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 13\n                                }, this),\n                                \" Ajouter un produit\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                lineNumber: 161,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                children: \"Filtres\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                children: \"Filtrer et rechercher des produits\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid gap-4 md:grid-cols-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                        placeholder: \"Rechercher un produit...\",\n                                        value: searchTerm,\n                                        onChange: (e)=>setSearchTerm(e.target.value)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                        value: categoryFilter,\n                                        onValueChange: setCategoryFilter,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                    placeholder: \"Cat\\xe9gorie\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                    lineNumber: 187,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                        value: \"all\",\n                                                        children: \"Toutes les cat\\xe9gories\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                        lineNumber: 190,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                        value: \"coffrets\",\n                                                        children: \"Coffrets tunnel pour volets roulants\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                        lineNumber: 191,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                        value: \"panneaux\",\n                                                        children: \"Panneaux Isolants\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                        lineNumber: 192,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                        value: \"fishbox\",\n                                                        children: \"Fish Box\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                        lineNumber: 193,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                        value: statusFilter,\n                                        onValueChange: setStatusFilter,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                    placeholder: \"Statut\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                    lineNumber: 200,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                        value: \"all\",\n                                                        children: \"Tous les statuts\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                        lineNumber: 203,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                        value: \"active\",\n                                                        children: \"Actif\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                        lineNumber: 204,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                        value: \"inactive\",\n                                                        children: \"Inactif\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                        lineNumber: 205,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                    lineNumber: 197,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                lineNumber: 170,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                children: \"Liste des produits\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                children: [\n                                    filteredProducts.length,\n                                    \" produit\",\n                                    filteredProducts.length > 1 ? \"s\" : \"\",\n                                    \" trouv\\xe9\",\n                                    filteredProducts.length > 1 ? \"s\" : \"\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.Table, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableRow, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                children: \"ID\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                children: \"Nom\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                children: \"Cat\\xe9gorie\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                children: \"Prix\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                children: \"Statut\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                lineNumber: 229,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                className: \"text-right\",\n                                                children: \"Actions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableBody, {\n                                    children: [\n                                        filteredProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableRow, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                        children: product.id\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                        lineNumber: 236,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                        className: \"font-medium\",\n                                                        children: product.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                        lineNumber: 237,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                        children: [\n                                                            product.category === \"coffrets\" && \"Coffrets tunnel pour volets roulants\",\n                                                            product.category === \"panneaux\" && \"Panneaux Isolants\",\n                                                            product.category === \"fishbox\" && \"Fish Box\",\n                                                            product.category === \"Polystyrène\" && \"polystyrène\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                        lineNumber: 238,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium \".concat(product.status === \"active\" ? \"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300\" : \"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300\"),\n                                                            children: product.status === \"active\" ? \"Actif\" : \"Inactif\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                            lineNumber: 246,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                        lineNumber: 245,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                        className: \"text-right\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenu, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuTrigger, {\n                                                                    asChild: true,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        size: \"icon\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_MoreHorizontal_Plus_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                                                lineNumber: 260,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"sr-only\",\n                                                                                children: \"Menu\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                                                lineNumber: 261,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                                        lineNumber: 259,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                                    lineNumber: 258,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuContent, {\n                                                                    align: \"end\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuItem, {\n                                                                            asChild: true,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                                href: \"/admin/produits/modifier/\".concat(product.id),\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_MoreHorizontal_Plus_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                        className: \"mr-2 h-4 w-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                                                        lineNumber: 267,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    \" Modifier\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                                                lineNumber: 266,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                                            lineNumber: 265,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuItem, {\n                                                                            onClick: ()=>handleStatusChange(product.id, product.status === \"active\" ? \"inactive\" : \"active\"),\n                                                                            children: product.status === \"active\" ? \"Désactiver\" : \"Activer\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                                            lineNumber: 270,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuItem, {\n                                                                            className: \"text-red-600 focus:text-red-600\",\n                                                                            onClick: ()=>handleDelete(product.id),\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_MoreHorizontal_Plus_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                    className: \"mr-2 h-4 w-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                                                    lineNumber: 281,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                \" Supprimer\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                                            lineNumber: 277,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                                    lineNumber: 264,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                            lineNumber: 257,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                        lineNumber: 256,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, product.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                lineNumber: 235,\n                                                columnNumber: 17\n                                            }, this)),\n                                        filteredProducts.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableRow, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                colSpan: 6,\n                                                className: \"text-center py-8 text-muted-foreground\",\n                                                children: \"Aucun produit trouv\\xe9\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                    lineNumber: 233,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                            lineNumber: 222,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                lineNumber: 213,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n        lineNumber: 160,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductsPage, \"YxIqmWbKgTGXkrt/sptUiaRPdO8=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast\n    ];\n});\n_c = ProductsPage;\nvar _c;\n$RefreshReg$(_c, \"ProductsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/admin/produits/page.tsx\n"));

/***/ })

});