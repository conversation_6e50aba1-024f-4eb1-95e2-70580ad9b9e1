import { TagsService } from './tags.service';
import { CreateTagDto } from './dto/create-tag.dto';
import { UpdateTagDto } from './dto/update-tag.dto';
export declare class TagsController {
    private readonly tagsService;
    constructor(tagsService: TagsService);
    create(createTagDto: CreateTagDto): Promise<import("../entities").Tag>;
    findAll(): Promise<import("../entities").Tag[]>;
    findOne(id: string): Promise<import("../entities").Tag>;
    update(id: string, updateTagDto: UpdateTagDto): Promise<import("../entities").Tag>;
    remove(id: string): Promise<{
        message: string;
    }>;
}
