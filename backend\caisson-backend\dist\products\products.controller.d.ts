import { ProductsService } from './products.service';
import { CreateProductDto } from './dto/create-product.dto';
import { UpdateProductDto } from './dto/update-product.dto';
export declare class ProductsController {
    private readonly productsService;
    constructor(productsService: ProductsService);
    create(createProductDto: CreateProductDto): Promise<import("../entities").Product>;
    findAll(): Promise<import("../entities").Product[]>;
    findBySlug(slug: string): Promise<import("../entities").Product>;
    findOne(id: string): Promise<import("../entities").Product>;
    update(id: string, updateProductDto: UpdateProductDto): Promise<import("../entities").Product>;
    remove(id: string): Promise<{
        message: string;
    }>;
}
