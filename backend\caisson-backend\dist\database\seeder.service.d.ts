import { Model } from 'mongoose';
import { ConfigService } from '@nestjs/config';
import { AuthService } from '../auth/auth.service';
import { UserDocument } from '../entities/user.entity';
export declare class SeederService {
    private userModel;
    private authService;
    private configService;
    private readonly logger;
    constructor(userModel: Model<UserDocument>, authService: AuthService, configService: ConfigService);
    seedAdminUser(): Promise<void>;
    seed(): Promise<void>;
}
