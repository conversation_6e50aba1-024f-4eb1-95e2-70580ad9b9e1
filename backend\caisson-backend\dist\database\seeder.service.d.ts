import { Model } from 'mongoose';
import { ConfigService } from '@nestjs/config';
import { AuthService } from '../auth/auth.service';
import { UserDocument } from '../entities/user.entity';
import { CategoryDocument } from '../entities/category.entity';
import { TagDocument } from '../entities/tag.entity';
import { ProductDocument } from '../entities/product.entity';
import { ReviewDocument } from '../entities/review.entity';
import { ProjectDocument } from '../entities/project.entity';
import { BlogPostDocument } from '../entities/blog-post.entity';
import { TestimonialDocument } from '../entities/testimonial.entity';
export declare class SeederService {
    private userModel;
    private categoryModel;
    private tagModel;
    private productModel;
    private reviewModel;
    private projectModel;
    private blogPostModel;
    private testimonialModel;
    private authService;
    private configService;
    private readonly logger;
    constructor(userModel: Model<UserDocument>, categoryModel: Model<CategoryDocument>, tagModel: Model<TagDocument>, productModel: Model<ProductDocument>, reviewModel: Model<ReviewDocument>, projectModel: Model<ProjectDocument>, blogPostModel: Model<BlogPostDocument>, testimonialModel: Model<TestimonialDocument>, authService: AuthService, configService: ConfigService);
    seedAdminUser(): Promise<void>;
    seedCategories(): Promise<void>;
    seedTags(): Promise<void>;
    seed(): Promise<void>;
}
