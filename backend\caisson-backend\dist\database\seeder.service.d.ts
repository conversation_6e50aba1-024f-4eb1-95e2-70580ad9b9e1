import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { AuthService } from '../auth/auth.service';
import { User } from '../entities/user.entity';
export declare class SeederService {
    private userRepository;
    private authService;
    private configService;
    private readonly logger;
    constructor(userRepository: Repository<User>, authService: AuthService, configService: ConfigService);
    seedAdminUser(): Promise<void>;
    seed(): Promise<void>;
}
