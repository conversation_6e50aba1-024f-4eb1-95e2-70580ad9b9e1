import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Tag, TagDocument } from '../entities/tag.entity';
import { CreateTagDto } from './dto/create-tag.dto';
import { UpdateTagDto } from './dto/update-tag.dto';

@Injectable()
export class TagsService {
  constructor(
    @InjectModel(Tag.name)
    private tagModel: Model<TagDocument>,
  ) {}

  async create(createTagDto: CreateTagDto): Promise<Tag> {
    // Check if tag with this name already exists
    const existingTag = await this.tagModel.findOne({
      name: createTagDto.name,
    }).exec();

    if (existingTag) {
      throw new BadRequestException('Tag with this name already exists');
    }

    const tag = new this.tagModel(createTagDto);
    return tag.save();
  }

  async findAll(): Promise<Tag[]> {
    return this.tagModel.find().sort({ createdAt: -1 }).exec();
  }

  async findOne(id: string): Promise<Tag> {
    const tag = await this.tagModel.findById(id).exec();

    if (!tag) {
      throw new NotFoundException('Tag not found');
    }

    return tag;
  }

  async update(id: string, updateTagDto: UpdateTagDto): Promise<Tag> {
    // Check if tag with this name already exists (excluding current tag)
    if (updateTagDto.name) {
      const existingTag = await this.tagModel.findOne({
        name: updateTagDto.name,
      }).exec();

      if (existingTag && (existingTag as any)._id.toString() !== id) {
        throw new BadRequestException('Tag with this name already exists');
      }
    }

    const tag = await this.tagModel.findByIdAndUpdate(
      id,
      updateTagDto,
      { new: true }
    ).exec();

    if (!tag) {
      throw new NotFoundException('Tag not found');
    }

    return tag;
  }

  async remove(id: string): Promise<void> {
    const tag = await this.tagModel.findById(id).exec();

    if (!tag) {
      throw new NotFoundException('Tag not found');
    }

    // Note: In MongoDB, we'll handle orphaned references in the products/blog services
    await this.tagModel.findByIdAndDelete(id).exec();
  }
}
