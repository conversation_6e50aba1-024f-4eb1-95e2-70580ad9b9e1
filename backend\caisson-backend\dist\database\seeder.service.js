"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var SeederService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.SeederService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const config_1 = require("@nestjs/config");
const auth_service_1 = require("../auth/auth.service");
const user_entity_1 = require("../entities/user.entity");
let SeederService = SeederService_1 = class SeederService {
    userRepository;
    authService;
    configService;
    logger = new common_1.Logger(SeederService_1.name);
    constructor(userRepository, authService, configService) {
        this.userRepository = userRepository;
        this.authService = authService;
        this.configService = configService;
    }
    async seedAdminUser() {
        const adminEmail = this.configService.get('ADMIN_EMAIL');
        const adminPassword = this.configService.get('ADMIN_PASSWORD');
        if (!adminEmail || !adminPassword) {
            this.logger.error('Admin email or password not configured');
            return;
        }
        const existingAdmin = await this.userRepository.findOne({
            where: { email: adminEmail },
        });
        if (existingAdmin) {
            this.logger.log('Admin user already exists');
            return;
        }
        try {
            await this.authService.createUser({
                email: adminEmail,
                name: 'Administrateur',
                password: adminPassword,
                role: 'admin',
                isActive: true,
            });
            this.logger.log('Admin user created successfully');
        }
        catch (error) {
            this.logger.error('Failed to create admin user', error);
        }
    }
    async seed() {
        this.logger.log('Starting database seeding...');
        await this.seedAdminUser();
        this.logger.log('Database seeding completed');
    }
};
exports.SeederService = SeederService;
exports.SeederService = SeederService = SeederService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        auth_service_1.AuthService,
        config_1.ConfigService])
], SeederService);
//# sourceMappingURL=seeder.service.js.map