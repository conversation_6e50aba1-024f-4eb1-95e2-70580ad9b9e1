"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var SeederService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.SeederService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const config_1 = require("@nestjs/config");
const auth_service_1 = require("../auth/auth.service");
const user_entity_1 = require("../entities/user.entity");
const category_entity_1 = require("../entities/category.entity");
const tag_entity_1 = require("../entities/tag.entity");
const product_entity_1 = require("../entities/product.entity");
const review_entity_1 = require("../entities/review.entity");
const project_entity_1 = require("../entities/project.entity");
const blog_post_entity_1 = require("../entities/blog-post.entity");
const testimonial_entity_1 = require("../entities/testimonial.entity");
let SeederService = SeederService_1 = class SeederService {
    userModel;
    categoryModel;
    tagModel;
    productModel;
    reviewModel;
    projectModel;
    blogPostModel;
    testimonialModel;
    authService;
    configService;
    logger = new common_1.Logger(SeederService_1.name);
    constructor(userModel, categoryModel, tagModel, productModel, reviewModel, projectModel, blogPostModel, testimonialModel, authService, configService) {
        this.userModel = userModel;
        this.categoryModel = categoryModel;
        this.tagModel = tagModel;
        this.productModel = productModel;
        this.reviewModel = reviewModel;
        this.projectModel = projectModel;
        this.blogPostModel = blogPostModel;
        this.testimonialModel = testimonialModel;
        this.authService = authService;
        this.configService = configService;
    }
    async seedAdminUser() {
        const adminEmail = this.configService.get('ADMIN_EMAIL');
        const adminPassword = this.configService.get('ADMIN_PASSWORD');
        if (!adminEmail || !adminPassword) {
            this.logger.error('Admin email or password not configured');
            return;
        }
        const existingAdmin = await this.userModel.findOne({
            email: adminEmail,
        }).exec();
        if (existingAdmin) {
            this.logger.log('Admin user already exists');
            return;
        }
        try {
            await this.authService.createUser({
                email: adminEmail,
                name: 'Administrateur',
                password: adminPassword,
                role: 'admin',
                isActive: true,
            });
            this.logger.log('Admin user created successfully');
        }
        catch (error) {
            this.logger.error('Failed to create admin user', error);
        }
    }
    async seedCategories() {
        const existingCategories = await this.categoryModel.countDocuments();
        if (existingCategories > 0) {
            this.logger.log('Categories already exist, skipping...');
            return;
        }
        const categories = [
            {
                name: "Coffrets tunnel pour volets roulants",
                slug: "coffrets",
                description: "Coffrets tunnel pour volets roulants et autres applications",
            },
            {
                name: "Panneaux Isolants",
                slug: "panneaux",
                description: "Panneaux isolants en polystyrène expansé pour l'isolation thermique",
            },
            {
                name: "Fish Box",
                slug: "fishbox",
                description: "Caissons d'emballage isothermes pour le transport de produits frais",
            },
        ];
        await this.categoryModel.insertMany(categories);
        this.logger.log('Categories seeded successfully');
    }
    async seedTags() {
        const existingTags = await this.tagModel.countDocuments();
        if (existingTags > 0) {
            this.logger.log('Tags already exist, skipping...');
            return;
        }
        const tags = [
            { name: "isolation" },
            { name: "thermique" },
            { name: "polystyrène" },
            { name: "EPS" },
            { name: "volet roulant" },
            { name: "coffret tunnel" },
            { name: "construction" },
            { name: "bâtiment" },
            { name: "isotherme" },
            { name: "transport" },
            { name: "innovation" },
            { name: "efficacité énergétique" },
        ];
        await this.tagModel.insertMany(tags);
        this.logger.log('Tags seeded successfully');
    }
    async seed() {
        this.logger.log('Starting database seeding...');
        await this.seedAdminUser();
        await this.seedCategories();
        await this.seedTags();
        this.logger.log('Database seeding completed');
    }
};
exports.SeederService = SeederService;
exports.SeederService = SeederService = SeederService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(user_entity_1.User.name)),
    __param(1, (0, mongoose_1.InjectModel)(category_entity_1.Category.name)),
    __param(2, (0, mongoose_1.InjectModel)(tag_entity_1.Tag.name)),
    __param(3, (0, mongoose_1.InjectModel)(product_entity_1.Product.name)),
    __param(4, (0, mongoose_1.InjectModel)(review_entity_1.Review.name)),
    __param(5, (0, mongoose_1.InjectModel)(project_entity_1.Project.name)),
    __param(6, (0, mongoose_1.InjectModel)(blog_post_entity_1.BlogPost.name)),
    __param(7, (0, mongoose_1.InjectModel)(testimonial_entity_1.Testimonial.name)),
    __metadata("design:paramtypes", [mongoose_2.Model,
        mongoose_2.Model,
        mongoose_2.Model,
        mongoose_2.Model,
        mongoose_2.Model,
        mongoose_2.Model,
        mongoose_2.Model,
        mongoose_2.Model,
        auth_service_1.AuthService,
        config_1.ConfigService])
], SeederService);
//# sourceMappingURL=seeder.service.js.map