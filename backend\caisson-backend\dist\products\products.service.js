"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a, _b, _c;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProductsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const product_entity_1 = require("../entities/product.entity");
const category_entity_1 = require("../entities/category.entity");
const tag_entity_1 = require("../entities/tag.entity");
let ProductsService = class ProductsService {
    productRepository;
    categoryRepository;
    tagRepository;
    constructor(productRepository, categoryRepository, tagRepository) {
        this.productRepository = productRepository;
        this.categoryRepository = categoryRepository;
        this.tagRepository = tagRepository;
    }
    async create(createProductDto) {
        const { categoryId, tagIds, ...productData } = createProductDto;
        const existingProduct = await this.productRepository.findOne({
            where: { slug: createProductDto.slug },
        });
        if (existingProduct) {
            throw new common_1.BadRequestException('Product with this slug already exists');
        }
        const category = await this.categoryRepository.findOne({
            where: { id: categoryId },
        });
        if (!category) {
            throw new common_1.NotFoundException('Category not found');
        }
        let tags = [];
        if (tagIds && tagIds.length > 0) {
            tags = await this.tagRepository.findBy({
                id: (0, typeorm_2.In)(tagIds),
            });
            if (tags.length !== tagIds.length) {
                throw new common_1.NotFoundException('One or more tags not found');
            }
        }
        const product = this.productRepository.create({
            ...productData,
            category,
            tags,
        });
        return this.productRepository.save(product);
    }
    async findAll() {
        return this.productRepository.find({
            relations: ['category', 'tags'],
            order: { createdAt: 'DESC' },
        });
    }
    async findOne(id) {
        const product = await this.productRepository.findOne({
            where: { id },
            relations: ['category', 'tags', 'reviews', 'technicalSheets'],
        });
        if (!product) {
            throw new common_1.NotFoundException('Product not found');
        }
        return product;
    }
    async findBySlug(slug) {
        const product = await this.productRepository.findOne({
            where: { slug },
            relations: ['category', 'tags', 'reviews', 'technicalSheets'],
        });
        if (!product) {
            throw new common_1.NotFoundException('Product not found');
        }
        return product;
    }
    async update(id, updateProductDto) {
        const product = await this.findOne(id);
        const { categoryId, tagIds, ...productData } = updateProductDto;
        if (updateProductDto.slug) {
            const existingProduct = await this.productRepository.findOne({
                where: { slug: updateProductDto.slug },
            });
            if (existingProduct && existingProduct.id !== id) {
                throw new common_1.BadRequestException('Product with this slug already exists');
            }
        }
        if (categoryId) {
            const category = await this.categoryRepository.findOne({
                where: { id: categoryId },
            });
            if (!category) {
                throw new common_1.NotFoundException('Category not found');
            }
            product.category = category;
        }
        if (tagIds !== undefined) {
            if (tagIds.length > 0) {
                const tags = await this.tagRepository.findBy({
                    id: (0, typeorm_2.In)(tagIds),
                });
                if (tags.length !== tagIds.length) {
                    throw new common_1.NotFoundException('One or more tags not found');
                }
                product.tags = tags;
            }
            else {
                product.tags = [];
            }
        }
        Object.assign(product, productData);
        return this.productRepository.save(product);
    }
    async remove(id) {
        const product = await this.findOne(id);
        await this.productRepository.remove(product);
    }
    async updateRating(productId) {
        const result = await this.productRepository
            .createQueryBuilder('product')
            .leftJoin('product.reviews', 'review')
            .select('AVG(review.rating)', 'avgRating')
            .addSelect('COUNT(review.id)', 'reviewCount')
            .where('product.id = :productId', { productId })
            .getRawOne();
        await this.productRepository.update(productId, {
            avgRating: parseFloat(result.avgRating) || 0,
            reviewCount: parseInt(result.reviewCount) || 0,
        });
    }
};
exports.ProductsService = ProductsService;
exports.ProductsService = ProductsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(product_entity_1.Product)),
    __param(1, (0, typeorm_1.InjectRepository)(category_entity_1.Category)),
    __param(2, (0, typeorm_1.InjectRepository)(tag_entity_1.Tag)),
    __metadata("design:paramtypes", [typeof (_a = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _a : Object, typeof (_b = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _b : Object, typeof (_c = typeof typeorm_2.Repository !== "undefined" && typeorm_2.Repository) === "function" ? _c : Object])
], ProductsService);
//# sourceMappingURL=products.service.js.map