"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProductsService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const product_entity_1 = require("../entities/product.entity");
const category_entity_1 = require("../entities/category.entity");
const tag_entity_1 = require("../entities/tag.entity");
let ProductsService = class ProductsService {
    productModel;
    categoryModel;
    tagModel;
    constructor(productModel, categoryModel, tagModel) {
        this.productModel = productModel;
        this.categoryModel = categoryModel;
        this.tagModel = tagModel;
    }
    async create(createProductDto) {
        const { categoryId, tagIds, ...productData } = createProductDto;
        const existingProduct = await this.productModel.findOne({
            slug: createProductDto.slug,
        }).exec();
        if (existingProduct) {
            throw new common_1.BadRequestException('Product with this slug already exists');
        }
        const category = await this.categoryModel.findById(categoryId).exec();
        if (!category) {
            throw new common_1.NotFoundException('Category not found');
        }
        let tagObjectIds = [];
        if (tagIds && tagIds.length > 0) {
            const tags = await this.tagModel.find({
                _id: { $in: tagIds.map(id => new mongoose_2.Types.ObjectId(id)) }
            }).exec();
            if (tags.length !== tagIds.length) {
                throw new common_1.NotFoundException('One or more tags not found');
            }
            tagObjectIds = tags.map(tag => tag._id);
        }
        const product = new this.productModel({
            ...productData,
            category: new mongoose_2.Types.ObjectId(categoryId),
            tags: tagObjectIds,
        });
        return product.save();
    }
    async findAll() {
        return this.productModel.find()
            .populate('category')
            .populate('tags')
            .sort({ createdAt: -1 })
            .exec();
    }
    async findOne(id) {
        const product = await this.productModel.findById(id)
            .populate('category')
            .populate('tags')
            .exec();
        if (!product) {
            throw new common_1.NotFoundException('Product not found');
        }
        return product;
    }
    async findBySlug(slug) {
        const product = await this.productModel.findOne({ slug })
            .populate('category')
            .populate('tags')
            .exec();
        if (!product) {
            throw new common_1.NotFoundException('Product not found');
        }
        return product;
    }
    async update(id, updateProductDto) {
        const { categoryId, tagIds, ...productData } = updateProductDto;
        if (updateProductDto.slug) {
            const existingProduct = await this.productModel.findOne({
                slug: updateProductDto.slug,
            }).exec();
            if (existingProduct && existingProduct._id.toString() !== id) {
                throw new common_1.BadRequestException('Product with this slug already exists');
            }
        }
        const updateData = { ...productData };
        if (categoryId) {
            const category = await this.categoryModel.findById(categoryId).exec();
            if (!category) {
                throw new common_1.NotFoundException('Category not found');
            }
            updateData.category = new mongoose_2.Types.ObjectId(categoryId);
        }
        if (tagIds !== undefined) {
            if (tagIds.length > 0) {
                const tags = await this.tagModel.find({
                    _id: { $in: tagIds.map(id => new mongoose_2.Types.ObjectId(id)) }
                }).exec();
                if (tags.length !== tagIds.length) {
                    throw new common_1.NotFoundException('One or more tags not found');
                }
                updateData.tags = tags.map(tag => tag._id);
            }
            else {
                updateData.tags = [];
            }
        }
        const product = await this.productModel.findByIdAndUpdate(id, updateData, { new: true }).populate('category').populate('tags').exec();
        if (!product) {
            throw new common_1.NotFoundException('Product not found');
        }
        return product;
    }
    async remove(id) {
        const product = await this.productModel.findById(id).exec();
        if (!product) {
            throw new common_1.NotFoundException('Product not found');
        }
        await this.productModel.findByIdAndDelete(id).exec();
    }
    async updateRating(productId) {
        const product = await this.productModel.findById(productId).exec();
        if (!product) {
            throw new common_1.NotFoundException('Product not found');
        }
        await this.productModel.findByIdAndUpdate(productId, {
            avgRating: 0,
            reviewCount: 0,
        }).exec();
    }
};
exports.ProductsService = ProductsService;
exports.ProductsService = ProductsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(product_entity_1.Product.name)),
    __param(1, (0, mongoose_1.InjectModel)(category_entity_1.Category.name)),
    __param(2, (0, mongoose_1.InjectModel)(tag_entity_1.Tag.name)),
    __metadata("design:paramtypes", [mongoose_2.Model,
        mongoose_2.Model,
        mongoose_2.Model])
], ProductsService);
//# sourceMappingURL=products.service.js.map