{"version": 3, "sources": ["../browser/src/driver/spanner/SpannerConnectionOptions.ts"], "names": [], "mappings": "", "file": "SpannerConnectionOptions.js", "sourcesContent": ["import { BaseConnectionOptions } from \"../../connection/BaseConnectionOptions\"\nimport { ReplicationMode } from \"../types/ReplicationMode\"\nimport { SpannerConnectionCredentialsOptions } from \"./SpannerConnectionCredentialsOptions\"\n\n/**\n * Spanner specific connection options.\n */\nexport interface SpannerConnectionOptions\n    extends BaseConnectionOptions,\n        SpannerConnectionCredentialsOptions {\n    /**\n     * Database type.\n     */\n    readonly type: \"spanner\"\n\n    /**\n     * The driver object\n     * This defaults to require(\"@google-cloud/spanner\").\n     */\n    readonly driver?: any\n\n    // todo\n    readonly database?: string\n\n    // todo\n    readonly schema?: string\n\n    /**\n     * The charset for the connection. This is called \"collation\" in the SQL-level of MySQL (like utf8_general_ci).\n     * If a SQL-level charset is specified (like utf8mb4) then the default collation for that charset is used.\n     * Default: 'UTF8_GENERAL_CI'\n     */\n    readonly charset?: string\n\n    /**\n     * The timezone configured on the MySQL server.\n     * This is used to type cast server date/time values to JavaScript Date object and vice versa.\n     * This can be 'local', 'Z', or an offset in the form +HH:MM or -HH:MM. (Default: 'local')\n     */\n    readonly timezone?: string\n\n    /**\n     * The milliseconds before a timeout occurs during the initial connection to the MySQL server. (Default: 10000)\n     */\n    readonly connectTimeout?: number\n\n    /**\n     * The milliseconds before a timeout occurs during the initial connection to the MySQL server. (Default: 10000)\n     * This difference between connectTimeout and acquireTimeout is subtle and is described in the mysqljs/mysql docs\n     * https://github.com/mysqljs/mysql/tree/master#pool-options\n     */\n    readonly acquireTimeout?: number\n\n    /**\n     * Allow connecting to MySQL instances that ask for the old (insecure) authentication method. (Default: false)\n     */\n    readonly insecureAuth?: boolean\n\n    /**\n     * When dealing with big numbers (BIGINT and DECIMAL columns) in the database, you should enable this option (Default: false)\n     */\n    readonly supportBigNumbers?: boolean\n\n    /**\n     * Enabling both supportBigNumbers and bigNumberStrings forces big numbers (BIGINT and DECIMAL columns) to be always\n     * returned as JavaScript String objects (Default: false). Enabling supportBigNumbers but leaving bigNumberStrings\n     * disabled will return big numbers as String objects only when they cannot be accurately represented with\n     * [JavaScript Number objects](http://ecma262-5.com/ELS5_HTML.htm#Section_8.5) (which happens when they exceed the [-2^53, +2^53] range),\n     * otherwise they will be returned as Number objects. This option is ignored if supportBigNumbers is disabled.\n     */\n    readonly bigNumberStrings?: boolean\n\n    /**\n     * Force date types (TIMESTAMP, DATETIME, DATE) to be returned as strings rather then inflated into JavaScript Date objects.\n     * Can be true/false or an array of type names to keep as strings.\n     */\n    readonly dateStrings?: boolean | string[]\n\n    /**\n     * Prints protocol details to stdout. Can be true/false or an array of packet type names that should be printed.\n     * (Default: false)\n     */\n    readonly debug?: boolean | string[]\n\n    /**\n     * Generates stack traces on Error to include call site of library entrance (\"long stack traces\").\n     * Slight performance penalty for most calls. (Default: true)\n     */\n    readonly trace?: boolean\n\n    /**\n     * Allow multiple mysql statements per query. Be careful with this, it could increase the scope of SQL injection attacks.\n     * (Default: false)\n     */\n    readonly multipleStatements?: boolean\n\n    /**\n     * Use spatial functions like GeomFromText and AsText which are removed in MySQL 8.\n     * (Default: true)\n     */\n    readonly legacySpatialSupport?: boolean\n\n    /**\n     * List of connection flags to use other than the default ones. It is also possible to blacklist default ones.\n     * For more information, check https://github.com/mysqljs/mysql#connection-flags.\n     */\n    readonly flags?: string[]\n\n    /**\n     * Replication setup.\n     */\n    readonly replication?: {\n        /**\n         * Master server used by orm to perform writes.\n         */\n        readonly master: SpannerConnectionCredentialsOptions\n\n        /**\n         * List of read-from servers (slaves).\n         */\n        readonly slaves: SpannerConnectionCredentialsOptions[]\n\n        /**\n         * If true, PoolCluster will attempt to reconnect when connection fails. (Default: true)\n         */\n        readonly canRetry?: boolean\n\n        /**\n         * If connection fails, node's errorCount increases.\n         * When errorCount is greater than removeNodeErrorCount, remove a node in the PoolCluster. (Default: 5)\n         */\n        readonly removeNodeErrorCount?: number\n\n        /**\n         * If connection fails, specifies the number of milliseconds before another connection attempt will be made.\n         * If set to 0, then node will be removed instead and never re-used. (Default: 0)\n         */\n        readonly restoreNodeTimeout?: number\n\n        /**\n         * Determines how slaves are selected:\n         * RR: Select one alternately (Round-Robin).\n         * RANDOM: Select the node by random function.\n         * ORDER: Select the first node available unconditionally.\n         */\n        readonly selector?: \"RR\" | \"RANDOM\" | \"ORDER\"\n\n        /**\n         * Default connection pool to use for SELECT queries\n         * @default \"slave\"\n         */\n        readonly defaultMode?: ReplicationMode\n    }\n\n    readonly poolSize?: never\n}\n"], "sourceRoot": "../.."}