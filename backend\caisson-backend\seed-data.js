// Simple script to seed the database with sample data
const axios = require('axios');

const API_BASE = 'http://localhost:3002';

async function login() {
  try {
    const response = await axios.post(`${API_BASE}/auth/login`, {
      email: '<EMAIL>',
      password: 'admin123'
    });
    return response.data.access_token;
  } catch (error) {
    console.error('Login failed:', error.message);
    throw error;
  }
}

async function createProduct(token, productData) {
  try {
    const response = await axios.post(`${API_BASE}/products`, productData, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    return response.data;
  } catch (error) {
    console.error('Failed to create product:', error.response?.data || error.message);
    throw error;
  }
}

async function getCategories() {
  try {
    const response = await axios.get(`${API_BASE}/categories`);
    return response.data;
  } catch (error) {
    console.error('Failed to get categories:', error.message);
    throw error;
  }
}

async function getTags() {
  try {
    const response = await axios.get(`${API_BASE}/tags`);
    return response.data;
  } catch (error) {
    console.error('Failed to get tags:', error.message);
    throw error;
  }
}

async function seedProducts() {
  try {
    console.log('Starting data seeding...');
    
    // Login to get token
    const token = await login();
    console.log('✅ Logged in successfully');
    
    // Get categories and tags
    const categories = await getCategories();
    const tags = await getTags();
    console.log(`✅ Found ${categories.length} categories and ${tags.length} tags`);
    
    // Find specific categories and tags
    const coffretCategory = categories.find(c => c.slug === 'coffrets');
    const panneauxCategory = categories.find(c => c.slug === 'panneaux');
    const fishboxCategory = categories.find(c => c.slug === 'fishbox');
    
    const isolationTag = tags.find(t => t.name === 'isolation');
    const thermiqueTag = tags.find(t => t.name === 'thermique');
    const polystyreneTag = tags.find(t => t.name === 'polystyrène');
    const voletTag = tags.find(t => t.name === 'volet roulant');
    const isothermeTag = tags.find(t => t.name === 'isotherme');
    
    // Sample products
    const products = [
      {
        name: "Coffret Tunnel Volet Roulant",
        slug: "coffret-tunnel-volet-roulant",
        description: "Solution légère et isolante pour l'installation de volets roulants.",
        longDescription: "<p>Le Coffret Tunnel Volet Roulant en polystyrène expansé (EPS) est une solution innovante qui combine légèreté et performance d'isolation thermique. Conçu pour faciliter l'installation des volets roulants, ce coffret tunnel offre une excellente isolation thermique et acoustique, contribuant ainsi à l'efficacité énergétique globale du bâtiment.</p>",
        images: ["/placeholder.svg?height=600&width=800"],
        inStock: true,
        features: [
          "Légèreté et facilité d'installation",
          "Excellente isolation thermique",
          "Résistance à l'humidité",
          "Durabilité et stabilité dimensionnelle"
        ],
        specifications: {
          "Matériau": "Polystyrène expansé (EPS)",
          "Densité": "30 kg/m³",
          "Conductivité thermique": "0,032 W/mK",
          "Résistance à la compression": "150 kPa",
          "Classement au feu": "Euroclasse E",
          "Dimensions standard": "Variable selon application"
        },
        category: coffretCategory?._id,
        tags: [isolationTag?._id, thermiqueTag?._id, polystyreneTag?._id, voletTag?._id].filter(Boolean),
        avgRating: 4.5,
        reviewCount: 2,
        status: "published"
      },
      {
        name: "Panneau Isolant EPS 100",
        slug: "panneau-isolant-eps-100",
        description: "Panneau isolant en polystyrène expansé pour l'isolation thermique des bâtiments.",
        longDescription: "<p>Le Panneau Isolant EPS 100 est conçu pour répondre aux exigences les plus strictes en matière d'isolation thermique dans le secteur du bâtiment. Avec sa densité de 20 kg/m³ et sa conductivité thermique exceptionnelle de 0,036 W/mK, ce panneau offre une performance d'isolation remarquable tout en conservant une légèreté qui facilite sa manipulation et son installation.</p>",
        images: ["/placeholder.svg?height=600&width=800"],
        inStock: true,
        features: [
          "Excellente isolation thermique",
          "Résistance à l'humidité",
          "Légèreté et facilité d'installation",
          "Durabilité et stabilité dimensionnelle"
        ],
        specifications: {
          "Matériau": "Polystyrène expansé (EPS)",
          "Densité": "20 kg/m³",
          "Conductivité thermique": "0,036 W/mK",
          "Résistance à la compression": "100 kPa",
          "Classement au feu": "Euroclasse E",
          "Dimensions standard": "1200 x 600 mm"
        },
        category: panneauxCategory?._id,
        tags: [isolationTag?._id, thermiqueTag?._id, polystyreneTag?._id].filter(Boolean),
        avgRating: 5.0,
        reviewCount: 1,
        status: "published"
      },
      {
        name: "Fish Box Isotherme",
        slug: "fish-box-isotherme",
        description: "Caisson isotherme pour le transport et la conservation des produits frais.",
        longDescription: "<p>La Fish Box Isotherme est spécialement conçue pour le transport et la conservation des produits de la mer et autres denrées périssables. Fabriquée en polystyrène expansé de haute densité, elle garantit une isolation thermique optimale, maintenant la chaîne du froid pendant de longues heures.</p>",
        images: ["/placeholder.svg?height=600&width=800"],
        inStock: true,
        features: [
          "Isolation thermique optimale",
          "Résistance aux chocs",
          "Étanchéité parfaite",
          "Facilité de nettoyage"
        ],
        specifications: {
          "Matériau": "Polystyrène expansé (EPS)",
          "Densité": "25 kg/m³",
          "Capacité": "Variable (10L à 100L)",
          "Température de conservation": "-20°C à +80°C",
          "Durée d'isolation": "24-48h selon modèle"
        },
        category: fishboxCategory?._id,
        tags: [isothermeTag?._id, polystyreneTag?._id].filter(Boolean),
        avgRating: 5.0,
        reviewCount: 1,
        status: "published"
      }
    ];
    
    // Create products
    for (const productData of products) {
      try {
        const product = await createProduct(token, productData);
        console.log(`✅ Created product: ${product.name}`);
      } catch (error) {
        console.error(`❌ Failed to create product: ${productData.name}`);
      }
    }
    
    console.log('🎉 Data seeding completed!');
    
  } catch (error) {
    console.error('❌ Seeding failed:', error.message);
  }
}

// Run the seeding
seedProducts();
