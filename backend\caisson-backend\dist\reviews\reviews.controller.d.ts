import { ReviewsService } from './reviews.service';
import { CreateReviewDto } from './dto/create-review.dto';
import { UpdateReviewDto } from './dto/update-review.dto';
export declare class ReviewsController {
    private readonly reviewsService;
    constructor(reviewsService: ReviewsService);
    create(createReviewDto: CreateReviewDto): Promise<import("../entities").Review>;
    findAll(): Promise<import("../entities").Review[]>;
    findByProduct(productId: string): Promise<import("../entities").Review[]>;
    getProductRatingStats(productId: string): Promise<{
        totalReviews: any;
        averageRating: number;
        ratingDistribution: any;
    }>;
    findOne(id: string): Promise<import("../entities").Review>;
    update(id: string, updateReviewDto: UpdateReviewDto): Promise<import("../entities").Review>;
    remove(id: string): Promise<{
        message: string;
    }>;
}
