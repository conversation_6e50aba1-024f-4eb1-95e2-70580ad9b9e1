import { Model } from 'mongoose';
import { Product, ProductDocument } from '../entities/product.entity';
import { CategoryDocument } from '../entities/category.entity';
import { TagDocument } from '../entities/tag.entity';
import { CreateProductDto } from './dto/create-product.dto';
import { UpdateProductDto } from './dto/update-product.dto';
export declare class ProductsService {
    private productModel;
    private categoryModel;
    private tagModel;
    constructor(productModel: Model<ProductDocument>, categoryModel: Model<CategoryDocument>, tagModel: Model<TagDocument>);
    create(createProductDto: CreateProductDto): Promise<Product>;
    findAll(): Promise<Product[]>;
    findOne(id: string): Promise<Product>;
    findBySlug(slug: string): Promise<Product>;
    update(id: string, updateProductDto: UpdateProductDto): Promise<Product>;
    remove(id: string): Promise<void>;
    updateRating(productId: string): Promise<void>;
}
