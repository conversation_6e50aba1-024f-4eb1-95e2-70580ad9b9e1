{"version": 3, "sources": ["../../src/driver/expo/legacy/ExpoLegacyQueryRunner.ts"], "names": [], "mappings": ";;;AAAA,oGAAgG;AAChG,sEAAkE;AAClE,+FAA2F;AAC3F,0FAAsF;AAEtF,iEAA6D;AAC7D,mEAA+D;AAC/D,6EAAyE;AAqBzE;;GAEG;AACH,MAAa,qBAAsB,SAAQ,qDAAyB;IAWhE,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YAAY,MAAwB;QAChC,KAAK,EAAE,CAAA;QACP,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;QACpB,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,UAAU,CAAA;QACnC,IAAI,CAAC,WAAW,GAAG,IAAI,yBAAW,CAAC,IAAI,CAAC,CAAA;IAC5C,CAAC;IAED;;;;;;;;;;OAUG;IACH,KAAK,CAAC,gBAAgB;QAClB,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAA;QAC/B,IAAI,CAAC;YACD,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAA;QAC9D,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACX,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAA;YAChC,MAAM,GAAG,CAAA;QACb,CAAC;QAED,IAAI,CAAC,gBAAgB,IAAI,CAAC,CAAA;QAE1B,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,uBAAuB,CAAC,CAAA;IAC7D,CAAC;IAED;;;;;;;OAOG;IACH,KAAK,CAAC,iBAAiB;QACnB,IACI,CAAC,IAAI,CAAC,mBAAmB;YACzB,OAAO,IAAI,CAAC,WAAW,KAAK,WAAW;YAEvC,MAAM,IAAI,uDAA0B,EAAE,CAAA;QAE1C,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,yBAAyB,CAAC,CAAA;QAE3D,IAAI,CAAC,WAAW,GAAG,SAAS,CAAA;QAC5B,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAA;QAEhC,IAAI,CAAC,gBAAgB,IAAI,CAAC,CAAA;QAE1B,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAA;IAC9D,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,mBAAmB;QACrB,IACI,CAAC,IAAI,CAAC,mBAAmB;YACzB,OAAO,IAAI,CAAC,WAAW,KAAK,WAAW;YAEvC,MAAM,IAAI,uDAA0B,EAAE,CAAA;QAE1C,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,2BAA2B,CAAC,CAAA;QAE7D,IAAI,CAAC,WAAW,GAAG,SAAS,CAAA;QAC5B,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAA;QAEhC,IAAI,CAAC,gBAAgB,IAAI,CAAC,CAAA;QAE1B,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAA;IAChE,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe;QACjB,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE,CAAA;QAC/C,OAAO,IAAI,OAAO,CAAC,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE;YAC5B,kBAAkB,CAAC,IAAI,CACnB,CAAC,EAAE,GAAG,EAAE,2BAA2B,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,EAChD,KAAK,EACL,CAAC,GAAQ,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CACzC,CAAA;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc;QAChB,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE,CAAA;QAC/C,OAAO,IAAI,OAAO,CAAC,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE;YAC5B,kBAAkB,CAAC,IAAI,CACnB,CAAC,EAAE,GAAG,EAAE,0BAA0B,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,EAC/C,KAAK,EACL,CAAC,GAAQ,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CACzC,CAAA;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,KAAK,CACP,KAAa,EACb,UAAkB,EAClB,mBAAmB,GAAG,KAAK;QAE3B,IAAI,IAAI,CAAC,UAAU;YAAE,MAAM,IAAI,iEAA+B,EAAE,CAAA;QAEhE,OAAO,IAAI,OAAO,CAAM,KAAK,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE;YACvC,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE,CAAA;YAC/C,MAAM,iBAAiB,GAAG,IAAI,qCAAiB,EAAE,CAAA;YAEjD,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,UAAU,EAAE,IAAI,CAAC,CAAA;YAC/D,IAAI,CAAC,WAAW,CAAC,yBAAyB,CACtC,iBAAiB,EACjB,KAAK,EACL,UAAU,CACb,CAAA;YAED,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;YACjC,6DAA6D;YAC7D,kBAAkB,CAAC,WAAW,CAC1B,KAAK,EAAE,WAAyB,EAAE,EAAE;gBAChC,IAAI,OAAO,IAAI,CAAC,WAAW,KAAK,WAAW,EAAE,CAAC;oBAC1C,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAA;oBAC7B,IAAI,CAAC,WAAW,GAAG,WAAW,CAAA;gBAClC,CAAC;gBACD,IAAI,CAAC,WAAW,CAAC,UAAU,CACvB,KAAK,EACL,UAAU,EACV,KAAK,EAAE,CAAe,EAAE,GAAe,EAAE,EAAE;oBACvC,oDAAoD;oBACpD,MAAM,qBAAqB,GACvB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,qBAAqB,CAAA;oBAC7C,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;oBAC/B,MAAM,kBAAkB,GACpB,YAAY,GAAG,cAAc,CAAA;oBAEjC,IAAI,CAAC,WAAW,CAAC,wBAAwB,CACrC,iBAAiB,EACjB,KAAK,EACL,UAAU,EACV,IAAI,EACJ,kBAAkB,EAClB,GAAG,EACH,SAAS,CACZ,CAAA;oBACD,MAAM,iBAAiB,CAAC,IAAI,EAAE,CAAA;oBAE9B,IACI,qBAAqB;wBACrB,kBAAkB,GAAG,qBAAqB,EAC5C,CAAC;wBACC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,YAAY,CACtC,kBAAkB,EAClB,KAAK,EACL,UAAU,EACV,IAAI,CACP,CAAA;oBACL,CAAC;oBAED,MAAM,MAAM,GAAG,IAAI,yBAAW,EAAE,CAAA;oBAEhC,IAAI,GAAG,EAAE,cAAc,CAAC,cAAc,CAAC,EAAE,CAAC;wBACtC,MAAM,CAAC,QAAQ,GAAG,GAAG,CAAC,YAAY,CAAA;oBACtC,CAAC;oBAED,IAAI,GAAG,EAAE,cAAc,CAAC,MAAM,CAAC,EAAE,CAAC;wBAC9B,IAAI,SAAS,GAAG,EAAE,CAAA;wBAClB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;4BACvC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAA;wBACpC,CAAC;wBAED,MAAM,CAAC,GAAG,GAAG,SAAS,CAAA;wBACtB,MAAM,CAAC,OAAO,GAAG,SAAS,CAAA;oBAC9B,CAAC;oBAED,4DAA4D;oBAC5D,IAAI,KAAK,CAAC,UAAU,CAAC,aAAa,CAAC,EAAE,CAAC;wBAClC,MAAM,CAAC,GAAG,GAAG,GAAG,CAAC,QAAQ,CAAA;oBAC7B,CAAC;oBAED,IAAI,mBAAmB,EAAE,CAAC;wBACtB,EAAE,CAAC,MAAM,CAAC,CAAA;oBACd,CAAC;yBAAM,CAAC;wBACJ,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;oBAClB,CAAC;gBACL,CAAC,EACD,KAAK,EAAE,CAAe,EAAE,GAAQ,EAAE,EAAE;oBAChC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,aAAa,CACvC,GAAG,EACH,KAAK,EACL,UAAU,EACV,IAAI,CACP,CAAA;oBACD,IAAI,CAAC,WAAW,CAAC,wBAAwB,CACrC,iBAAiB,EACjB,KAAK,EACL,UAAU,EACV,KAAK,EACL,SAAS,EACT,SAAS,EACT,GAAG,CACN,CAAA;oBACD,MAAM,iBAAiB,CAAC,IAAI,EAAE,CAAA;oBAE9B,IAAI,CAAC,IAAI,mCAAgB,CAAC,KAAK,EAAE,UAAU,EAAE,GAAG,CAAC,CAAC,CAAA;gBACtD,CAAC,CACJ,CAAA;YACL,CAAC,EACD,KAAK,EAAE,GAAQ,EAAE,EAAE;gBACf,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAA;gBAChC,IAAI,CAAC,GAAG,CAAC,CAAA;YACb,CAAC,EACD,GAAG,EAAE;gBACD,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAA;gBAChC,IAAI,CAAC,WAAW,GAAG,SAAS,CAAA;YAChC,CAAC,CACJ,CAAA;QACL,CAAC,CAAC,CAAA;IACN,CAAC;CACJ;AAvPD,sDAuPC", "file": "ExpoLegacyQueryRunner.js", "sourcesContent": ["import { QueryRunnerAlreadyReleasedError } from \"../../../error/QueryRunnerAlreadyReleasedError\"\nimport { QueryFailedError } from \"../../../error/QueryFailedError\"\nimport { AbstractSqliteQueryRunner } from \"../../sqlite-abstract/AbstractSqliteQueryRunner\"\nimport { TransactionNotStartedError } from \"../../../error/TransactionNotStartedError\"\nimport { ExpoLegacyDriver } from \"./ExpoLegacyDriver\"\nimport { Broadcaster } from \"../../../subscriber/Broadcaster\"\nimport { QueryResult } from \"../../../query-runner/QueryResult\"\nimport { BroadcasterResult } from \"../../../subscriber/BroadcasterResult\"\n\n// Needed to satisfy the Typescript compiler\ninterface IResultSet {\n    insertId: number | undefined\n    rowsAffected: number\n    rows: {\n        length: number\n        item: (idx: number) => any\n        _array: any[]\n    }\n}\ninterface ITransaction {\n    executeSql: (\n        sql: string,\n        args: any[] | undefined,\n        ok: (tsx: ITransaction, resultSet: IResultSet) => void,\n        fail: (tsx: ITransaction, err: any) => void,\n    ) => void\n}\n\n/**\n * Runs queries on a single sqlite database connection.\n */\nexport class ExpoLegacyQueryRunner extends AbstractSqliteQueryRunner {\n    /**\n     * Database driver used by connection.\n     */\n    driver: ExpoLegacyDriver\n\n    /**\n     * Database transaction object\n     */\n    private transaction?: ITransaction\n\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(driver: ExpoLegacyDriver) {\n        super()\n        this.driver = driver\n        this.connection = driver.connection\n        this.broadcaster = new Broadcaster(this)\n    }\n\n    /**\n     * Starts transaction. Within Expo, all database operations happen in a\n     * transaction context, so issuing a `BEGIN TRANSACTION` command is\n     * redundant and will result in the following error:\n     *\n     * `Error: Error code 1: cannot start a transaction within a transaction`\n     *\n     * Instead, we keep track of a `Transaction` object in `this.transaction`\n     * and continue using the same object until we wish to commit the\n     * transaction.\n     */\n    async startTransaction(): Promise<void> {\n        this.isTransactionActive = true\n        try {\n            await this.broadcaster.broadcast(\"BeforeTransactionStart\")\n        } catch (err) {\n            this.isTransactionActive = false\n            throw err\n        }\n\n        this.transactionDepth += 1\n\n        await this.broadcaster.broadcast(\"AfterTransactionStart\")\n    }\n\n    /**\n     * Commits transaction.\n     * Error will be thrown if transaction was not started.\n     * Since Expo will automatically commit the transaction once all the\n     * callbacks of the transaction object have been completed, \"committing\" a\n     * transaction in this driver's context means that we delete the transaction\n     * object and set the stage for the next transaction.\n     */\n    async commitTransaction(): Promise<void> {\n        if (\n            !this.isTransactionActive &&\n            typeof this.transaction === \"undefined\"\n        )\n            throw new TransactionNotStartedError()\n\n        await this.broadcaster.broadcast(\"BeforeTransactionCommit\")\n\n        this.transaction = undefined\n        this.isTransactionActive = false\n\n        this.transactionDepth -= 1\n\n        await this.broadcaster.broadcast(\"AfterTransactionCommit\")\n    }\n\n    /**\n     * Rollbacks transaction.\n     * Error will be thrown if transaction was not started.\n     * This method's functionality is identical to `commitTransaction()` because\n     * the transaction lifecycle is handled within the Expo transaction object.\n     * Issuing separate statements for `COMMIT` or `ROLLBACK` aren't necessary.\n     */\n    async rollbackTransaction(): Promise<void> {\n        if (\n            !this.isTransactionActive &&\n            typeof this.transaction === \"undefined\"\n        )\n            throw new TransactionNotStartedError()\n\n        await this.broadcaster.broadcast(\"BeforeTransactionRollback\")\n\n        this.transaction = undefined\n        this.isTransactionActive = false\n\n        this.transactionDepth -= 1\n\n        await this.broadcaster.broadcast(\"AfterTransactionRollback\")\n    }\n\n    /**\n     * Called before migrations are run.\n     */\n    async beforeMigration(): Promise<void> {\n        const databaseConnection = await this.connect()\n        return new Promise((ok, fail) => {\n            databaseConnection.exec(\n                [{ sql: \"PRAGMA foreign_keys = OFF\", args: [] }],\n                false,\n                (err: any) => (err ? fail(err) : ok()),\n            )\n        })\n    }\n\n    /**\n     * Called after migrations are run.\n     */\n    async afterMigration(): Promise<void> {\n        const databaseConnection = await this.connect()\n        return new Promise((ok, fail) => {\n            databaseConnection.exec(\n                [{ sql: \"PRAGMA foreign_keys = ON\", args: [] }],\n                false,\n                (err: any) => (err ? fail(err) : ok()),\n            )\n        })\n    }\n\n    /**\n     * Executes a given SQL query.\n     */\n    async query(\n        query: string,\n        parameters?: any[],\n        useStructuredResult = false,\n    ): Promise<any> {\n        if (this.isReleased) throw new QueryRunnerAlreadyReleasedError()\n\n        return new Promise<any>(async (ok, fail) => {\n            const databaseConnection = await this.connect()\n            const broadcasterResult = new BroadcasterResult()\n\n            this.driver.connection.logger.logQuery(query, parameters, this)\n            this.broadcaster.broadcastBeforeQueryEvent(\n                broadcasterResult,\n                query,\n                parameters,\n            )\n\n            const queryStartTime = Date.now()\n            // All Expo SQL queries are executed in a transaction context\n            databaseConnection.transaction(\n                async (transaction: ITransaction) => {\n                    if (typeof this.transaction === \"undefined\") {\n                        await this.startTransaction()\n                        this.transaction = transaction\n                    }\n                    this.transaction.executeSql(\n                        query,\n                        parameters,\n                        async (t: ITransaction, raw: IResultSet) => {\n                            // log slow queries if maxQueryExecution time is set\n                            const maxQueryExecutionTime =\n                                this.driver.options.maxQueryExecutionTime\n                            const queryEndTime = Date.now()\n                            const queryExecutionTime =\n                                queryEndTime - queryStartTime\n\n                            this.broadcaster.broadcastAfterQueryEvent(\n                                broadcasterResult,\n                                query,\n                                parameters,\n                                true,\n                                queryExecutionTime,\n                                raw,\n                                undefined,\n                            )\n                            await broadcasterResult.wait()\n\n                            if (\n                                maxQueryExecutionTime &&\n                                queryExecutionTime > maxQueryExecutionTime\n                            ) {\n                                this.driver.connection.logger.logQuerySlow(\n                                    queryExecutionTime,\n                                    query,\n                                    parameters,\n                                    this,\n                                )\n                            }\n\n                            const result = new QueryResult()\n\n                            if (raw?.hasOwnProperty(\"rowsAffected\")) {\n                                result.affected = raw.rowsAffected\n                            }\n\n                            if (raw?.hasOwnProperty(\"rows\")) {\n                                let resultSet = []\n                                for (let i = 0; i < raw.rows.length; i++) {\n                                    resultSet.push(raw.rows.item(i))\n                                }\n\n                                result.raw = resultSet\n                                result.records = resultSet\n                            }\n\n                            // return id of inserted row, if query was insert statement.\n                            if (query.startsWith(\"INSERT INTO\")) {\n                                result.raw = raw.insertId\n                            }\n\n                            if (useStructuredResult) {\n                                ok(result)\n                            } else {\n                                ok(result.raw)\n                            }\n                        },\n                        async (t: ITransaction, err: any) => {\n                            this.driver.connection.logger.logQueryError(\n                                err,\n                                query,\n                                parameters,\n                                this,\n                            )\n                            this.broadcaster.broadcastAfterQueryEvent(\n                                broadcasterResult,\n                                query,\n                                parameters,\n                                false,\n                                undefined,\n                                undefined,\n                                err,\n                            )\n                            await broadcasterResult.wait()\n\n                            fail(new QueryFailedError(query, parameters, err))\n                        },\n                    )\n                },\n                async (err: any) => {\n                    await this.rollbackTransaction()\n                    fail(err)\n                },\n                () => {\n                    this.isTransactionActive = false\n                    this.transaction = undefined\n                },\n            )\n        })\n    }\n}\n"], "sourceRoot": "../../.."}