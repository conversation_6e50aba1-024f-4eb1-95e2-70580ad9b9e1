{"version": 3, "file": "jwt.strategy.js", "sourceRoot": "", "sources": ["../../src/auth/jwt.strategy.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAAmE;AACnE,+CAAoD;AACpD,+CAAoD;AACpD,2CAA+C;AAC/C,+CAA+C;AAC/C,uCAAiC;AACjC,yDAA6D;AAGtD,IAAM,WAAW,GAAjB,MAAM,WAAY,SAAQ,IAAA,2BAAgB,EAAC,uBAAQ,CAAC;IAG/C;IACA;IAHV,YAEU,SAA8B,EAC9B,aAA4B;QAEpC,KAAK,CAAC;YACJ,cAAc,EAAE,yBAAU,CAAC,2BAA2B,EAAE;YACxD,gBAAgB,EAAE,KAAK;YACvB,WAAW,EAAE,aAAa,CAAC,GAAG,CAAS,YAAY,CAAC,IAAI,gBAAgB;SACzE,CAAC,CAAC;QAPK,cAAS,GAAT,SAAS,CAAqB;QAC9B,kBAAa,GAAb,aAAa,CAAe;IAOtC,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,OAAY;QACzB,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;QAE/D,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC5B,MAAM,IAAI,8BAAqB,EAAE,CAAC;QACpC,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;CACF,CAAA;AAtBY,kCAAW;sBAAX,WAAW;IADvB,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,sBAAW,EAAC,kBAAI,CAAC,IAAI,CAAC,CAAA;qCACJ,gBAAK;QACD,sBAAa;GAJ3B,WAAW,CAsBvB"}