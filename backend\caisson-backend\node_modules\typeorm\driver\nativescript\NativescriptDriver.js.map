{"version": 3, "sources": ["../../src/driver/nativescript/NativescriptDriver.ts"], "names": [], "mappings": ";;;AAAA,kFAA8E;AAE9E,uEAAmE;AAGnE,+FAA2F;AAI3F;;GAEG;AACH,MAAa,kBAAmB,SAAQ,2CAAoB;IAiBxD,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YAAY,UAAsB;QAC9B,KAAK,CAAC,UAAU,CAAC,CAAA;QAEjB,IAAI,CAAC,UAAU,GAAG,UAAU,CAAA;QAC5B,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC,OAAwC,CAAA;QAClE,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAA;QACrC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAA;QAEjC,sBAAsB;QACtB,IAAI,CAAC,gBAAgB,EAAE,CAAA;IAC3B,CAAC;IAED,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;OAEG;IACH,KAAK,CAAC,UAAU;QACZ,OAAO,IAAI,OAAO,CAAO,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE;YAClC,IAAI,CAAC,WAAW,GAAG,SAAS,CAAA;YAC5B,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;QACxD,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,IAAqB;QACnC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACpB,IAAI,CAAC,WAAW,GAAG,IAAI,iDAAuB,CAAC,IAAI,CAAC,CAAA;QACxD,CAAC;QAED,OAAO,IAAI,CAAC,WAAW,CAAA;IAC3B,CAAC;IAED,aAAa,CAAC,MAKb;QACG,IAAK,MAAM,CAAC,IAAY,KAAK,MAAM,EAAE,CAAC;YAClC,OAAO,MAAM,CAAA;QACjB,CAAC;QAED,OAAO,KAAK,CAAC,aAAa,CAAC,MAAM,CAAC,CAAA;IACtC,CAAC;IACD,4EAA4E;IAC5E,oBAAoB;IACpB,4EAA4E;IAE5E;;OAEG;IACO,wBAAwB;QAC9B,OAAO,IAAI,OAAO,CAAO,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE;YAClC,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CACzB,EAAE,EACF;gBACI,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ;gBAC/B,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG;gBACrB,cAAc,EAAE,IAAI,CAAC,OAAO,CAAC,cAAc;gBAC3C,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO;gBAC7B,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ;gBAC/B,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY;aAC1C,EACD,IAAI,CAAC,OAAO,CAAC,KAAK,IAAI,EAAE,CAC3B,CAAA;YAED,IAAI,IAAI,CAAC,MAAM,CACX,IAAI,CAAC,OAAO,CAAC,QAAQ,EACrB,OAAO,EACP,CAAC,GAAU,EAAE,EAAO,EAAO,EAAE;gBACzB,IAAI,GAAG;oBAAE,OAAO,IAAI,CAAC,GAAG,CAAC,CAAA;gBAEzB,uCAAuC;gBACvC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,CAAA;gBAE1C,yFAAyF;gBACzF,+DAA+D;gBAC/D,EAAE,CAAC,OAAO,CACN,0BAA0B,EAC1B,EAAE,EACF,CAAC,GAAU,EAAE,MAAW,EAAO,EAAE;oBAC7B,IAAI,GAAG;wBAAE,OAAO,IAAI,CAAC,GAAG,CAAC,CAAA;oBACzB,iBAAiB;oBACjB,EAAE,CAAC,EAAE,CAAC,CAAA;gBACV,CAAC,CACJ,CAAA;YACL,CAAC,CACJ,CAAA;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACO,gBAAgB;QACtB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAA;QACzB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACf,MAAM,IAAI,+DAA8B,CACpC,cAAc,EACd,qBAAqB,CACxB,CAAA;QACL,CAAC;IACL,CAAC;CACJ;AAjID,gDAiIC", "file": "NativescriptDriver.js", "sourcesContent": ["import { AbstractSqliteDriver } from \"../sqlite-abstract/AbstractSqliteDriver\"\nimport { NativescriptConnectionOptions } from \"./NativescriptConnectionOptions\"\nimport { NativescriptQueryRunner } from \"./NativescriptQueryRunner\"\nimport { QueryRunner } from \"../../query-runner/QueryRunner\"\nimport { DataSource } from \"../../data-source/DataSource\"\nimport { DriverPackageNotInstalledError } from \"../../error/DriverPackageNotInstalledError\"\nimport { ColumnType } from \"../types/ColumnTypes\"\nimport { ReplicationMode } from \"../types/ReplicationMode\"\n\n/**\n * Organizes communication with sqlite DBMS within Nativescript.\n */\nexport class NativescriptDriver extends AbstractSqliteDriver {\n    // -------------------------------------------------------------------------\n    // Public Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Connection options.\n     */\n    options: NativescriptConnectionOptions\n\n    /**\n     * Nativescript driver module\n     * this is most likely `nativescript-sqlite`\n     * but user can pass his own\n     */\n    driver: any\n\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(connection: DataSource) {\n        super(connection)\n\n        this.connection = connection\n        this.options = connection.options as NativescriptConnectionOptions\n        this.database = this.options.database\n        this.driver = this.options.driver\n\n        // load sqlite package\n        this.loadDependencies()\n    }\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Closes connection with database.\n     */\n    async disconnect(): Promise<void> {\n        return new Promise<void>((ok, fail) => {\n            this.queryRunner = undefined\n            this.databaseConnection.close().then(ok).catch(fail)\n        })\n    }\n\n    /**\n     * Creates a query runner used to execute database queries.\n     */\n    createQueryRunner(mode: ReplicationMode): QueryRunner {\n        if (!this.queryRunner) {\n            this.queryRunner = new NativescriptQueryRunner(this)\n        }\n\n        return this.queryRunner\n    }\n\n    normalizeType(column: {\n        type?: ColumnType\n        length?: number | string\n        precision?: number | null\n        scale?: number\n    }): string {\n        if ((column.type as any) === Buffer) {\n            return \"blob\"\n        }\n\n        return super.normalizeType(column)\n    }\n    // -------------------------------------------------------------------------\n    // Protected Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Creates connection with the database.\n     */\n    protected createDatabaseConnection() {\n        return new Promise<void>((ok, fail) => {\n            const options = Object.assign(\n                {},\n                {\n                    readOnly: this.options.readOnly,\n                    key: this.options.key,\n                    multithreading: this.options.multithreading,\n                    migrate: this.options.migrate,\n                    iosFlags: this.options.iosFlags,\n                    androidFlags: this.options.androidFlags,\n                },\n                this.options.extra || {},\n            )\n\n            new this.sqlite(\n                this.options.database,\n                options,\n                (err: Error, db: any): any => {\n                    if (err) return fail(err)\n\n                    // use object mode to work with TypeORM\n                    db.resultType(this.sqlite.RESULTSASOBJECT)\n\n                    // we need to enable foreign keys in sqlite to make sure all foreign key related features\n                    // working properly. this also makes onDelete work with sqlite.\n                    db.execSQL(\n                        `PRAGMA foreign_keys = ON`,\n                        [],\n                        (err: Error, result: any): any => {\n                            if (err) return fail(err)\n                            // We are all set\n                            ok(db)\n                        },\n                    )\n                },\n            )\n        })\n    }\n\n    /**\n     * If driver dependency is not given explicitly, then try to load it via \"require\".\n     */\n    protected loadDependencies(): void {\n        this.sqlite = this.driver\n        if (!this.driver) {\n            throw new DriverPackageNotInstalledError(\n                \"Nativescript\",\n                \"nativescript-sqlite\",\n            )\n        }\n    }\n}\n"], "sourceRoot": "../.."}