{"version": 3, "sources": ["../../src/logger/LoggerOptions.ts"], "names": [], "mappings": "", "file": "LoggerOptions.js", "sourcesContent": ["import { LogLevel } from \"./Logger\"\n\n/**\n * Logging options.\n */\nexport type LoggerOptions = boolean | \"all\" | LogLevel[]\n\n/**\n * File logging option.\n */\nexport type FileLoggerOptions = {\n    /**\n     * Specify custom path for log file, relative to application root\n     */\n    logPath: string\n}\n"], "sourceRoot": ".."}