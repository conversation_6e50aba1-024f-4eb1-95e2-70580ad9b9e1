"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/produits/page",{

/***/ "(app-pages-browser)/./app/admin/produits/page.tsx":
/*!*************************************!*\
  !*** ./app/admin/produits/page.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProductsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Edit_MoreHorizontal_Plus_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,MoreHorizontal,Plus,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_MoreHorizontal_Plus_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,MoreHorizontal,Plus,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/ellipsis.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_MoreHorizontal_Plus_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,MoreHorizontal,Plus,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_MoreHorizontal_Plus_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,MoreHorizontal,Plus,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./components/ui/table.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n// Sample product data\nconst initialProducts = [\n    {\n        id: 1,\n        name: \"Coffret Tunnel Volet Roulant\",\n        category: \"coffrets\",\n        status: \"active\"\n    },\n    {\n        id: 4,\n        name: \"Panneau Isolant 2cm\",\n        category: \"panneaux\",\n        status: \"active\"\n    },\n    {\n        id: 5,\n        name: \"Panneau Isolant 3cm\",\n        category: \"panneaux\",\n        status: \"active\"\n    },\n    {\n        id: 6,\n        name: \"Panneau Isolant Sur Mesure\",\n        category: \"panneaux\",\n        status: \"inactive\"\n    },\n    {\n        id: 7,\n        name: \"Fish Box Standard\",\n        category: \"fishbox\",\n        status: \"active\"\n    },\n    {\n        id: 8,\n        name: \"Fish Box Grande Capacité\",\n        category: \"fishbox\",\n        status: \"active\"\n    },\n    {\n        id: 9,\n        name: \"Caisson d'Emballage Personnalisé\",\n        category: \"fishbox\",\n        status: \"inactive\"\n    }\n];\nfunction ProductsPage() {\n    _s();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast)();\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialProducts);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [categoryFilter, setCategoryFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const handleDelete = (id)=>{\n        setProducts(products.filter((product)=>product.id !== id));\n        toast({\n            title: \"Produit supprimé\",\n            description: \"Le produit a été supprimé avec succès.\"\n        });\n    };\n    const handleStatusChange = (id, newStatus)=>{\n        setProducts(products.map((product)=>product.id === id ? {\n                ...product,\n                status: newStatus\n            } : product));\n        toast({\n            title: \"Statut mis à jour\",\n            description: \"Le produit est maintenant \".concat(newStatus === \"active\" ? \"actif\" : \"inactif\", \".\")\n        });\n    };\n    // Filter products based on search term, category, and status\n    const filteredProducts = products.filter((product)=>{\n        const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase());\n        const matchesCategory = categoryFilter === \"all\" || product.category === categoryFilter;\n        const matchesStatus = statusFilter === \"all\" || product.status === statusFilter;\n        return matchesSearch && matchesCategory && matchesStatus;\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold\",\n                        children: \"Gestion des produits\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        asChild: true,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/admin/produits/ajouter\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_MoreHorizontal_Plus_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"mr-2 h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 13\n                                }, this),\n                                \" Ajouter un produit\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                children: \"Filtres\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                children: \"Filtrer et rechercher des produits\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid gap-4 md:grid-cols-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                        placeholder: \"Rechercher un produit...\",\n                                        value: searchTerm,\n                                        onChange: (e)=>setSearchTerm(e.target.value)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                        value: categoryFilter,\n                                        onValueChange: setCategoryFilter,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                    placeholder: \"Cat\\xe9gorie\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                    lineNumber: 121,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                lineNumber: 120,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                        value: \"all\",\n                                                        children: \"Toutes les cat\\xe9gories\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                        lineNumber: 124,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                        value: \"coffrets\",\n                                                        children: \"Coffrets tunnel pour volets roulants\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                        lineNumber: 125,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                        value: \"panneaux\",\n                                                        children: \"Panneaux Isolants\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                        lineNumber: 126,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                        value: \"fishbox\",\n                                                        children: \"Fish Box\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                        lineNumber: 127,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                        value: statusFilter,\n                                        onValueChange: setStatusFilter,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                    placeholder: \"Statut\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                    lineNumber: 134,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                        value: \"all\",\n                                                        children: \"Tous les statuts\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                        lineNumber: 137,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                        value: \"active\",\n                                                        children: \"Actif\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                        lineNumber: 138,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                        value: \"inactive\",\n                                                        children: \"Inactif\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                        lineNumber: 139,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                lineNumber: 104,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                children: \"Liste des produits\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                children: [\n                                    filteredProducts.length,\n                                    \" produit\",\n                                    filteredProducts.length > 1 ? \"s\" : \"\",\n                                    \" trouv\\xe9\",\n                                    filteredProducts.length > 1 ? \"s\" : \"\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.Table, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableRow, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                children: \"ID\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                lineNumber: 159,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                children: \"Nom\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                children: \"Cat\\xe9gorie\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                children: \"Prix\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                children: \"Statut\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                className: \"text-right\",\n                                                children: \"Actions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableBody, {\n                                    children: [\n                                        filteredProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableRow, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                        children: product.id\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                        lineNumber: 170,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                        className: \"font-medium\",\n                                                        children: product.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                        lineNumber: 171,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                        children: [\n                                                            product.category === \"coffrets\" && \"Coffrets tunnel pour volets roulants\",\n                                                            product.category === \"panneaux\" && \"Panneaux Isolants\",\n                                                            product.category === \"fishbox\" && \"Fish Box\",\n                                                            product.category === \"Polystyrène\" && \"polystyrène\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                        lineNumber: 172,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium \".concat(product.status === \"active\" ? \"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300\" : \"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300\"),\n                                                            children: product.status === \"active\" ? \"Actif\" : \"Inactif\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                            lineNumber: 180,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                        lineNumber: 179,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                        className: \"text-right\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenu, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuTrigger, {\n                                                                    asChild: true,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        size: \"icon\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_MoreHorizontal_Plus_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                                                lineNumber: 194,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"sr-only\",\n                                                                                children: \"Menu\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                                                lineNumber: 195,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                                        lineNumber: 193,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                                    lineNumber: 192,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuContent, {\n                                                                    align: \"end\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuItem, {\n                                                                            asChild: true,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                                href: \"/admin/produits/modifier/\".concat(product.id),\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_MoreHorizontal_Plus_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                        className: \"mr-2 h-4 w-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                                                        lineNumber: 201,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    \" Modifier\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                                                lineNumber: 200,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                                            lineNumber: 199,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuItem, {\n                                                                            onClick: ()=>handleStatusChange(product.id, product.status === \"active\" ? \"inactive\" : \"active\"),\n                                                                            children: product.status === \"active\" ? \"Désactiver\" : \"Activer\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                                            lineNumber: 204,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuItem, {\n                                                                            className: \"text-red-600 focus:text-red-600\",\n                                                                            onClick: ()=>handleDelete(product.id),\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_MoreHorizontal_Plus_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                    className: \"mr-2 h-4 w-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                                                    lineNumber: 215,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                \" Supprimer\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                                            lineNumber: 211,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                                    lineNumber: 198,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                            lineNumber: 191,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                        lineNumber: 190,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, product.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 17\n                                            }, this)),\n                                        filteredProducts.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableRow, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                colSpan: 6,\n                                                className: \"text-center py-8 text-muted-foreground\",\n                                                children: \"Aucun produit trouv\\xe9\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                                lineNumber: 224,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                            lineNumber: 156,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n                lineNumber: 147,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\projects\\\\site caisson\\\\soci-t--caisson-tunisie\\\\app\\\\admin\\\\produits\\\\page.tsx\",\n        lineNumber: 94,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductsPage, \"mRXhbniXqUA3/DjMBsS1WQ4nuSc=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast\n    ];\n});\n_c = ProductsPage;\nvar _c;\n$RefreshReg$(_c, \"ProductsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/admin/produits/page.tsx\n"));

/***/ })

});