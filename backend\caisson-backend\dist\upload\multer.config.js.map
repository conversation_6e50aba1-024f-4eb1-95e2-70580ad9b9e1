{"version": 3, "file": "multer.config.js", "sourceRoot": "", "sources": ["../../src/upload/multer.config.ts"], "names": [], "mappings": ";;;AAAA,mCAAqC;AACrC,+BAA+B;AAC/B,+BAAoC;AAEvB,QAAA,YAAY,GAAG;IAC1B,OAAO,EAAE,IAAA,oBAAW,EAAC;QACnB,WAAW,EAAE,WAAW;QACxB,QAAQ,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE;YAChC,MAAM,YAAY,GAAG,IAAA,SAAM,GAAE,CAAC;YAC9B,MAAM,GAAG,GAAG,IAAA,cAAO,EAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YACvC,MAAM,QAAQ,GAAG,GAAG,YAAY,GAAG,GAAG,EAAE,CAAC;YACzC,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;QAC3B,CAAC;KACF,CAAC;IACF,UAAU,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE;QAClC,MAAM,gBAAgB,GAAG;YACvB,YAAY;YACZ,WAAW;YACX,WAAW;YACX,YAAY;YACZ,WAAW;YACX,YAAY;YACZ,iBAAiB;YACjB,oBAAoB;YACpB,yEAAyE;SAC1E,CAAC;QAEF,IAAI,gBAAgB,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC7C,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QACvB,CAAC;aAAM,CAAC;YACN,QAAQ,CAAC,IAAI,KAAK,CAAC,uBAAuB,CAAC,EAAE,KAAK,CAAC,CAAC;QACtD,CAAC;IACH,CAAC;IACD,MAAM,EAAE;QACN,QAAQ,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI;KAC3B;CACF,CAAC"}