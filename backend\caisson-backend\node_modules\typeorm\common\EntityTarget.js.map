{"version": 3, "sources": ["../../src/common/EntityTarget.ts"], "names": [], "mappings": "", "file": "EntityTarget.js", "sourcesContent": ["import { ObjectType } from \"./ObjectType\"\nimport { EntitySchema } from \"..\"\n\n/**\n * Entity target.\n */\nexport type EntityTarget<Entity> =\n    | ObjectType<Entity>\n    | EntitySchema<Entity>\n    | string\n    | { type: Entity; name: string }\n"], "sourceRoot": ".."}