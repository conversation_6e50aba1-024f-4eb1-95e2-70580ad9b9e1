import { ConfigService } from '@nestjs/config';
import { Repository } from 'typeorm';
import { Media } from '../entities/media.entity';
export declare class UploadService {
    private mediaRepository;
    private configService;
    constructor(mediaRepository: Repository<Media>, configService: ConfigService);
    uploadFile(file: Express.Multer.File, title?: string, description?: string): Promise<Media>;
    uploadMultipleFiles(files: Express.Multer.File[], title?: string, description?: string): Promise<Media[]>;
    deleteFile(id: string): Promise<void>;
    findAll(): Promise<Media[]>;
    findOne(id: string): Promise<Media>;
    validateFile(file: Express.Multer.File): void;
}
