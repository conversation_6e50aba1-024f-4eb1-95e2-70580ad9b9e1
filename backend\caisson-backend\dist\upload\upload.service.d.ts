import { ConfigService } from '@nestjs/config';
import { Model } from 'mongoose';
import { Media, MediaDocument } from '../entities/media.entity';
export declare class UploadService {
    private mediaModel;
    private configService;
    constructor(mediaModel: Model<MediaDocument>, configService: ConfigService);
    uploadFile(file: Express.Multer.File, title?: string, description?: string): Promise<Media>;
    uploadMultipleFiles(files: Express.Multer.File[], title?: string, description?: string): Promise<Media[]>;
    deleteFile(id: string): Promise<void>;
    findAll(): Promise<Media[]>;
    findOne(id: string): Promise<Media>;
    validateFile(file: Express.Multer.File): void;
}
