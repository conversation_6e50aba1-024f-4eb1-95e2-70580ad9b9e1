{"version": 3, "sources": ["../browser/src/schema-builder/options/TableCheckOptions.ts"], "names": [], "mappings": "", "file": "TableCheckOptions.js", "sourcesContent": ["/**\n * Database's table check constraint options.\n */\nexport interface TableCheckOptions {\n    // -------------------------------------------------------------------------\n    // Public Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Constraint name.\n     */\n    name?: string\n\n    /**\n     * Column that contains this constraint.\n     */\n    columnNames?: string[]\n\n    /**\n     * Check expression.\n     */\n    expression?: string\n}\n"], "sourceRoot": "../.."}