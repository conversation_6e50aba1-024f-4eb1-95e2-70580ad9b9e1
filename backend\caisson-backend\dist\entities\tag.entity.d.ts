import { Document } from 'mongoose';
export type TagDocument = Tag & Document;
export declare class Tag {
    name: string;
    createdAt?: Date;
    updatedAt?: Date;
}
export declare const TagSchema: import("mongoose").Schema<Tag, import("mongoose").Model<Tag, any, any, any, Document<unknown, any, Tag, any> & Tag & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, Tag, Document<unknown, {}, import("mongoose").FlatRecord<Tag>, {}> & import("mongoose").FlatRecord<Tag> & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}>;
