import {
  <PERSON>tity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  ManyToMany,
  JoinTable,
  OneToMany,
} from 'typeorm';
import { Category } from './category.entity';
import { Tag } from './tag.entity';
import { Review } from './review.entity';
import { TechnicalSheet } from './technical-sheet.entity';

export enum ProductStatus {
  DRAFT = 'draft',
  PUBLISHED = 'published',
  ARCHIVED = 'archived',
}

@Entity('products')
export class Product {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  name: string;

  @Column({ unique: true })
  slug: string;

  @Column({ type: 'text' })
  description: string;

  @Column({ type: 'longtext', nullable: true })
  longDescription: string;

  @Column('json')
  images: string[];

  @Column({ default: true })
  inStock: boolean;

  @Column('json')
  features: string[];

  @Column('json', { nullable: true })
  specifications: Record<string, string>;

  @ManyToOne(() => Category, (category) => category.products)
  category: Category;

  @ManyToMany(() => Tag, (tag) => tag.products)
  @JoinTable()
  tags: Tag[];

  @OneToMany(() => Review, (review) => review.product)
  reviews: Review[];

  @OneToMany(() => TechnicalSheet, (technicalSheet) => technicalSheet.product)
  technicalSheets: TechnicalSheet[];

  @Column({ type: 'decimal', precision: 3, scale: 2, default: 0 })
  avgRating: number;

  @Column({ default: 0 })
  reviewCount: number;

  @Column({
    type: 'enum',
    enum: ProductStatus,
    default: ProductStatus.PUBLISHED,
  })
  status: ProductStatus;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
