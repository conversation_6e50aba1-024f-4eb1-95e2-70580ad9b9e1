{"version": 3, "sources": ["../browser/src/schema-builder/options/TableForeignKeyOptions.ts"], "names": [], "mappings": "", "file": "TableForeignKeyOptions.js", "sourcesContent": ["/**\n * Foreign key options.\n */\nexport interface TableForeignKeyOptions {\n    // -------------------------------------------------------------------------\n    // Public Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Name of the foreign key.\n     */\n    name?: string\n\n    /**\n     * Column names which included by this foreign key.\n     */\n    columnNames: string[]\n\n    /**\n     * Database of the Table referenced in the foreign key.\n     */\n    referencedDatabase?: string\n\n    /**\n     * Schema of the Table referenced in the foreign key.\n     */\n    referencedSchema?: string\n\n    /**\n     * Table referenced in the foreign key.\n     */\n    referencedTableName: string\n\n    /**\n     * Column names which included by this foreign key.\n     */\n    referencedColumnNames: string[]\n\n    /**\n     * \"ON DELETE\" of this foreign key, e.g. what action database should perform when\n     * referenced stuff is being deleted.\n     */\n    onDelete?: string\n\n    /**\n     * \"ON UPDATE\" of this foreign key, e.g. what action database should perform when\n     * referenced stuff is being updated.\n     */\n    onUpdate?: string\n\n    /**\n     * Set this foreign key constraint as \"DEFERRABLE\" e.g. check constraints at start\n     * or at the end of a transaction\n     */\n    deferrable?: string\n}\n"], "sourceRoot": "../.."}