import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { ConfigService } from '@nestjs/config';
import { AuthService } from '../auth/auth.service';
import { User, UserDocument } from '../entities/user.entity';
import { Category, CategoryDocument } from '../entities/category.entity';
import { Tag, TagDocument } from '../entities/tag.entity';
import { Product, ProductDocument, ProductStatus } from '../entities/product.entity';
import { Review, ReviewDocument } from '../entities/review.entity';
import { Project, ProjectDocument, ProjectCategory } from '../entities/project.entity';
import { BlogPost, BlogPostDocument, BlogCategory } from '../entities/blog-post.entity';
import { Testimonial, TestimonialDocument } from '../entities/testimonial.entity';

@Injectable()
export class SeederService {
  private readonly logger = new Logger(SeederService.name);

  constructor(
    @InjectModel(User.name)
    private userModel: Model<UserDocument>,
    @InjectModel(Category.name)
    private categoryModel: Model<CategoryDocument>,
    @InjectModel(Tag.name)
    private tagModel: Model<TagDocument>,
    @InjectModel(Product.name)
    private productModel: Model<ProductDocument>,
    @InjectModel(Review.name)
    private reviewModel: Model<ReviewDocument>,
    @InjectModel(Project.name)
    private projectModel: Model<ProjectDocument>,
    @InjectModel(BlogPost.name)
    private blogPostModel: Model<BlogPostDocument>,
    @InjectModel(Testimonial.name)
    private testimonialModel: Model<TestimonialDocument>,
    private authService: AuthService,
    private configService: ConfigService,
  ) {}

  async seedAdminUser() {
    const adminEmail = this.configService.get<string>('ADMIN_EMAIL');
    const adminPassword = this.configService.get<string>('ADMIN_PASSWORD');

    if (!adminEmail || !adminPassword) {
      this.logger.error('Admin email or password not configured');
      return;
    }

    // Check if admin user already exists
    const existingAdmin = await this.userModel.findOne({
      email: adminEmail,
    }).exec();

    if (existingAdmin) {
      this.logger.log('Admin user already exists');
      return;
    }

    // Create admin user
    try {
      await this.authService.createUser({
        email: adminEmail,
        name: 'Administrateur',
        password: adminPassword,
        role: 'admin',
        isActive: true,
      });

      this.logger.log('Admin user created successfully');
    } catch (error) {
      this.logger.error('Failed to create admin user', error);
    }
  }

  async seedCategories() {
    const existingCategories = await this.categoryModel.countDocuments();
    if (existingCategories > 0) {
      this.logger.log('Categories already exist, skipping...');
      return;
    }

    const categories = [
      {
        name: "Coffrets tunnel pour volets roulants",
        slug: "coffrets",
        description: "Coffrets tunnel pour volets roulants et autres applications",
      },
      {
        name: "Panneaux Isolants",
        slug: "panneaux",
        description: "Panneaux isolants en polystyrène expansé pour l'isolation thermique",
      },
      {
        name: "Fish Box",
        slug: "fishbox",
        description: "Caissons d'emballage isothermes pour le transport de produits frais",
      },
    ];

    await this.categoryModel.insertMany(categories);
    this.logger.log('Categories seeded successfully');
  }

  async seedTags() {
    const existingTags = await this.tagModel.countDocuments();
    if (existingTags > 0) {
      this.logger.log('Tags already exist, skipping...');
      return;
    }

    const tags = [
      { name: "isolation" },
      { name: "thermique" },
      { name: "polystyrène" },
      { name: "EPS" },
      { name: "volet roulant" },
      { name: "coffret tunnel" },
      { name: "construction" },
      { name: "bâtiment" },
      { name: "isotherme" },
      { name: "transport" },
      { name: "innovation" },
      { name: "efficacité énergétique" },
    ];

    await this.tagModel.insertMany(tags);
    this.logger.log('Tags seeded successfully');
  }

  // TODO: Add product seeding method

  // TODO: Add other seeding methods after fixing TypeScript issues

  async seed() {
    this.logger.log('Starting database seeding...');
    await this.seedAdminUser();
    await this.seedCategories();
    await this.seedTags();
    // TODO: Add other seeding methods after fixing TypeScript issues
    this.logger.log('Database seeding completed');
  }
}
