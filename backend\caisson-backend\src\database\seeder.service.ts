import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { ConfigService } from '@nestjs/config';
import { AuthService } from '../auth/auth.service';
import { User, UserDocument } from '../entities/user.entity';

@Injectable()
export class SeederService {
  private readonly logger = new Logger(SeederService.name);

  constructor(
    @InjectModel(User.name)
    private userModel: Model<UserDocument>,
    private authService: AuthService,
    private configService: ConfigService,
  ) {}

  async seedAdminUser() {
    const adminEmail = this.configService.get<string>('ADMIN_EMAIL');
    const adminPassword = this.configService.get<string>('ADMIN_PASSWORD');

    if (!adminEmail || !adminPassword) {
      this.logger.error('Admin email or password not configured');
      return;
    }

    // Check if admin user already exists
    const existingAdmin = await this.userModel.findOne({
      email: adminEmail,
    }).exec();

    if (existingAdmin) {
      this.logger.log('Admin user already exists');
      return;
    }

    // Create admin user
    try {
      await this.authService.createUser({
        email: adminEmail,
        name: 'Administrateur',
        password: adminPassword,
        role: 'admin',
        isActive: true,
      });

      this.logger.log('Admin user created successfully');
    } catch (error) {
      this.logger.error('Failed to create admin user', error);
    }
  }

  async seed() {
    this.logger.log('Starting database seeding...');
    await this.seedAdminUser();
    this.logger.log('Database seeding completed');
  }
}
