"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TagsService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const tag_entity_1 = require("../entities/tag.entity");
let TagsService = class TagsService {
    tagModel;
    constructor(tagModel) {
        this.tagModel = tagModel;
    }
    async create(createTagDto) {
        const existingTag = await this.tagModel.findOne({
            name: createTagDto.name,
        }).exec();
        if (existingTag) {
            throw new common_1.BadRequestException('Tag with this name already exists');
        }
        const tag = new this.tagModel(createTagDto);
        return tag.save();
    }
    async findAll() {
        return this.tagModel.find().sort({ createdAt: -1 }).exec();
    }
    async findOne(id) {
        const tag = await this.tagModel.findById(id).exec();
        if (!tag) {
            throw new common_1.NotFoundException('Tag not found');
        }
        return tag;
    }
    async update(id, updateTagDto) {
        if (updateTagDto.name) {
            const existingTag = await this.tagModel.findOne({
                name: updateTagDto.name,
            }).exec();
            if (existingTag && existingTag._id.toString() !== id) {
                throw new common_1.BadRequestException('Tag with this name already exists');
            }
        }
        const tag = await this.tagModel.findByIdAndUpdate(id, updateTagDto, { new: true }).exec();
        if (!tag) {
            throw new common_1.NotFoundException('Tag not found');
        }
        return tag;
    }
    async remove(id) {
        const tag = await this.tagModel.findById(id).exec();
        if (!tag) {
            throw new common_1.NotFoundException('Tag not found');
        }
        await this.tagModel.findByIdAndDelete(id).exec();
    }
};
exports.TagsService = TagsService;
exports.TagsService = TagsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(tag_entity_1.Tag.name)),
    __metadata("design:paramtypes", [mongoose_2.Model])
], TagsService);
//# sourceMappingURL=tags.service.js.map