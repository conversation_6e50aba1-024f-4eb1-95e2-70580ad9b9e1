import { Tag } from './tag.entity';
export declare enum BlogCategory {
    ISOLATION = "ISOLATION",
    PRODUCTS = "PRODUCTS",
    INNOVATION = "INNOVATION",
    ADVICE = "ADVICE"
}
export declare class BlogPost {
    id: string;
    title: string;
    slug: string;
    excerpt: string;
    content: string;
    image: string;
    author: string;
    category: BlogCategory;
    tags: Tag[];
    published: boolean;
    createdAt: Date;
    updatedAt: Date;
}
