import { Document, Types } from 'mongoose';
export declare enum BlogCategory {
    ISOLATION = "ISOLATION",
    PRODUCTS = "PRODUCTS",
    INNOVATION = "INNOVATION",
    ADVICE = "ADVICE"
}
export type BlogPostDocument = BlogPost & Document;
export declare class BlogPost {
    title: string;
    slug: string;
    excerpt: string;
    content: string;
    image?: string;
    author: string;
    category: BlogCategory;
    tags: Types.ObjectId[];
    published: boolean;
    createdAt?: Date;
    updatedAt?: Date;
}
export declare const BlogPostSchema: import("mongoose").Schema<BlogPost, import("mongoose").Model<BlogPost, any, any, any, Document<unknown, any, BlogPost, any> & BlogPost & {
    _id: Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, BlogPost, Document<unknown, {}, import("mongoose").FlatRecord<BlogPost>, {}> & import("mongoose").FlatRecord<BlogPost> & {
    _id: Types.ObjectId;
} & {
    __v: number;
}>;
