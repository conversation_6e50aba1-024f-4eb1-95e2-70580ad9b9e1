{"version": 3, "sources": ["../browser/src/driver/spanner/SpannerQueryRunner.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,MAAM,aAAa,CAAA;AAC1C,OAAO,EAAE,gBAAgB,EAAE,MAAM,8BAA8B,CAAA;AAC/D,OAAO,EAAE,+BAA+B,EAAE,MAAM,6CAA6C,CAAA;AAC7F,OAAO,EAAE,0BAA0B,EAAE,MAAM,wCAAwC,CAAA;AAEnF,OAAO,EAAE,eAAe,EAAE,MAAM,oCAAoC,CAAA;AACpE,OAAO,EAAE,WAAW,EAAE,MAAM,gCAAgC,CAAA;AAG5D,OAAO,EAAE,KAAK,EAAE,MAAM,kCAAkC,CAAA;AACxD,OAAO,EAAE,UAAU,EAAE,MAAM,uCAAuC,CAAA;AAClE,OAAO,EAAE,WAAW,EAAE,MAAM,wCAAwC,CAAA;AAEpE,OAAO,EAAE,eAAe,EAAE,MAAM,4CAA4C,CAAA;AAC5E,OAAO,EAAE,UAAU,EAAE,MAAM,uCAAuC,CAAA;AAClE,OAAO,EAAE,WAAW,EAAE,MAAM,wCAAwC,CAAA;AACpE,OAAO,EAAE,IAAI,EAAE,MAAM,gCAAgC,CAAA;AACrD,OAAO,EAAE,WAAW,EAAE,MAAM,8BAA8B,CAAA;AAC1D,OAAO,EAAE,iBAAiB,EAAE,MAAM,oCAAoC,CAAA;AACtE,OAAO,EAAE,QAAQ,EAAE,MAAM,qBAAqB,CAAA;AAC9C,OAAO,EAAE,KAAK,EAAE,MAAM,UAAU,CAAA;AAGhC,OAAO,EAAE,iBAAiB,EAAE,MAAM,4BAA4B,CAAA;AAI9D;;GAEG;AACH,MAAM,OAAO,kBAAmB,SAAQ,eAAe;IAoBnD,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YAAY,MAAqB,EAAE,IAAqB;QACpD,KAAK,EAAE,CAAA;QACP,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;QACpB,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,UAAU,CAAA;QACnC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;QAChB,IAAI,CAAC,WAAW,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,CAAA;IAC5C,CAAC;IAED,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;;OAGG;IACH,KAAK,CAAC,OAAO;QACT,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;QACxC,CAAC;QAED,MAAM,CAAC,OAAO,CAAC,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,aAAa,CAAC,EAAE,CAAC,CAAA;QACtE,IAAI,CAAC,OAAO,GAAG,OAAO,CAAA;QACtB,IAAI,CAAC,kBAAkB,GAAG,MAAM,OAAO,CAAC,WAAW,EAAE,CAAA;QACrD,OAAO,IAAI,CAAC,OAAO,CAAA;IACvB,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,OAAO;QACT,IAAI,CAAC,UAAU,GAAG,IAAI,CAAA;QACtB,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAA;QAC/B,CAAC;QACD,IAAI,CAAC,OAAO,GAAG,SAAS,CAAA;QACxB,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;IAC5B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB,CAAC,cAA+B;QAClD,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAA;QAC/B,IAAI,CAAC;YACD,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAA;QAC9D,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACX,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAA;YAChC,MAAM,GAAG,CAAA;QACb,CAAC;QAED,MAAM,IAAI,CAAC,OAAO,EAAE,CAAA;QACpB,MAAM,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAA;QACrC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,mBAAmB,CAAC,CAAA;QAEpD,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,uBAAuB,CAAC,CAAA;IAC7D,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,iBAAiB;QACnB,IAAI,CAAC,IAAI,CAAC,mBAAmB,IAAI,CAAC,IAAI,CAAC,kBAAkB;YACrD,MAAM,IAAI,0BAA0B,EAAE,CAAA;QAE1C,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,yBAAyB,CAAC,CAAA;QAE3D,MAAM,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,CAAA;QACtC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAA;QACzC,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAA;QAEhC,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAA;IAC9D,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,mBAAmB;QACrB,IAAI,CAAC,IAAI,CAAC,mBAAmB,IAAI,CAAC,IAAI,CAAC,kBAAkB;YACrD,MAAM,IAAI,0BAA0B,EAAE,CAAA;QAE1C,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,2BAA2B,CAAC,CAAA;QAE7D,MAAM,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,CAAA;QACxC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAA;QAC3C,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAA;QAEhC,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAA;IAChE,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,KAAK,CACP,KAAa,EACb,UAAkB,EAClB,sBAA+B,KAAK;QAEpC,IAAI,IAAI,CAAC,UAAU;YAAE,MAAM,IAAI,+BAA+B,EAAE,CAAA;QAEhE,MAAM,IAAI,CAAC,OAAO,EAAE,CAAA;QAEpB,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,UAAU,EAAE,IAAI,CAAC,CAAA;QAC/D,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,aAAa,EAAE,KAAK,EAAE,UAAU,CAAC,CAAA;QAElE,MAAM,iBAAiB,GAAG,IAAI,iBAAiB,EAAE,CAAA;QAEjD,IAAI,CAAC;YACD,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;YACjC,IAAI,SAAS,GAWK,SAAS,CAAA;YAC3B,MAAM,QAAQ,GAAG,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAA;YAC3C,MAAM,QAAQ,GACV,QAAQ,IAAI,CAAC,IAAI,CAAC,mBAAmB;gBACjC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,gBAAgB;gBAC9B,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAA;YAEjC,IAAI,CAAC,IAAI,CAAC,mBAAmB,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACzC,MAAM,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAA;YACzC,CAAC;YAED,IAAI,CAAC;gBACD,SAAS,GAAG,MAAM,QAAQ,CAAC,GAAG,CAAC;oBAC3B,GAAG,EAAE,KAAK;oBACV,MAAM,EAAE,UAAU;wBACd,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE;4BACvC,MAAM,CAAC,OAAO,GAAG,KAAK,CAAC,GAAG,KAAK,CAAA;4BAC/B,OAAO,MAAM,CAAA;wBACjB,CAAC,EAAE,EAAmB,CAAC;wBACzB,CAAC,CAAC,SAAS;oBACf,IAAI,EAAE,IAAI;iBACb,CAAC,CAAA;gBACF,IAAI,CAAC,IAAI,CAAC,mBAAmB,IAAI,CAAC,QAAQ,EAAE,CAAC;oBACzC,MAAM,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,CAAA;gBAC1C,CAAC;YACL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,IAAI,CAAC;oBACD,2DAA2D;oBAC3D,IAAI,CAAC,IAAI,CAAC,mBAAmB,IAAI,CAAC,QAAQ;wBACtC,MAAM,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,CAAA;gBAChD,CAAC;gBAAC,OAAO,aAAa,EAAE,CAAC,CAAA,CAAC;gBAC1B,MAAM,KAAK,CAAA;YACf,CAAC;YAED,oDAAoD;YACpD,MAAM,qBAAqB,GACvB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,qBAAqB,CAAA;YAC7C,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;YAC/B,MAAM,kBAAkB,GAAG,YAAY,GAAG,cAAc,CAAA;YAExD,IAAI,CAAC,WAAW,CAAC,wBAAwB,CACrC,iBAAiB,EACjB,KAAK,EACL,UAAU,EACV,IAAI,EACJ,kBAAkB,EAClB,SAAS,EACT,SAAS,CACZ,CAAA;YAED,IACI,qBAAqB;gBACrB,kBAAkB,GAAG,qBAAqB;gBAE1C,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,YAAY,CACtC,kBAAkB,EAClB,KAAK,EACL,UAAU,EACV,IAAI,CACP,CAAA;YAEL,MAAM,MAAM,GAAG,IAAI,WAAW,EAAE,CAAA;YAEhC,MAAM,CAAC,GAAG,GAAG,SAAS,CAAA;YACtB,MAAM,CAAC,OAAO,GAAG,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAA;YAC9C,IAAI,SAAS,IAAI,SAAS,CAAC,CAAC,CAAC,IAAI,SAAS,CAAC,CAAC,CAAC,CAAC,aAAa,EAAE,CAAC;gBAC1D,MAAM,CAAC,QAAQ,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAA;YAC1D,CAAC;YAED,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBACvB,OAAO,MAAM,CAAC,OAAO,CAAA;YACzB,CAAC;YAED,OAAO,MAAM,CAAA;QACjB,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACX,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,aAAa,CACvC,GAAG,EACH,KAAK,EACL,UAAU,EACV,IAAI,CACP,CAAA;YACD,IAAI,CAAC,WAAW,CAAC,wBAAwB,CACrC,iBAAiB,EACjB,KAAK,EACL,UAAU,EACV,KAAK,EACL,SAAS,EACT,SAAS,EACT,GAAG,CACN,CAAA;YACD,MAAM,IAAI,gBAAgB,CAAC,KAAK,EAAE,UAAU,EAAE,GAAG,CAAC,CAAA;QACtD,CAAC;gBAAS,CAAC;YACP,MAAM,iBAAiB,CAAC,IAAI,EAAE,CAAA;QAClC,CAAC;IACL,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,SAAS,CAAC,KAAa,EAAE,UAAkB;QAC7C,IAAI,IAAI,CAAC,UAAU;YAAE,MAAM,IAAI,+BAA+B,EAAE,CAAA;QAEhE,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,UAAU,EAAE,IAAI,CAAC,CAAA;QAC/D,IAAI,CAAC;YACD,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;YACjC,MAAM,CAAC,SAAS,CAAC,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,YAAY,CAC/D,KAAK,CACR,CAAA;YACD,MAAM,SAAS,CAAC,OAAO,EAAE,CAAA;YACzB,oDAAoD;YACpD,MAAM,qBAAqB,GACvB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,qBAAqB,CAAA;YAC7C,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;YAC/B,MAAM,kBAAkB,GAAG,YAAY,GAAG,cAAc,CAAA;YACxD,IACI,qBAAqB;gBACrB,kBAAkB,GAAG,qBAAqB;gBAE1C,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,YAAY,CACtC,kBAAkB,EAClB,KAAK,EACL,UAAU,EACV,IAAI,CACP,CAAA;QACT,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACX,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,aAAa,CACvC,GAAG,EACH,KAAK,EACL,UAAU,EACV,IAAI,CACP,CAAA;YACD,MAAM,IAAI,gBAAgB,CAAC,KAAK,EAAE,UAAU,EAAE,GAAG,CAAC,CAAA;QACtD,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,MAAM,CACR,KAAa,EACb,UAAkB,EAClB,KAAgB,EAChB,OAAkB;QAElB,IAAI,IAAI,CAAC,UAAU;YAAE,MAAM,IAAI,+BAA+B,EAAE,CAAA;QAEhE,IAAI,CAAC;YACD,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,UAAU,EAAE,IAAI,CAAC,CAAA;YAC/D,MAAM,OAAO,GAAG;gBACZ,GAAG,EAAE,KAAK;gBACV,MAAM,EAAE,UAAU;oBACd,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE;wBACvC,MAAM,CAAC,OAAO,GAAG,KAAK,CAAC,GAAG,KAAK,CAAA;wBAC/B,OAAO,MAAM,CAAA;oBACjB,CAAC,EAAE,EAAmB,CAAC;oBACzB,CAAC,CAAC,SAAS;gBACf,IAAI,EAAE,IAAI;aACb,CAAA;YACD,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,SAAS,CAAC,OAAO,CAAC,CAAA;YAE9D,IAAI,KAAK,EAAE,CAAC;gBACR,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;YAC3B,CAAC;YAED,IAAI,OAAO,EAAE,CAAC;gBACV,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,CAAA;YAC/B,CAAC;YAED,OAAO,MAAM,CAAA;QACjB,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACX,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,aAAa,CACvC,GAAG,EACH,KAAK,EACL,UAAU,EACV,IAAI,CACP,CAAA;YACD,MAAM,IAAI,gBAAgB,CAAC,KAAK,EAAE,UAAU,EAAE,GAAG,CAAC,CAAA;QACtD,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY;QACd,OAAO,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAA;IAC9B,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,UAAU,CAAC,QAAiB;QAC9B,OAAO,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAA;IAC9B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CAAC,QAAgB;QAC9B,MAAM,IAAI,YAAY,CAClB,6DAA6D,CAChE,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,kBAAkB;QACpB,MAAM,IAAI,YAAY,CAClB,6DAA6D,CAChE,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CAAC,MAAc;QAC1B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,KAAK,CAC3B,wEAAwE,MAAM,GAAG,CACpF,CAAA;QACD,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAA;IACvC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB;QAClB,MAAM,IAAI,YAAY,CAClB,2DAA2D,CAC9D,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ,CAAC,WAA2B;QACtC,MAAM,SAAS,GACX,WAAW,YAAY,KAAK,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW,CAAA;QACjE,MAAM,GAAG,GACL,kDAAkD;YAClD,2FAA2F;YAC3F,yBAAyB,SAAS,GAAG,CAAA;QACzC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QACpC,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAA;IACvC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CACX,WAA2B,EAC3B,UAAkB;QAElB,MAAM,SAAS,GACX,WAAW,YAAY,KAAK,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW,CAAA;QACjE,MAAM,GAAG,GACL,mDAAmD;YACnD,yDAAyD;YACzD,yBAAyB,SAAS,4BAA4B,UAAU,GAAG,CAAA;QAC/E,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QACpC,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAA;IACvC,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,cAAc,CAChB,QAAgB,EAChB,UAAoB;QAEpB,IAAI,UAAU,EAAE,CAAC;YACb,MAAM,qBAAqB,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAA;YAE9D,IAAI,qBAAqB;gBAAE,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;QACvD,CAAC;QAED,MAAM,EAAE,GAAG,oBAAoB,QAAQ,GAAG,CAAA;QAC1C,MAAM,IAAI,GAAG,kBAAkB,QAAQ,GAAG,CAAA;QAC1C,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,CAAA;IAC7D,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,YAAY,CAAC,QAAgB,EAAE,OAAiB;QAClD,MAAM,EAAE,GAAG,OAAO;YACd,CAAC,CAAC,4BAA4B,QAAQ,GAAG;YACzC,CAAC,CAAC,kBAAkB,QAAQ,GAAG,CAAA;QACnC,MAAM,IAAI,GAAG,oBAAoB,QAAQ,GAAG,CAAA;QAC5C,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,CAAA;IAC7D,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CACd,UAAkB,EAClB,UAAoB;QAEpB,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;IAC5B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CACZ,UAAkB,EAClB,OAAiB,EACjB,SAAmB;QAEnB,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;IAC5B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CACb,KAAY,EACZ,aAAsB,KAAK,EAC3B,oBAA6B,IAAI,EACjC,gBAAyB,IAAI;QAE7B,IAAI,UAAU,EAAE,CAAC;YACb,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAA;YAC/C,IAAI,YAAY;gBAAE,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;QAC9C,CAAC;QACD,MAAM,SAAS,GAAY,EAAE,CAAA;QAC7B,MAAM,WAAW,GAAY,EAAE,CAAA;QAE/B,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,iBAAiB,CAAC,CAAC,CAAA;QAC7D,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAA;QAE1C,iFAAiF;QACjF,kIAAkI;QAClI,IAAI,iBAAiB;YACjB,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE,CACrC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC,CAC9D,CAAA;QAEL,IAAI,aAAa,EAAE,CAAC;YAChB,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;gBAC5B,sFAAsF;gBACtF,IAAI,CAAC,KAAK,CAAC,IAAI;oBACX,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,SAAS,CACjD,KAAK,EACL,KAAK,CAAC,WAAW,EACjB,KAAK,CAAC,KAAK,CACd,CAAA;gBACL,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAA;gBACjD,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAA;YACrD,CAAC,CAAC,CAAA;QACN,CAAC;QAED,4FAA4F;QAC5F,MAAM,gBAAgB,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CACzC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,aAAa,IAAI,MAAM,CAAC,YAAY,CAC1D,CAAA;QAED,KAAK,MAAM,MAAM,IAAI,gBAAgB,EAAE,CAAC;YACpC,MAAM,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC;gBAC9C,KAAK,EAAE,KAAK,CAAC,IAAI;gBACjB,IAAI,EAAE,iBAAiB,CAAC,gBAAgB;gBACxC,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,KAAK,EAAE,MAAM,CAAC,YAAY;aAC7B,CAAC,CAAA;YAEF,MAAM,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC;gBAC9C,KAAK,EAAE,KAAK,CAAC,IAAI;gBACjB,IAAI,EAAE,iBAAiB,CAAC,gBAAgB;gBACxC,IAAI,EAAE,MAAM,CAAC,IAAI;aACpB,CAAC,CAAA;YAEF,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;YAC3B,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;QACjC,CAAC;QAED,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;IACrD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CACX,MAAsB,EACtB,OAAiB,EACjB,kBAA2B,IAAI,EAC/B,cAAuB,IAAI;QAE3B,qGAAqG;QACrG,wDAAwD;QACxD,IAAI,OAAO,EAAE,CAAC;YACV,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;YAChD,IAAI,CAAC,YAAY;gBAAE,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;QAC/C,CAAC;QAED,8FAA8F;QAC9F,MAAM,iBAAiB,GAAY,eAAe,CAAA;QAClD,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAA;QAC3C,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAA;QAClD,MAAM,SAAS,GAAY,EAAE,CAAA;QAC7B,MAAM,WAAW,GAAY,EAAE,CAAA;QAE/B,IAAI,WAAW,EAAE,CAAC;YACd,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;gBAC5B,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAA;gBAC/C,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAA;YACvD,CAAC,CAAC,CAAA;QACN,CAAC;QAED,IAAI,eAAe;YACf,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE,CACrC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC,CAC5D,CAAA;QAEL,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAA;QACxC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,iBAAiB,CAAC,CAAC,CAAA;QAE/D,kGAAkG;QAClG,MAAM,gBAAgB,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CACzC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,aAAa,IAAI,MAAM,CAAC,YAAY,CAC1D,CAAA;QAED,KAAK,MAAM,MAAM,IAAI,gBAAgB,EAAE,CAAC;YACpC,MAAM,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC;gBAC9C,KAAK,EAAE,KAAK,CAAC,IAAI;gBACjB,IAAI,EAAE,iBAAiB,CAAC,gBAAgB;gBACxC,IAAI,EAAE,MAAM,CAAC,IAAI;aACpB,CAAC,CAAA;YAEF,MAAM,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC;gBAC9C,KAAK,EAAE,KAAK,CAAC,IAAI;gBACjB,IAAI,EAAE,iBAAiB,CAAC,gBAAgB;gBACxC,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,KAAK,EAAE,MAAM,CAAC,YAAY;aAC7B,CAAC,CAAA;YAEF,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;YAC3B,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;QACjC,CAAC;QAED,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;IACrD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CAAC,IAAU;QACvB,MAAM,SAAS,GAAY,EAAE,CAAA;QAC7B,MAAM,WAAW,GAAY,EAAE,CAAA;QAC/B,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAA;QACxC,SAAS,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC,CAAA;QACxD,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAA;QACxC,WAAW,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC,CAAA;QAC1D,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;IACrD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ,CAAC,MAAqB;QAChC,MAAM,QAAQ,GAAG,MAAM,YAAY,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAA;QAC9D,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAA;QAE/C,MAAM,SAAS,GAAY,EAAE,CAAA;QAC7B,MAAM,WAAW,GAAY,EAAE,CAAA;QAC/B,SAAS,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC,CAAA;QACxD,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAA;QACtC,WAAW,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC,CAAA;QAC1D,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAA;QAC1C,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;IACrD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CACb,cAA8B,EAC9B,YAAoB;QAEpB,MAAM,IAAI,YAAY,CAClB,2DAA2D,CAC9D,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CACX,WAA2B,EAC3B,MAAmB;QAEnB,MAAM,KAAK,GACP,WAAW,YAAY,KAAK;YACxB,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAChD,MAAM,WAAW,GAAG,KAAK,CAAC,KAAK,EAAE,CAAA;QACjC,MAAM,SAAS,GAAY,EAAE,CAAA;QAC7B,MAAM,WAAW,GAAY,EAAE,CAAA;QAE/B,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,QAAQ,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,EAAE,CAC/C,CACJ,CAAA;QACD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,gBAAgB,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CACrD,CACJ,CAAA;QAED,sBAAsB;QACtB,MAAM,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,CACxC,CAAC,KAAK,EAAE,EAAE,CACN,KAAK,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC;YAC9B,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,IAAI,CAC3C,CAAA;QACD,IAAI,WAAW,EAAE,CAAC;YACd,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC,CAAA;YACvD,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC,CAAA;QAC3D,CAAC;aAAM,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;YACzB,MAAM,WAAW,GAAG,IAAI,UAAU,CAAC;gBAC/B,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,SAAS,CAAC,KAAK,EAAE;oBAClD,MAAM,CAAC,IAAI;iBACd,CAAC;gBACF,WAAW,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC;gBAC1B,QAAQ,EAAE,IAAI;aACjB,CAAC,CAAA;YACF,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;YACrC,WAAW,CAAC,OAAO,CAAC,IAAI,CACpB,IAAI,WAAW,CAAC;gBACZ,IAAI,EAAE,WAAW,CAAC,IAAI;gBACtB,WAAW,EAAE,WAAW,CAAC,WAAW;aACvC,CAAC,CACL,CAAA;YAED,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC,CAAA;YACvD,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC,CAAA;QAC3D,CAAC;QAED,IAAI,MAAM,CAAC,aAAa,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC;YAC9C,MAAM,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC;gBAC9C,KAAK,EAAE,KAAK,CAAC,IAAI;gBACjB,IAAI,EAAE,iBAAiB,CAAC,gBAAgB;gBACxC,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,KAAK,EAAE,MAAM,CAAC,YAAY;aAC7B,CAAC,CAAA;YAEF,MAAM,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC;gBAC9C,KAAK,EAAE,KAAK,CAAC,IAAI;gBACjB,IAAI,EAAE,iBAAiB,CAAC,gBAAgB;gBACxC,IAAI,EAAE,MAAM,CAAC,IAAI;aACpB,CAAC,CAAA;YAEF,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;YAC3B,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;QACjC,CAAC;QAED,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;QAEjD,WAAW,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;QAC7B,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAA;IAC/C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CACZ,WAA2B,EAC3B,OAAsB;QAEtB,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC3B,MAAM,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,MAAM,CAAC,CAAA;QAC7C,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CACd,WAA2B,EAC3B,oBAA0C,EAC1C,oBAA0C;QAE1C,MAAM,KAAK,GACP,WAAW,YAAY,KAAK;YACxB,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAChD,MAAM,SAAS,GACX,oBAAoB,YAAY,WAAW;YACvC,CAAC,CAAC,oBAAoB;YACtB,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,oBAAoB,CAAC,CAAA;QACpE,IAAI,CAAC,SAAS;YACV,MAAM,IAAI,YAAY,CAClB,WAAW,oBAAoB,2BAA2B,KAAK,CAAC,IAAI,UAAU,CACjF,CAAA;QAEL,IAAI,SAAS,CAAA;QACb,IAAI,oBAAoB,YAAY,WAAW,EAAE,CAAC;YAC9C,SAAS,GAAG,oBAAoB,CAAA;QACpC,CAAC;aAAM,CAAC;YACJ,SAAS,GAAG,SAAS,CAAC,KAAK,EAAE,CAAA;YAC7B,SAAS,CAAC,IAAI,GAAG,oBAAoB,CAAA;QACzC,CAAC;QAED,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,SAAS,EAAE,SAAS,CAAC,CAAA;IACzD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CACd,WAA2B,EAC3B,oBAA0C,EAC1C,SAAsB;QAEtB,MAAM,KAAK,GACP,WAAW,YAAY,KAAK;YACxB,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAChD,IAAI,WAAW,GAAG,KAAK,CAAC,KAAK,EAAE,CAAA;QAC/B,MAAM,SAAS,GAAY,EAAE,CAAA;QAC7B,MAAM,WAAW,GAAY,EAAE,CAAA;QAE/B,MAAM,SAAS,GACX,oBAAoB,YAAY,WAAW;YACvC,CAAC,CAAC,oBAAoB;YACtB,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CACd,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,oBAAoB,CACnD,CAAA;QACX,IAAI,CAAC,SAAS;YACV,MAAM,IAAI,YAAY,CAClB,WAAW,oBAAoB,2BAA2B,KAAK,CAAC,IAAI,UAAU,CACjF,CAAA;QAEL,IACI,SAAS,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI;YACjC,SAAS,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI;YACjC,SAAS,CAAC,MAAM,KAAK,SAAS,CAAC,MAAM;YACrC,SAAS,CAAC,OAAO,KAAK,SAAS,CAAC,OAAO;YACvC,SAAS,CAAC,aAAa,KAAK,SAAS,CAAC,aAAa;YACnD,SAAS,CAAC,YAAY,KAAK,SAAS,CAAC,YAAY,EACnD,CAAC;YACC,oDAAoD;YACpD,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,SAAS,CAAC,CAAA;YACvC,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,SAAS,CAAC,CAAA;YAEtC,sBAAsB;YACtB,WAAW,GAAG,KAAK,CAAC,KAAK,EAAE,CAAA;QAC/B,CAAC;aAAM,CAAC;YACJ,IACI,SAAS,CAAC,SAAS,KAAK,SAAS,CAAC,SAAS;gBAC3C,SAAS,CAAC,KAAK,KAAK,SAAS,CAAC,KAAK,EACrC,CAAC;gBACC,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,kBACjC,SAAS,CAAC,IACd,UAAU,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,SAAS,CAAC,EAAE,CACpD,CACJ,CAAA;gBACD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,kBACjC,SAAS,CAAC,IACd,UAAU,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,SAAS,CAAC,EAAE,CACpD,CACJ,CAAA;YACL,CAAC;YAED,IAAI,SAAS,CAAC,UAAU,KAAK,SAAS,CAAC,UAAU,EAAE,CAAC;gBAChD,IAAI,SAAS,CAAC,UAAU,EAAE,CAAC;oBACvB,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,kBAAkB,SAAS,CAAC,IAAI,iBAAiB,CACrD,CACJ,CAAA;oBACD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,kBAAkB,SAAS,CAAC,IAAI,gBAAgB,CACpD,CACJ,CAAA;gBACL,CAAC;qBAAM,CAAC;oBACJ,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,kBAAkB,SAAS,CAAC,IAAI,gBAAgB,CACpD,CACJ,CAAA;oBACD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,kBAAkB,SAAS,CAAC,IAAI,iBAAiB,CACrD,CACJ,CAAA;gBACL,CAAC;YACL,CAAC;YAED,IAAI,SAAS,CAAC,QAAQ,KAAK,SAAS,CAAC,QAAQ,EAAE,CAAC;gBAC5C,IAAI,SAAS,CAAC,QAAQ,KAAK,IAAI,EAAE,CAAC;oBAC9B,MAAM,WAAW,GAAG,IAAI,UAAU,CAAC;wBAC/B,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,SAAS,CAAC,KAAK,EAAE;4BAClD,SAAS,CAAC,IAAI;yBACjB,CAAC;wBACF,WAAW,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC;wBAC7B,QAAQ,EAAE,IAAI;qBACjB,CAAC,CAAA;oBACF,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;oBACrC,WAAW,CAAC,OAAO,CAAC,IAAI,CACpB,IAAI,WAAW,CAAC;wBACZ,IAAI,EAAE,WAAW,CAAC,IAAI;wBACtB,WAAW,EAAE,WAAW,CAAC,WAAW;qBACvC,CAAC,CACL,CAAA;oBAED,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC,CAAA;oBACvD,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC,CAAA;gBAC3D,CAAC;qBAAM,CAAC;oBACJ,MAAM,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;wBACnD,OAAO,CACH,KAAK,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC;4BAC9B,KAAK,CAAC,QAAQ,KAAK,IAAI;4BACvB,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CACpB,CAAC,UAAU,EAAE,EAAE,CAAC,UAAU,KAAK,SAAS,CAAC,IAAI,CAChD,CACJ,CAAA;oBACL,CAAC,CAAC,CAAA;oBACF,WAAW,CAAC,OAAO,CAAC,MAAM,CACtB,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,WAAY,CAAC,EACzC,CAAC,CACJ,CAAA;oBAED,MAAM,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,CACxC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,WAAY,CAAC,IAAI,CAChD,CAAA;oBACD,WAAW,CAAC,OAAO,CAAC,MAAM,CACtB,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,WAAY,CAAC,EACzC,CAAC,CACJ,CAAA;oBAED,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,WAAY,CAAC,CAAC,CAAA;oBACtD,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,WAAY,CAAC,CAAC,CAAA;gBAC9D,CAAC;YACL,CAAC;QACL,CAAC;QAED,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;QACjD,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAA;IAC/C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa,CACf,WAA2B,EAC3B,cAAoE;QAEpE,KAAK,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,cAAc,EAAE,CAAC;YACpD,MAAM,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,SAAS,EAAE,SAAS,CAAC,CAAA;QAC9D,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CACZ,WAA2B,EAC3B,YAAkC;QAElC,MAAM,KAAK,GACP,WAAW,YAAY,KAAK;YACxB,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAChD,MAAM,MAAM,GACR,YAAY,YAAY,WAAW;YAC/B,CAAC,CAAC,YAAY;YACd,CAAC,CAAC,KAAK,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAA;QAC9C,IAAI,CAAC,MAAM;YACP,MAAM,IAAI,YAAY,CAClB,WAAW,YAAY,6BAA6B,KAAK,CAAC,IAAI,GAAG,CACpE,CAAA;QAEL,MAAM,WAAW,GAAG,KAAK,CAAC,KAAK,EAAE,CAAA;QACjC,MAAM,SAAS,GAAY,EAAE,CAAA;QAC7B,MAAM,WAAW,GAAY,EAAE,CAAA;QAE/B,oBAAoB;QACpB,MAAM,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,CACxC,CAAC,KAAK,EAAE,EAAE,CACN,KAAK,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC;YAC9B,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,IAAI,CAC3C,CAAA;QACD,IAAI,WAAW,EAAE,CAAC;YACd,WAAW,CAAC,OAAO,CAAC,MAAM,CACtB,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,EACxC,CAAC,CACJ,CAAA;YACD,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC,CAAA;YACrD,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC,CAAA;QAC7D,CAAC;QAED,oBAAoB;QACpB,MAAM,WAAW,GAAG,WAAW,CAAC,MAAM,CAAC,IAAI,CACvC,CAAC,KAAK,EAAE,EAAE,CACN,CAAC,CAAC,KAAK,CAAC,WAAW;YACnB,KAAK,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC;YAC9B,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,IAAI,CAC3C,CAAA;QACD,IAAI,WAAW,EAAE,CAAC;YACd,WAAW,CAAC,MAAM,CAAC,MAAM,CACrB,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,EACvC,CAAC,CACJ,CAAA;YACD,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC,CAAA;YAC/D,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC,CAAA;QACvE,CAAC;QAED,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,gBAAgB,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CACrD,CACJ,CAAA;QACD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,QAAQ,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,EAAE,CAC/C,CACJ,CAAA;QAED,IAAI,MAAM,CAAC,aAAa,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC;YAC9C,MAAM,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC;gBAC9C,KAAK,EAAE,KAAK,CAAC,IAAI;gBACjB,IAAI,EAAE,iBAAiB,CAAC,gBAAgB;gBACxC,IAAI,EAAE,MAAM,CAAC,IAAI;aACpB,CAAC,CAAA;YACF,MAAM,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC;gBAC9C,KAAK,EAAE,KAAK,CAAC,IAAI;gBACjB,IAAI,EAAE,iBAAiB,CAAC,gBAAgB;gBACxC,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,KAAK,EAAE,MAAM,CAAC,YAAY;aAC7B,CAAC,CAAA;YAEF,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;YAC3B,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;QACjC,CAAC;QAED,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;QAEjD,WAAW,CAAC,YAAY,CAAC,MAAM,CAAC,CAAA;QAChC,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAA;IAC/C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CACb,WAA2B,EAC3B,OAAiC;QAEjC,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC3B,MAAM,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,MAAM,CAAC,CAAA;QAC9C,CAAC;IACL,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,gBAAgB,CAClB,WAA2B,EAC3B,WAAqB;QAErB,MAAM,IAAI,KAAK,CACX,kIAAkI,CACrI,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB,CACnB,WAA2B,EAC3B,OAAsB;QAEtB,MAAM,IAAI,KAAK,CACX,kIAAkI,CACrI,CAAA;IACL,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,cAAc,CAAC,WAA2B;QAC5C,MAAM,IAAI,KAAK,CACX,kIAAkI,CACrI,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,sBAAsB,CACxB,WAA2B,EAC3B,gBAA6B;QAE7B,MAAM,IAAI,YAAY,CAClB,wEAAwE,CAC3E,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,uBAAuB,CACzB,WAA2B,EAC3B,iBAAgC;QAEhC,MAAM,IAAI,YAAY,CAClB,wEAAwE,CAC3E,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,oBAAoB,CACtB,WAA2B,EAC3B,YAAkC;QAElC,MAAM,IAAI,YAAY,CAClB,wEAAwE,CAC3E,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,qBAAqB,CACvB,WAA2B,EAC3B,iBAAgC;QAEhC,MAAM,IAAI,YAAY,CAClB,wEAAwE,CAC3E,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,qBAAqB,CACvB,WAA2B,EAC3B,eAA2B;QAE3B,MAAM,KAAK,GACP,WAAW,YAAY,KAAK;YACxB,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAEhD,kGAAkG;QAClG,IAAI,CAAC,eAAe,CAAC,IAAI;YACrB,eAAe,CAAC,IAAI;gBAChB,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,mBAAmB,CAC9C,KAAK,EACL,eAAe,CAAC,UAAW,CAC9B,CAAA;QAET,MAAM,EAAE,GAAG,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,eAAe,CAAC,CAAA;QAChE,MAAM,IAAI,GAAG,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,eAAe,CAAC,CAAA;QAChE,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;QACnC,KAAK,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAA;IAC7C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,sBAAsB,CACxB,WAA2B,EAC3B,gBAA8B;QAE9B,MAAM,QAAQ,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC,eAAe,EAAE,EAAE,CACtD,IAAI,CAAC,qBAAqB,CAAC,WAAW,EAAE,eAAe,CAAC,CAC3D,CAAA;QACD,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;IAC/B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,mBAAmB,CACrB,WAA2B,EAC3B,WAAgC;QAEhC,MAAM,KAAK,GACP,WAAW,YAAY,KAAK;YACxB,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAChD,MAAM,eAAe,GACjB,WAAW,YAAY,UAAU;YAC7B,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,WAAW,CAAC,CAAA;QAC1D,IAAI,CAAC,eAAe;YAChB,MAAM,IAAI,YAAY,CAClB,oDAAoD,KAAK,CAAC,IAAI,EAAE,CACnE,CAAA;QAEL,MAAM,EAAE,GAAG,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,eAAe,CAAC,CAAA;QAC9D,MAAM,IAAI,GAAG,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,eAAe,CAAC,CAAA;QAClE,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;QACnC,KAAK,CAAC,qBAAqB,CAAC,eAAe,CAAC,CAAA;IAChD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,oBAAoB,CACtB,WAA2B,EAC3B,gBAA8B;QAE9B,MAAM,QAAQ,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC,eAAe,EAAE,EAAE,CACtD,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE,eAAe,CAAC,CACzD,CAAA;QACD,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;IAC/B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,yBAAyB,CAC3B,WAA2B,EAC3B,mBAAmC;QAEnC,MAAM,IAAI,YAAY,CAClB,iDAAiD,CACpD,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,0BAA0B,CAC5B,WAA2B,EAC3B,oBAAsC;QAEtC,MAAM,IAAI,YAAY,CAClB,iDAAiD,CACpD,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,uBAAuB,CACzB,WAA2B,EAC3B,eAAwC;QAExC,MAAM,IAAI,YAAY,CAClB,iDAAiD,CACpD,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,wBAAwB,CAC1B,WAA2B,EAC3B,oBAAsC;QAEtC,MAAM,IAAI,YAAY,CAClB,iDAAiD,CACpD,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB,CAClB,WAA2B,EAC3B,UAA2B;QAE3B,MAAM,KAAK,GACP,WAAW,YAAY,KAAK;YACxB,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAEhD,gFAAgF;QAChF,IAAI,CAAC,UAAU,CAAC,IAAI;YAChB,UAAU,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CAC3D,KAAK,EACL,UAAU,CAAC,WAAW,EACtB,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,EAC7B,UAAU,CAAC,qBAAqB,CACnC,CAAA;QAEL,MAAM,EAAE,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAA;QACtD,MAAM,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAA;QACtD,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;QACnC,KAAK,CAAC,aAAa,CAAC,UAAU,CAAC,CAAA;IACnC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB,CACnB,WAA2B,EAC3B,WAA8B;QAE9B,KAAK,MAAM,UAAU,IAAI,WAAW,EAAE,CAAC;YACnC,MAAM,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,UAAU,CAAC,CAAA;QACxD,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAChB,WAA2B,EAC3B,gBAA0C;QAE1C,MAAM,KAAK,GACP,WAAW,YAAY,KAAK;YACxB,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAChD,MAAM,UAAU,GACZ,gBAAgB,YAAY,eAAe;YACvC,CAAC,CAAC,gBAAgB;YAClB,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,IAAI,KAAK,gBAAgB,CAAC,CAAA;QACtE,IAAI,CAAC,UAAU;YACX,MAAM,IAAI,YAAY,CAClB,+CAA+C,KAAK,CAAC,IAAI,EAAE,CAC9D,CAAA;QAEL,MAAM,EAAE,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAA;QACpD,MAAM,IAAI,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAA;QACxD,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;QACnC,KAAK,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAA;IACtC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe,CACjB,WAA2B,EAC3B,WAA8B;QAE9B,KAAK,MAAM,UAAU,IAAI,WAAW,EAAE,CAAC;YACnC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,UAAU,CAAC,CAAA;QACtD,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CACb,WAA2B,EAC3B,KAAiB;QAEjB,MAAM,KAAK,GACP,WAAW,YAAY,KAAK;YACxB,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAEhD,sFAAsF;QACtF,IAAI,CAAC,KAAK,CAAC,IAAI;YAAE,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;QAElE,MAAM,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;QAC5C,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;QAC5C,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;QACnC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAA;IACzB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa,CACf,WAA2B,EAC3B,OAAqB;QAErB,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE,CAAC;YAC1B,MAAM,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,KAAK,CAAC,CAAA;QAC9C,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CACX,WAA2B,EAC3B,WAAgC;QAEhC,MAAM,KAAK,GACP,WAAW,YAAY,KAAK;YACxB,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAChD,MAAM,KAAK,GACP,WAAW,YAAY,UAAU;YAC7B,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,WAAW,CAAC,CAAA;QAC3D,IAAI,CAAC,KAAK;YACN,MAAM,IAAI,YAAY,CAClB,kBAAkB,WAAW,2BAA2B,KAAK,CAAC,IAAI,EAAE,CACvE,CAAA;QAEL,sFAAsF;QACtF,IAAI,CAAC,KAAK,CAAC,IAAI;YAAE,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;QAElE,MAAM,EAAE,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;QAC1C,MAAM,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;QAC9C,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;QACnC,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAA;IAC5B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CACb,WAA2B,EAC3B,OAAqB;QAErB,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE,CAAC;YAC1B,MAAM,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,KAAK,CAAC,CAAA;QAC5C,CAAC;IACL,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,UAAU,CAAC,SAAiB;QAC9B,MAAM,IAAI,CAAC,KAAK,CAAC,eAAe,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,aAAa,CAAC,CAAA;IAC5E,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa;QACf,qBAAqB;QACrB,MAAM,qBAAqB,GACvB,gEAAgE;YAChE,0CAA0C;YAC1C,wHAAwH,CAAA;QAC5H,MAAM,gBAAgB,GAAoB,MAAM,IAAI,CAAC,KAAK,CACtD,qBAAqB,CACxB,CAAA;QAED,2BAA2B;QAC3B,MAAM,kBAAkB,GACpB,+GAA+G;YAC/G,oDAAoD;YACpD,gGAAgG,CAAA;QACpG,MAAM,aAAa,GAAoB,MAAM,IAAI,CAAC,KAAK,CACnD,kBAAkB,CACrB,CAAA;QAED,oBAAoB;QACpB,oIAAoI;QACpI,6DAA6D;QAC7D,4BAA4B;QAC5B,IAAI;QAEJ,qBAAqB;QACrB,MAAM,eAAe,GACjB,gEAAgE;YAChE,yCAAyC;YACzC,0FAA0F,CAAA;QAC9F,MAAM,gBAAgB,GAAoB,MAAM,IAAI,CAAC,KAAK,CACtD,eAAe,CAClB,CAAA;QAED,IACI,CAAC,gBAAgB,CAAC,MAAM;YACxB,CAAC,aAAa,CAAC,MAAM;YACrB,6BAA6B;YAC7B,CAAC,gBAAgB,CAAC,MAAM;YAExB,OAAM;QAEV,MAAM,0BAA0B,GAAG,IAAI,CAAC,mBAAmB,CAAA;QAC3D,IAAI,CAAC,0BAA0B;YAAE,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAA;QAC9D,IAAI,CAAC;YACD,KAAK,MAAM,KAAK,IAAI,gBAAgB,EAAE,CAAC;gBACnC,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAA;YACxC,CAAC;YACD,KAAK,MAAM,KAAK,IAAI,aAAa,EAAE,CAAC;gBAChC,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAA;YACxC,CAAC;YAED,uCAAuC;YACvC,2CAA2C;YAC3C,IAAI;YAEJ,KAAK,MAAM,KAAK,IAAI,gBAAgB,EAAE,CAAC;gBACnC,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAA;YACxC,CAAC;YAED,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAA;QAClC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAI,CAAC;gBACD,2DAA2D;gBAC3D,IAAI,CAAC,0BAA0B;oBAC3B,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAA;YACxC,CAAC;YAAC,OAAO,aAAa,EAAE,CAAC,CAAA,CAAC;YAC1B,MAAM,KAAK,CAAA;QACf,CAAC;IACL,CAAC;IAED,4EAA4E;IAC5E,mBAAmB;IACnB,4EAA4E;IAE5E;;OAEG;IACH,KAAK,CAAC,kBAAkB;QACpB,KAAK,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,IAAI,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,CAAC;YAC7D,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;gBACzB,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,UAAU,CAAC,CAAA;YACvC,CAAC;iBAAM,CAAC;gBACJ,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,UAAU,CAAC,CAAA;YAC3C,CAAC;QACL,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,oBAAoB;QACtB,KAAK,MAAM,EACP,KAAK,EACL,UAAU,GACb,IAAI,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO,EAAE,EAAE,CAAC;YAC1C,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;gBACzB,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,UAAU,CAAC,CAAA;YACvC,CAAC;iBAAM,CAAC;gBACJ,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,UAAU,CAAC,CAAA;YAC3C,CAAC;QACL,CAAC;IACL,CAAC;IAED,4EAA4E;IAC5E,oBAAoB;IACpB,4EAA4E;IAElE,KAAK,CAAC,SAAS,CAAC,SAAoB;QAC1C,2EAA2E;QAC3E,mBAAmB;QACnB,gBAAgB;QAChB,IAAI;QACJ,EAAE;QACF,oBAAoB;QACpB,qBAAqB;QACrB,IAAI;QACJ,EAAE;QACF,qCAAqC;QACrC,0CAA0C;QAC1C,kBAAkB;QAClB,EAAE;QACF,gBAAgB;QAChB,yEAAyE;QACzE,8CAA8C;QAC9C,mBAAmB;QACnB,sGAAsG;QACtG,6DAA6D;QAC7D,2BAA2B;QAC3B,+DAA+D;QAC/D,mBAAmB;QACnB,SAAS;QACT,0CAA0C;QAC1C,wCAAwC;QACxC,8BAA8B;QAC9B,qCAAqC;QACrC,6DAA6D;QAC7D,uCAAuC;QACvC,kBAAkB;QAClB,KAAK;QAEL,OAAO,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAA;IAC9B,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,UAAU,CAAC,UAAqB;QAC5C,IAAI,UAAU,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACxC,OAAO,EAAE,CAAA;QACb,CAAC;QAED,MAAM,QAAQ,GAA6B,EAAE,CAAA;QAE7C,IAAI,CAAC,UAAU,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC;YACpC,4DAA4D;YAC5D,MAAM,SAAS,GACX,wBAAwB;gBACxB,yCAAyC;gBACzC,0FAA0F,CAAA;YAC9F,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAA;QACnD,CAAC;aAAM,CAAC;YACJ,MAAM,SAAS,GACX,wBAAwB;gBACxB,yCAAyC;gBACzC,2FAA2F;gBAC3F,0BAA0B,UAAU;qBAC/B,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,IAAI,SAAS,GAAG,CAAC;qBACpC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAA;YAEtB,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAA;QACnD,CAAC;QAED,yDAAyD;QACzD,IAAI,CAAC,QAAQ,CAAC,MAAM;YAAE,OAAO,EAAE,CAAA;QAE/B,MAAM,gBAAgB,GAAG,QAAQ;aAC5B,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,IAAI,OAAO,CAAC,UAAU,GAAG,CAAC;aAC3C,IAAI,CAAC,IAAI,CAAC,CAAA;QAEf,MAAM,UAAU,GAAG,kIAAkI,gBAAgB,GAAG,CAAA;QAExK,MAAM,aAAa,GACf,yDAAyD;YACzD,2DAA2D;YAC3D,6HAA6H;YAC7H,sHAAsH;YACtH,iCAAiC,gBAAgB,GAAG,CAAA;QAExD,MAAM,UAAU,GACZ,6HAA6H;YAC7H,gDAAgD;YAChD,6GAA6G;YAC7G,mDAAmD;YACnD,mGAAmG,gBAAgB,IAAI;YACvH,6EAA6E,CAAA;QAEjF,MAAM,SAAS,GACX,4GAA4G;YAC5G,2DAA2D;YAC3D,oIAAoI;YACpI,4HAA4H;YAC5H,gHAAgH;YAChH,iCAAiC,gBAAgB,6DAA6D,CAAA;QAElH,MAAM,cAAc,GAChB,qFAAqF;YACrF,8GAA8G;YAC9G,iDAAiD;YACjD,2DAA2D;YAC3D,6HAA6H;YAC7H,mIAAmI;YACnI,kIAAkI;YAClI,oIAAoI;YACpI,sHAAsH;YACtH,iCAAiC,gBAAgB,GAAG,CAAA;QAExD,MAAM,CACF,SAAS,EACT,aAAa,EACb,SAAS,EACT,QAAQ,EACR,aAAa,EAChB,GAAsB,MAAM,OAAO,CAAC,GAAG,CAAC;YACrC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;YACtB,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC;YACzB,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;YACtB,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC;YACrB,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC;SAC7B,CAAC,CAAA;QAEF,kCAAkC;QAClC,OAAO,OAAO,CAAC,GAAG,CACd,QAAQ,CAAC,GAAG,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;YAC3B,MAAM,KAAK,GAAG,IAAI,KAAK,EAAE,CAAA;YAEzB,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAA;YAE9D,yCAAyC;YACzC,KAAK,CAAC,OAAO,GAAG,MAAM,OAAO,CAAC,GAAG,CAC7B,SAAS;iBACJ,MAAM,CACH,CAAC,QAAQ,EAAE,EAAE,CACT,QAAQ,CAAC,YAAY,CAAC;gBACtB,OAAO,CAAC,YAAY,CAAC,CAC5B;iBACA,GAAG,CAAC,KAAK,EAAE,QAAQ,EAAE,EAAE;gBACpB,MAAM,mBAAmB,GAAG,SAAS,CAAC,MAAM,CACxC,CAAC,OAAO,EAAE,EAAE;oBACR,OAAO,CACH,OAAO,CAAC,YAAY,CAAC;wBACjB,OAAO,CAAC,YAAY,CAAC;wBACzB,OAAO,CAAC,aAAa,CAAC;4BAClB,QAAQ,CAAC,aAAa,CAAC;wBAC3B,OAAO,CAAC,WAAW,CAAC,KAAK,IAAI,CAChC,CAAA;gBACL,CAAC,CACJ,CAAA;gBAED,MAAM,aAAa,GACf,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,IAAI,CAChC,CAAC,QAAQ,EAAE,EAAE,CACT,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC;oBACxB,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAClC,CAAA;gBACL,MAAM,eAAe,GACjB,mBAAmB,CAAC,MAAM,GAAG,CAAC;oBAC9B,aAAa;oBACb,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;wBACjC,OAAO,mBAAmB,CAAC,IAAI,CAC3B,CAAC,WAAW,EAAE,EAAE;4BACZ,OAAO,CACH,KAAK,CAAC,IAAI;gCACN,WAAW,CAAC,YAAY,CAAC;gCAC7B,KAAK,CAAC,WAAW,KAAK,KAAK,CAC9B,CAAA;wBACL,CAAC,CACJ,CAAA;oBACL,CAAC,CAAC,CAAA;gBAEN,MAAM,qBAAqB,GACvB,mBAAmB,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,EAAE;oBACtC,OAAO,SAAS,CAAC,IAAI,CACjB,CAAC,OAAO,EAAE,EAAE,CACR,OAAO,CAAC,YAAY,CAAC;wBACjB,WAAW,CAAC,YAAY,CAAC;wBAC7B,OAAO,CAAC,aAAa,CAAC;4BAClB,QAAQ,CAAC,aAAa,CAAC,CAClC,CAAA;gBACL,CAAC,CAAC,CAAA;gBAEN,MAAM,WAAW,GAAG,IAAI,WAAW,EAAE,CAAA;gBACrC,WAAW,CAAC,IAAI,GAAG,QAAQ,CAAC,aAAa,CAAC,CAAA;gBAE1C,IAAI,QAAQ,GACR,QAAQ,CAAC,cAAc,CAAC,CAAC,WAAW,EAAE,CAAA;gBAC1C,IAAI,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;oBACnC,WAAW,CAAC,OAAO,GAAG,IAAI,CAAA;oBAC1B,QAAQ,GAAG,QAAQ,CAAC,SAAS,CACzB,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EACzB,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,CACxB,CAAA;gBACL,CAAC;gBAED,IAAI,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;oBAC/B,WAAW,CAAC,IAAI,GAAG,QAAQ,CAAC,SAAS,CACjC,CAAC,EACD,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,CACxB,CAAA;gBACL,CAAC;qBAAM,CAAC;oBACJ,WAAW,CAAC,IAAI,GAAG,QAAQ,CAAA;gBAC/B,CAAC;gBAED,IACI,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,OAAO,CACrC,WAAW,CAAC,IAAkB,CACjC,KAAK,CAAC,CAAC,EACV,CAAC;oBACC,WAAW,CAAC,MAAM,GAAG,QAAQ,CAAC,SAAS,CACnC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EACzB,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,CACxB,CAAA;gBACL,CAAC;gBAED,IAAI,QAAQ,CAAC,cAAc,CAAC,KAAK,QAAQ,EAAE,CAAC;oBACxC,WAAW,CAAC,YAAY;wBACpB,QAAQ,CAAC,uBAAuB,CAAC,CAAA;oBACrC,WAAW,CAAC,aAAa,GAAG,QAAQ,CAAA;oBAEpC,0GAA0G;oBAC1G,MAAM,iBAAiB,GACnB,IAAI,CAAC,wBAAwB,CAAC;wBAC1B,KAAK,EAAE,OAAO,CAAC,YAAY,CAAC;wBAC5B,IAAI,EAAE,iBAAiB,CAAC,gBAAgB;wBACxC,IAAI,EAAE,WAAW,CAAC,IAAI;qBACzB,CAAC,CAAA;oBAEN,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,KAAK,CAC5B,iBAAiB,CAAC,KAAK,EACvB,iBAAiB,CAAC,UAAU,CAC/B,CAAA;oBAED,IAAI,OAAO,CAAC,CAAC,CAAC,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;wBACjC,WAAW,CAAC,YAAY,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAA;oBAC/C,CAAC;yBAAM,CAAC;wBACJ,WAAW,CAAC,YAAY,GAAG,EAAE,CAAA;oBACjC,CAAC;gBACL,CAAC;gBAED,WAAW,CAAC,QAAQ;oBAChB,mBAAmB,CAAC,MAAM,GAAG,CAAC;wBAC9B,CAAC,eAAe;wBAChB,CAAC,qBAAqB,CAAA;gBAC1B,WAAW,CAAC,UAAU;oBAClB,QAAQ,CAAC,aAAa,CAAC,KAAK,KAAK,CAAA;gBACrC,WAAW,CAAC,SAAS,GAAG,aAAa,CAAC,IAAI,CACtC,CAAC,YAAY,EAAE,EAAE;oBACb,OAAO,CACH,YAAY,CAAC,YAAY,CAAC;wBACtB,QAAQ,CAAC,YAAY,CAAC;wBAC1B,YAAY,CAAC,aAAa,CAAC;4BACvB,QAAQ,CAAC,aAAa,CAAC,CAC9B,CAAA;gBACL,CAAC,CACJ,CAAA;gBAED,OAAO,WAAW,CAAA;YACtB,CAAC,CAAC,CACT,CAAA;YAED,MAAM,gBAAgB,GAAG,aAAa,CAAC,MAAM,CACzC,CAAC,YAAY,EAAE,EAAE;gBACb,OAAO,CACH,YAAY,CAAC,YAAY,CAAC,KAAK,OAAO,CAAC,YAAY,CAAC,CACvD,CAAA;YACL,CAAC,CACJ,CAAA;YAED,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,IAAI,CAC7B,gBAAgB,EAChB,CAAC,YAAY,EAAE,EAAE,CAAC,YAAY,CAAC,iBAAiB,CAAC,CACpD,CAAC,GAAG,CAAC,CAAC,YAAY,EAAE,EAAE;gBACnB,MAAM,WAAW,GAAG,gBAAgB,CAAC,MAAM,CACvC,CAAC,IAAI,EAAE,EAAE,CACL,IAAI,CAAC,iBAAiB,CAAC;oBACvB,YAAY,CAAC,iBAAiB,CAAC,CACtC,CAAA;gBACD,OAAO,IAAI,eAAe,CAAC;oBACvB,IAAI,EAAE,YAAY,CAAC,iBAAiB,CAAC;oBACrC,WAAW,EAAE,QAAQ,CAAC,IAAI,CACtB,WAAW,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CACjD;oBACD,kBAAkB,EACd,YAAY,CAAC,yBAAyB,CAAC;oBAC3C,mBAAmB,EACf,YAAY,CAAC,uBAAuB,CAAC;oBACzC,qBAAqB,EAAE,QAAQ,CAAC,IAAI,CAChC,WAAW,CAAC,GAAG,CACX,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAC3C,CACJ;oBACD,QAAQ,EAAE,YAAY,CAAC,aAAa,CAAC;oBACrC,QAAQ,EAAE,YAAY,CAAC,aAAa,CAAC;iBACxC,CAAC,CAAA;YACN,CAAC,CAAC,CAAA;YAEF,MAAM,YAAY,GAAG,SAAS,CAAC,MAAM,CACjC,CAAC,OAAO,EAAE,EAAE,CACR,OAAO,CAAC,YAAY,CAAC,KAAK,OAAO,CAAC,YAAY,CAAC,CACtD,CAAA;YAED,KAAK,CAAC,OAAO,GAAG,QAAQ,CAAC,IAAI,CACzB,YAAY,EACZ,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,YAAY,CAAC,CACrC,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE;gBACjB,MAAM,OAAO,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,EAAE;oBAC1C,OAAO,KAAK,CAAC,YAAY,CAAC,KAAK,UAAU,CAAC,YAAY,CAAC,CAAA;gBAC3D,CAAC,CAAC,CAAA;gBAEF,OAAO,IAAI,UAAU,CAAoB;oBACrC,KAAK,EAAE,KAAK;oBACZ,IAAI,EAAE,UAAU,CAAC,YAAY,CAAC;oBAC9B,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC;oBACjD,QAAQ,EAAE,UAAU,CAAC,WAAW,CAAC;oBACjC,cAAc,EAAE,UAAU,CAAC,kBAAkB,CAAC;iBACjD,CAAC,CAAA;YACN,CAAC,CAAC,CAAA;YAEF,MAAM,WAAW,GAAG,QAAQ,CAAC,MAAM,CAC/B,CAAC,OAAO,EAAE,EAAE,CACR,OAAO,CAAC,YAAY,CAAC,KAAK,OAAO,CAAC,YAAY,CAAC,CACtD,CAAA;YAED,KAAK,CAAC,MAAM,GAAG,QAAQ,CAAC,IAAI,CACxB,WAAW,EACX,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAC1C,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE;gBACjB,MAAM,MAAM,GAAG,WAAW,CAAC,MAAM,CAC7B,CAAC,GAAG,EAAE,EAAE,CACJ,GAAG,CAAC,iBAAiB,CAAC;oBACtB,UAAU,CAAC,iBAAiB,CAAC,CACpC,CAAA;gBACD,OAAO,IAAI,UAAU,CAAC;oBAClB,IAAI,EAAE,UAAU,CAAC,iBAAiB,CAAC;oBACnC,WAAW,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC;oBAChD,UAAU,EAAE,UAAU,CAAC,cAAc,CAAC;iBACzC,CAAC,CAAA;YACN,CAAC,CAAC,CAAA;YAEF,OAAO,KAAK,CAAA;QAChB,CAAC,CAAC,CACL,CAAA;IACL,CAAC;IAED;;OAEG;IACO,cAAc,CAAC,KAAY,EAAE,iBAA2B;QAC9D,MAAM,iBAAiB,GAAG,KAAK,CAAC,OAAO;aAClC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;aAClD,IAAI,CAAC,IAAI,CAAC,CAAA;QACf,IAAI,GAAG,GAAG,gBAAgB,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,iBAAiB,EAAE,CAAA;QAExE,4GAA4G;QAC5G,qEAAqE;QACrE,KAAK,CAAC,OAAO;aACR,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC;aACnC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;YAChB,MAAM,kBAAkB,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;gBACpD,OAAO,CACH,KAAK,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC;oBAC9B,CAAC,CAAC,KAAK,CAAC,QAAQ;oBAChB,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAChD,CAAA;YACL,CAAC,CAAC,CAAA;YACF,MAAM,uBAAuB,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE;gBAC1D,OAAO,CACH,MAAM,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC;oBAC/B,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CACjD,CAAA;YACL,CAAC,CAAC,CAAA;YACF,IAAI,CAAC,kBAAkB,IAAI,CAAC,uBAAuB;gBAC/C,KAAK,CAAC,OAAO,CAAC,IAAI,CACd,IAAI,UAAU,CAAC;oBACX,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,oBAAoB,CACrD,KAAK,EACL,CAAC,MAAM,CAAC,IAAI,CAAC,CAChB;oBACD,WAAW,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC;oBAC1B,QAAQ,EAAE,IAAI;iBACjB,CAAC,CACL,CAAA;QACT,CAAC,CAAC,CAAA;QAEN,wHAAwH;QACxH,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC3B,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;gBAC7B,MAAM,WAAW,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAClC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,CACxC,CAAA;gBACD,IAAI,CAAC,WAAW,EAAE,CAAC;oBACf,KAAK,CAAC,OAAO,CAAC,IAAI,CACd,IAAI,UAAU,CAAC;wBACX,IAAI,EAAE,MAAM,CAAC,IAAI;wBACjB,WAAW,EAAE,MAAM,CAAC,WAAW;wBAC/B,QAAQ,EAAE,IAAI;qBACjB,CAAC,CACL,CAAA;gBACL,CAAC;YACL,CAAC,CAAC,CAAA;QACN,CAAC;QAED,IAAI,KAAK,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC1B,MAAM,SAAS,GAAG,KAAK,CAAC,MAAM;iBACzB,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;gBACX,MAAM,SAAS,GAAG,KAAK,CAAC,IAAI;oBACxB,CAAC,CAAC,KAAK,CAAC,IAAI;oBACZ,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,mBAAmB,CAC9C,KAAK,EACL,KAAK,CAAC,UAAW,CACpB,CAAA;gBACP,OAAO,gBAAgB,SAAS,aAAa,KAAK,CAAC,UAAU,GAAG,CAAA;YACpE,CAAC,CAAC;iBACD,IAAI,CAAC,IAAI,CAAC,CAAA;YAEf,GAAG,IAAI,KAAK,SAAS,EAAE,CAAA;QAC3B,CAAC;QAED,IAAI,KAAK,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,IAAI,iBAAiB,EAAE,CAAC;YACpD,MAAM,cAAc,GAAG,KAAK,CAAC,WAAW;iBACnC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE;gBACR,MAAM,WAAW,GAAG,EAAE,CAAC,WAAW;qBAC7B,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,KAAK,UAAU,IAAI,CAAC;qBACxC,IAAI,CAAC,IAAI,CAAC,CAAA;gBACf,IAAI,CAAC,EAAE,CAAC,IAAI;oBACR,EAAE,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACnD,KAAK,EACL,EAAE,CAAC,WAAW,EACd,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,EACrB,EAAE,CAAC,qBAAqB,CAC3B,CAAA;gBACL,MAAM,qBAAqB,GAAG,EAAE,CAAC,qBAAqB;qBACjD,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,KAAK,UAAU,IAAI,CAAC;qBACxC,IAAI,CAAC,IAAI,CAAC,CAAA;gBAEf,OAAO,gBACH,EAAE,CAAC,IACP,mBAAmB,WAAW,gBAAgB,IAAI,CAAC,UAAU,CACzD,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,CACxB,KAAK,qBAAqB,GAAG,CAAA;YAClC,CAAC,CAAC;iBACD,IAAI,CAAC,IAAI,CAAC,CAAA;YAEf,GAAG,IAAI,KAAK,cAAc,EAAE,CAAA;QAChC,CAAC;QAED,GAAG,IAAI,GAAG,CAAA;QAEV,MAAM,cAAc,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CACvC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,SAAS,CAC/B,CAAA;QACD,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC5B,MAAM,WAAW,GAAG,cAAc;iBAC7B,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;iBAChD,IAAI,CAAC,IAAI,CAAC,CAAA;YACf,GAAG,IAAI,iBAAiB,WAAW,GAAG,CAAA;QAC1C,CAAC;QAED,OAAO,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;IACzB,CAAC;IAED;;OAEG;IACO,YAAY,CAAC,WAA2B;QAC9C,OAAO,IAAI,KAAK,CAAC,cAAc,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE,CAAC,CAAA;IAClE,CAAC;IAES,aAAa,CAAC,IAAU;QAC9B,MAAM,kBAAkB,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE,CAAA;QACnE,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAA;QAEtC,MAAM,UAAU,GACZ,OAAO,IAAI,CAAC,UAAU,KAAK,QAAQ;YAC/B,CAAC,CAAC,IAAI,CAAC,UAAU;YACjB,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,QAAQ,EAAE,CAAA;QACrD,OAAO,IAAI,KAAK,CACZ,UAAU,kBAAkB,QAAQ,QAAQ,4BAA4B,UAAU,EAAE,CACvF,CAAA;IACL,CAAC;IAES,KAAK,CAAC,uBAAuB,CAAC,IAAU;QAC9C,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA;QAEpE,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY;YAC1B,CAAC,CAAC,iBAAiB,CAAC,iBAAiB;YACrC,CAAC,CAAC,iBAAiB,CAAC,IAAI,CAAA;QAC5B,MAAM,UAAU,GACZ,OAAO,IAAI,CAAC,UAAU,KAAK,QAAQ;YAC/B,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE;YACxB,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,QAAQ,EAAE,CAAA;QACrD,OAAO,IAAI,CAAC,wBAAwB,CAAC;YACjC,IAAI;YACJ,MAAM;YACN,IAAI;YACJ,KAAK,EAAE,UAAU;SACpB,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACO,WAAW,CAAC,IAAU;QAC5B,MAAM,kBAAkB,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE,CAAA;QACnE,OAAO,IAAI,KAAK,CACZ,QAAQ,kBAAkB,QAAQ,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAC5D,CAAA;IACL,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,uBAAuB,CAAC,IAAU;QAC9C,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA;QAEpE,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY;YAC1B,CAAC,CAAC,iBAAiB,CAAC,iBAAiB;YACrC,CAAC,CAAC,iBAAiB,CAAC,IAAI,CAAA;QAC5B,OAAO,IAAI,CAAC,wBAAwB,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAA;IAChE,CAAC;IAED;;OAEG;IACO,cAAc,CAAC,KAAY,EAAE,KAAiB;QACpD,MAAM,OAAO,GAAG,KAAK,CAAC,WAAW;aAC5B,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;aACnD,IAAI,CAAC,IAAI,CAAC,CAAA;QACf,IAAI,SAAS,GAAG,EAAE,CAAA;QAClB,IAAI,KAAK,CAAC,QAAQ;YAAE,SAAS,IAAI,SAAS,CAAA;QAC1C,IAAI,KAAK,CAAC,cAAc;YAAE,SAAS,IAAI,gBAAgB,CAAA;QAEvD,OAAO,IAAI,KAAK,CACZ,UAAU,SAAS,WAAW,KAAK,CAAC,IAAI,SAAS,IAAI,CAAC,UAAU,CAC5D,KAAK,CACR,KAAK,OAAO,GAAG,CACnB,CAAA;IACL,CAAC;IAED;;OAEG;IACO,YAAY,CAClB,KAAY,EACZ,WAAgC;QAEhC,MAAM,SAAS,GACX,WAAW,YAAY,UAAU,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW,CAAA;QACtE,OAAO,IAAI,KAAK,CAAC,gBAAgB,SAAS,IAAI,CAAC,CAAA;IACnD,CAAC;IAED;;OAEG;IACO,wBAAwB,CAC9B,KAAY,EACZ,eAA2B;QAE3B,OAAO,IAAI,KAAK,CACZ,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,qBACjC,eAAe,CAAC,IACpB,aAAa,eAAe,CAAC,UAAU,GAAG,CAC7C,CAAA;IACL,CAAC;IAED;;OAEG;IACO,sBAAsB,CAC5B,KAAY,EACZ,WAAgC;QAEhC,MAAM,SAAS,GACX,WAAW,YAAY,UAAU,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW,CAAA;QACtE,OAAO,IAAI,KAAK,CACZ,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,sBAAsB,SAAS,IAAI,CACvC,CAAA;IACL,CAAC;IAED;;OAEG;IACO,mBAAmB,CACzB,KAAY,EACZ,UAA2B;QAE3B,MAAM,WAAW,GAAG,UAAU,CAAC,WAAW;aACrC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;aAC3C,IAAI,CAAC,IAAI,CAAC,CAAA;QACf,MAAM,qBAAqB,GAAG,UAAU,CAAC,qBAAqB;aACzD,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;aAC3C,IAAI,CAAC,GAAG,CAAC,CAAA;QACd,MAAM,GAAG,GACL,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,qBACjC,UAAU,CAAC,IACf,mBAAmB,WAAW,IAAI;YAClC,cAAc,IAAI,CAAC,UAAU,CACzB,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,CAChC,KAAK,qBAAqB,GAAG,CAAA;QAElC,OAAO,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;IACzB,CAAC;IAED;;OAEG;IACO,iBAAiB,CACvB,KAAY,EACZ,gBAA0C;QAE1C,MAAM,cAAc,GAChB,gBAAgB,YAAY,eAAe;YACvC,CAAC,CAAC,gBAAgB,CAAC,IAAI;YACvB,CAAC,CAAC,gBAAgB,CAAA;QAC1B,OAAO,IAAI,KAAK,CACZ,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,sBAAsB,cAAc,IAAI,CAC5C,CAAA;IACL,CAAC;IAED;;OAEG;IACO,UAAU,CAAC,MAA6B;QAC9C,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA;QACxD,OAAO,KAAK,SAAS,IAAI,CAAA;IAC7B,CAAC;IAED;;OAEG;IACO,oBAAoB,CAAC,MAAmB;QAC9C,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CACzB,MAAM,CAAC,IAAI,CACd,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE,CAAA;QAEpD,qDAAqD;QACrD,IAAI,MAAM,CAAC,aAAa,KAAK,QAAQ,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC;YAC3D,CAAC,IAAI,QAAQ,MAAM,CAAC,YAAY,UAAU,CAAA;QAC9C,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,MAAM,CAAC,UAAU;gBAAE,CAAC,IAAI,WAAW,CAAA;QAC5C,CAAC;QAED,OAAO,CAAC,CAAA;IACZ,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,cAAc,CAC1B,SAA0B,EAC1B,WAA4B;QAE5B,IAAI,SAAS,YAAY,KAAK;YAAE,SAAS,GAAG,CAAC,SAAS,CAAC,CAAA;QACvD,IAAI,WAAW,YAAY,KAAK;YAAE,WAAW,GAAG,CAAC,WAAW,CAAC,CAAA;QAE7D,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,CAAA;QAC7C,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,WAAW,CAAC,CAAA;QAEjD,8EAA8E;QAC9E,IAAI,IAAI,CAAC,aAAa,KAAK,IAAI;YAC3B,OAAO,OAAO,CAAC,OAAO,EAAkB,CAAA;QAE5C,KAAK,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,IAAI,SAAS,EAAE,CAAC;YAC5C,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;gBACzB,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,UAAU,CAAC,CAAA;YACvC,CAAC;iBAAM,CAAC;gBACJ,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,UAAU,CAAC,CAAA;YAC3C,CAAC;QACL,CAAC;IACL,CAAC;IAES,UAAU,CAAC,KAAa;QAC9B,OAAO,CACH,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC;YAC1B,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC;YAC1B,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC,CAC7B,CAAA;IACL,CAAC;IAED;;OAEG;IACH,kBAAkB,CACd,WAA2B,EAC3B,OAAgB;QAEhB,MAAM,IAAI,YAAY,CAClB,uDAAuD,CAC1D,CAAA;IACL,CAAC;CACJ", "file": "SpannerQueryRunner.js", "sourcesContent": ["import { ObjectLiteral } from \"../../common/ObjectLiteral\"\nimport { TypeORMError } from \"../../error\"\nimport { QueryFailedError } from \"../../error/QueryFailedError\"\nimport { QueryRunnerAlreadyReleasedError } from \"../../error/QueryRunnerAlreadyReleasedError\"\nimport { TransactionNotStartedError } from \"../../error/TransactionNotStartedError\"\nimport { ReadStream } from \"../../platform/PlatformTools\"\nimport { BaseQueryRunner } from \"../../query-runner/BaseQueryRunner\"\nimport { QueryResult } from \"../../query-runner/QueryResult\"\nimport { QueryRunner } from \"../../query-runner/QueryRunner\"\nimport { TableIndexOptions } from \"../../schema-builder/options/TableIndexOptions\"\nimport { Table } from \"../../schema-builder/table/Table\"\nimport { TableCheck } from \"../../schema-builder/table/TableCheck\"\nimport { TableColumn } from \"../../schema-builder/table/TableColumn\"\nimport { TableExclusion } from \"../../schema-builder/table/TableExclusion\"\nimport { TableForeignKey } from \"../../schema-builder/table/TableForeignKey\"\nimport { TableIndex } from \"../../schema-builder/table/TableIndex\"\nimport { TableUnique } from \"../../schema-builder/table/TableUnique\"\nimport { View } from \"../../schema-builder/view/View\"\nimport { Broadcaster } from \"../../subscriber/Broadcaster\"\nimport { BroadcasterResult } from \"../../subscriber/BroadcasterResult\"\nimport { OrmUtils } from \"../../util/OrmUtils\"\nimport { Query } from \"../Query\"\nimport { ColumnType } from \"../types/ColumnTypes\"\nimport { IsolationLevel } from \"../types/IsolationLevel\"\nimport { MetadataTableType } from \"../types/MetadataTableType\"\nimport { ReplicationMode } from \"../types/ReplicationMode\"\nimport { SpannerDriver } from \"./SpannerDriver\"\n\n/**\n * Runs queries on a single postgres database connection.\n */\nexport class SpannerQueryRunner extends BaseQueryRunner implements QueryRunner {\n    // -------------------------------------------------------------------------\n    // Public Implemented Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Database driver used by connection.\n     */\n    driver: SpannerDriver\n\n    /**\n     * Real database connection from a connection pool used to perform queries.\n     */\n    protected session?: any\n\n    /**\n     * Transaction currently executed by this session.\n     */\n    protected sessionTransaction?: any\n\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(driver: SpannerDriver, mode: ReplicationMode) {\n        super()\n        this.driver = driver\n        this.connection = driver.connection\n        this.mode = mode\n        this.broadcaster = new Broadcaster(this)\n    }\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Creates/uses database connection from the connection pool to perform further operations.\n     * Returns obtained database connection.\n     */\n    async connect(): Promise<any> {\n        if (this.session) {\n            return Promise.resolve(this.session)\n        }\n\n        const [session] = await this.driver.instanceDatabase.createSession({})\n        this.session = session\n        this.sessionTransaction = await session.transaction()\n        return this.session\n    }\n\n    /**\n     * Releases used database connection.\n     * You cannot use query runner methods once its released.\n     */\n    async release(): Promise<void> {\n        this.isReleased = true\n        if (this.session) {\n            await this.session.delete()\n        }\n        this.session = undefined\n        return Promise.resolve()\n    }\n\n    /**\n     * Starts transaction.\n     */\n    async startTransaction(isolationLevel?: IsolationLevel): Promise<void> {\n        this.isTransactionActive = true\n        try {\n            await this.broadcaster.broadcast(\"BeforeTransactionStart\")\n        } catch (err) {\n            this.isTransactionActive = false\n            throw err\n        }\n\n        await this.connect()\n        await this.sessionTransaction.begin()\n        this.connection.logger.logQuery(\"START TRANSACTION\")\n\n        await this.broadcaster.broadcast(\"AfterTransactionStart\")\n    }\n\n    /**\n     * Commits transaction.\n     * Error will be thrown if transaction was not started.\n     */\n    async commitTransaction(): Promise<void> {\n        if (!this.isTransactionActive || !this.sessionTransaction)\n            throw new TransactionNotStartedError()\n\n        await this.broadcaster.broadcast(\"BeforeTransactionCommit\")\n\n        await this.sessionTransaction.commit()\n        this.connection.logger.logQuery(\"COMMIT\")\n        this.isTransactionActive = false\n\n        await this.broadcaster.broadcast(\"AfterTransactionCommit\")\n    }\n\n    /**\n     * Rollbacks transaction.\n     * Error will be thrown if transaction was not started.\n     */\n    async rollbackTransaction(): Promise<void> {\n        if (!this.isTransactionActive || !this.sessionTransaction)\n            throw new TransactionNotStartedError()\n\n        await this.broadcaster.broadcast(\"BeforeTransactionRollback\")\n\n        await this.sessionTransaction.rollback()\n        this.connection.logger.logQuery(\"ROLLBACK\")\n        this.isTransactionActive = false\n\n        await this.broadcaster.broadcast(\"AfterTransactionRollback\")\n    }\n\n    /**\n     * Executes a given SQL query.\n     */\n    async query(\n        query: string,\n        parameters?: any[],\n        useStructuredResult: boolean = false,\n    ): Promise<any> {\n        if (this.isReleased) throw new QueryRunnerAlreadyReleasedError()\n\n        await this.connect()\n\n        this.driver.connection.logger.logQuery(query, parameters, this)\n        await this.broadcaster.broadcast(\"BeforeQuery\", query, parameters)\n\n        const broadcasterResult = new BroadcasterResult()\n\n        try {\n            const queryStartTime = Date.now()\n            let rawResult:\n                | [\n                      any[],\n                      {\n                          queryPlan: null\n                          queryStats: null\n                          rowCountExact: string\n                          rowCount: string\n                      },\n                      { rowType: { fields: [] }; transaction: null },\n                  ]\n                | undefined = undefined\n            const isSelect = query.startsWith(\"SELECT\")\n            const executor =\n                isSelect && !this.isTransactionActive\n                    ? this.driver.instanceDatabase\n                    : this.sessionTransaction\n\n            if (!this.isTransactionActive && !isSelect) {\n                await this.sessionTransaction.begin()\n            }\n\n            try {\n                rawResult = await executor.run({\n                    sql: query,\n                    params: parameters\n                        ? parameters.reduce((params, value, index) => {\n                              params[\"param\" + index] = value\n                              return params\n                          }, {} as ObjectLiteral)\n                        : undefined,\n                    json: true,\n                })\n                if (!this.isTransactionActive && !isSelect) {\n                    await this.sessionTransaction.commit()\n                }\n            } catch (error) {\n                try {\n                    // we throw original error even if rollback thrown an error\n                    if (!this.isTransactionActive && !isSelect)\n                        await this.sessionTransaction.rollback()\n                } catch (rollbackError) {}\n                throw error\n            }\n\n            // log slow queries if maxQueryExecution time is set\n            const maxQueryExecutionTime =\n                this.driver.options.maxQueryExecutionTime\n            const queryEndTime = Date.now()\n            const queryExecutionTime = queryEndTime - queryStartTime\n\n            this.broadcaster.broadcastAfterQueryEvent(\n                broadcasterResult,\n                query,\n                parameters,\n                true,\n                queryExecutionTime,\n                rawResult,\n                undefined,\n            )\n\n            if (\n                maxQueryExecutionTime &&\n                queryExecutionTime > maxQueryExecutionTime\n            )\n                this.driver.connection.logger.logQuerySlow(\n                    queryExecutionTime,\n                    query,\n                    parameters,\n                    this,\n                )\n\n            const result = new QueryResult()\n\n            result.raw = rawResult\n            result.records = rawResult ? rawResult[0] : []\n            if (rawResult && rawResult[1] && rawResult[1].rowCountExact) {\n                result.affected = parseInt(rawResult[1].rowCountExact)\n            }\n\n            if (!useStructuredResult) {\n                return result.records\n            }\n\n            return result\n        } catch (err) {\n            this.driver.connection.logger.logQueryError(\n                err,\n                query,\n                parameters,\n                this,\n            )\n            this.broadcaster.broadcastAfterQueryEvent(\n                broadcasterResult,\n                query,\n                parameters,\n                false,\n                undefined,\n                undefined,\n                err,\n            )\n            throw new QueryFailedError(query, parameters, err)\n        } finally {\n            await broadcasterResult.wait()\n        }\n    }\n\n    /**\n     * Update database schema.\n     * Used for creating/altering/dropping tables, columns, indexes, etc.\n     *\n     * DDL changing queries should be executed by `updateSchema()` method.\n     */\n    async updateDDL(query: string, parameters?: any[]): Promise<void> {\n        if (this.isReleased) throw new QueryRunnerAlreadyReleasedError()\n\n        this.driver.connection.logger.logQuery(query, parameters, this)\n        try {\n            const queryStartTime = Date.now()\n            const [operation] = await this.driver.instanceDatabase.updateSchema(\n                query,\n            )\n            await operation.promise()\n            // log slow queries if maxQueryExecution time is set\n            const maxQueryExecutionTime =\n                this.driver.options.maxQueryExecutionTime\n            const queryEndTime = Date.now()\n            const queryExecutionTime = queryEndTime - queryStartTime\n            if (\n                maxQueryExecutionTime &&\n                queryExecutionTime > maxQueryExecutionTime\n            )\n                this.driver.connection.logger.logQuerySlow(\n                    queryExecutionTime,\n                    query,\n                    parameters,\n                    this,\n                )\n        } catch (err) {\n            this.driver.connection.logger.logQueryError(\n                err,\n                query,\n                parameters,\n                this,\n            )\n            throw new QueryFailedError(query, parameters, err)\n        }\n    }\n\n    /**\n     * Returns raw data stream.\n     */\n    async stream(\n        query: string,\n        parameters?: any[],\n        onEnd?: Function,\n        onError?: Function,\n    ): Promise<ReadStream> {\n        if (this.isReleased) throw new QueryRunnerAlreadyReleasedError()\n\n        try {\n            this.driver.connection.logger.logQuery(query, parameters, this)\n            const request = {\n                sql: query,\n                params: parameters\n                    ? parameters.reduce((params, value, index) => {\n                          params[\"param\" + index] = value\n                          return params\n                      }, {} as ObjectLiteral)\n                    : undefined,\n                json: true,\n            }\n            const stream = this.driver.instanceDatabase.runStream(request)\n\n            if (onEnd) {\n                stream.on(\"end\", onEnd)\n            }\n\n            if (onError) {\n                stream.on(\"error\", onError)\n            }\n\n            return stream\n        } catch (err) {\n            this.driver.connection.logger.logQueryError(\n                err,\n                query,\n                parameters,\n                this,\n            )\n            throw new QueryFailedError(query, parameters, err)\n        }\n    }\n\n    /**\n     * Returns all available database names including system databases.\n     */\n    async getDatabases(): Promise<string[]> {\n        return Promise.resolve([])\n    }\n\n    /**\n     * Returns all available schema names including system schemas.\n     * If database parameter specified, returns schemas of that database.\n     */\n    async getSchemas(database?: string): Promise<string[]> {\n        return Promise.resolve([])\n    }\n\n    /**\n     * Checks if database with the given name exist.\n     */\n    async hasDatabase(database: string): Promise<boolean> {\n        throw new TypeORMError(\n            `Check database queries are not supported by Spanner driver.`,\n        )\n    }\n\n    /**\n     * Loads currently using database\n     */\n    async getCurrentDatabase(): Promise<string> {\n        throw new TypeORMError(\n            `Check database queries are not supported by Spanner driver.`,\n        )\n    }\n\n    /**\n     * Checks if schema with the given name exist.\n     */\n    async hasSchema(schema: string): Promise<boolean> {\n        const result = await this.query(\n            `SELECT * FROM \"information_schema\".\"schemata\" WHERE \"schema_name\" = '${schema}'`,\n        )\n        return result.length ? true : false\n    }\n\n    /**\n     * Loads currently using database schema\n     */\n    async getCurrentSchema(): Promise<string> {\n        throw new TypeORMError(\n            `Check schema queries are not supported by Spanner driver.`,\n        )\n    }\n\n    /**\n     * Checks if table with the given name exist in the database.\n     */\n    async hasTable(tableOrName: Table | string): Promise<boolean> {\n        const tableName =\n            tableOrName instanceof Table ? tableOrName.name : tableOrName\n        const sql =\n            `SELECT * FROM \\`INFORMATION_SCHEMA\\`.\\`TABLES\\` ` +\n            `WHERE \\`TABLE_CATALOG\\` = '' AND \\`TABLE_SCHEMA\\` = '' AND \\`TABLE_TYPE\\` = 'BASE TABLE' ` +\n            `AND \\`TABLE_NAME\\` = '${tableName}'`\n        const result = await this.query(sql)\n        return result.length ? true : false\n    }\n\n    /**\n     * Checks if column with the given name exist in the given table.\n     */\n    async hasColumn(\n        tableOrName: Table | string,\n        columnName: string,\n    ): Promise<boolean> {\n        const tableName =\n            tableOrName instanceof Table ? tableOrName.name : tableOrName\n        const sql =\n            `SELECT * FROM \\`INFORMATION_SCHEMA\\`.\\`COLUMNS\\` ` +\n            `WHERE \\`TABLE_CATALOG\\` = '' AND \\`TABLE_SCHEMA\\` = '' ` +\n            `AND \\`TABLE_NAME\\` = '${tableName}' AND \\`COLUMN_NAME\\` = '${columnName}'`\n        const result = await this.query(sql)\n        return result.length ? true : false\n    }\n\n    /**\n     * Creates a new database.\n     * Note: Spanner does not support database creation inside a transaction block.\n     */\n    async createDatabase(\n        database: string,\n        ifNotExist?: boolean,\n    ): Promise<void> {\n        if (ifNotExist) {\n            const databaseAlreadyExists = await this.hasDatabase(database)\n\n            if (databaseAlreadyExists) return Promise.resolve()\n        }\n\n        const up = `CREATE DATABASE \"${database}\"`\n        const down = `DROP DATABASE \"${database}\"`\n        await this.executeQueries(new Query(up), new Query(down))\n    }\n\n    /**\n     * Drops database.\n     * Note: Spanner does not support database dropping inside a transaction block.\n     */\n    async dropDatabase(database: string, ifExist?: boolean): Promise<void> {\n        const up = ifExist\n            ? `DROP DATABASE IF EXISTS \"${database}\"`\n            : `DROP DATABASE \"${database}\"`\n        const down = `CREATE DATABASE \"${database}\"`\n        await this.executeQueries(new Query(up), new Query(down))\n    }\n\n    /**\n     * Creates a new table schema.\n     */\n    async createSchema(\n        schemaPath: string,\n        ifNotExist?: boolean,\n    ): Promise<void> {\n        return Promise.resolve()\n    }\n\n    /**\n     * Drops table schema.\n     */\n    async dropSchema(\n        schemaPath: string,\n        ifExist?: boolean,\n        isCascade?: boolean,\n    ): Promise<void> {\n        return Promise.resolve()\n    }\n\n    /**\n     * Creates a new table.\n     */\n    async createTable(\n        table: Table,\n        ifNotExist: boolean = false,\n        createForeignKeys: boolean = true,\n        createIndices: boolean = true,\n    ): Promise<void> {\n        if (ifNotExist) {\n            const isTableExist = await this.hasTable(table)\n            if (isTableExist) return Promise.resolve()\n        }\n        const upQueries: Query[] = []\n        const downQueries: Query[] = []\n\n        upQueries.push(this.createTableSql(table, createForeignKeys))\n        downQueries.push(this.dropTableSql(table))\n\n        // if createForeignKeys is true, we must drop created foreign keys in down query.\n        // createTable does not need separate method to create foreign keys, because it create fk's in the same query with table creation.\n        if (createForeignKeys)\n            table.foreignKeys.forEach((foreignKey) =>\n                downQueries.push(this.dropForeignKeySql(table, foreignKey)),\n            )\n\n        if (createIndices) {\n            table.indices.forEach((index) => {\n                // new index may be passed without name. In this case we generate index name manually.\n                if (!index.name)\n                    index.name = this.connection.namingStrategy.indexName(\n                        table,\n                        index.columnNames,\n                        index.where,\n                    )\n                upQueries.push(this.createIndexSql(table, index))\n                downQueries.push(this.dropIndexSql(table, index))\n            })\n        }\n\n        // if table has column with generated type, we must add the expression to the metadata table\n        const generatedColumns = table.columns.filter(\n            (column) => column.generatedType && column.asExpression,\n        )\n\n        for (const column of generatedColumns) {\n            const insertQuery = this.insertTypeormMetadataSql({\n                table: table.name,\n                type: MetadataTableType.GENERATED_COLUMN,\n                name: column.name,\n                value: column.asExpression,\n            })\n\n            const deleteQuery = this.deleteTypeormMetadataSql({\n                table: table.name,\n                type: MetadataTableType.GENERATED_COLUMN,\n                name: column.name,\n            })\n\n            upQueries.push(insertQuery)\n            downQueries.push(deleteQuery)\n        }\n\n        await this.executeQueries(upQueries, downQueries)\n    }\n\n    /**\n     * Drops the table.\n     */\n    async dropTable(\n        target: Table | string,\n        ifExist?: boolean,\n        dropForeignKeys: boolean = true,\n        dropIndices: boolean = true,\n    ): Promise<void> {\n        // It needs because if table does not exist and dropForeignKeys or dropIndices is true, we don't need\n        // to perform drop queries for foreign keys and indices.\n        if (ifExist) {\n            const isTableExist = await this.hasTable(target)\n            if (!isTableExist) return Promise.resolve()\n        }\n\n        // if dropTable called with dropForeignKeys = true, we must create foreign keys in down query.\n        const createForeignKeys: boolean = dropForeignKeys\n        const tablePath = this.getTablePath(target)\n        const table = await this.getCachedTable(tablePath)\n        const upQueries: Query[] = []\n        const downQueries: Query[] = []\n\n        if (dropIndices) {\n            table.indices.forEach((index) => {\n                upQueries.push(this.dropIndexSql(table, index))\n                downQueries.push(this.createIndexSql(table, index))\n            })\n        }\n\n        if (dropForeignKeys)\n            table.foreignKeys.forEach((foreignKey) =>\n                upQueries.push(this.dropForeignKeySql(table, foreignKey)),\n            )\n\n        upQueries.push(this.dropTableSql(table))\n        downQueries.push(this.createTableSql(table, createForeignKeys))\n\n        // if table had columns with generated type, we must remove the expression from the metadata table\n        const generatedColumns = table.columns.filter(\n            (column) => column.generatedType && column.asExpression,\n        )\n\n        for (const column of generatedColumns) {\n            const deleteQuery = this.deleteTypeormMetadataSql({\n                table: table.name,\n                type: MetadataTableType.GENERATED_COLUMN,\n                name: column.name,\n            })\n\n            const insertQuery = this.insertTypeormMetadataSql({\n                table: table.name,\n                type: MetadataTableType.GENERATED_COLUMN,\n                name: column.name,\n                value: column.asExpression,\n            })\n\n            upQueries.push(deleteQuery)\n            downQueries.push(insertQuery)\n        }\n\n        await this.executeQueries(upQueries, downQueries)\n    }\n\n    /**\n     * Creates a new view.\n     */\n    async createView(view: View): Promise<void> {\n        const upQueries: Query[] = []\n        const downQueries: Query[] = []\n        upQueries.push(this.createViewSql(view))\n        upQueries.push(await this.insertViewDefinitionSql(view))\n        downQueries.push(this.dropViewSql(view))\n        downQueries.push(await this.deleteViewDefinitionSql(view))\n        await this.executeQueries(upQueries, downQueries)\n    }\n\n    /**\n     * Drops the view.\n     */\n    async dropView(target: View | string): Promise<void> {\n        const viewName = target instanceof View ? target.name : target\n        const view = await this.getCachedView(viewName)\n\n        const upQueries: Query[] = []\n        const downQueries: Query[] = []\n        upQueries.push(await this.deleteViewDefinitionSql(view))\n        upQueries.push(this.dropViewSql(view))\n        downQueries.push(await this.insertViewDefinitionSql(view))\n        downQueries.push(this.createViewSql(view))\n        await this.executeQueries(upQueries, downQueries)\n    }\n\n    /**\n     * Renames the given table.\n     */\n    async renameTable(\n        oldTableOrName: Table | string,\n        newTableName: string,\n    ): Promise<void> {\n        throw new TypeORMError(\n            `Rename table queries are not supported by Spanner driver.`,\n        )\n    }\n\n    /**\n     * Creates a new column from the column in the table.\n     */\n    async addColumn(\n        tableOrName: Table | string,\n        column: TableColumn,\n    ): Promise<void> {\n        const table =\n            tableOrName instanceof Table\n                ? tableOrName\n                : await this.getCachedTable(tableOrName)\n        const clonedTable = table.clone()\n        const upQueries: Query[] = []\n        const downQueries: Query[] = []\n\n        upQueries.push(\n            new Query(\n                `ALTER TABLE ${this.escapePath(\n                    table,\n                )} ADD ${this.buildCreateColumnSql(column)}`,\n            ),\n        )\n        downQueries.push(\n            new Query(\n                `ALTER TABLE ${this.escapePath(\n                    table,\n                )} DROP COLUMN ${this.driver.escape(column.name)}`,\n            ),\n        )\n\n        // create column index\n        const columnIndex = clonedTable.indices.find(\n            (index) =>\n                index.columnNames.length === 1 &&\n                index.columnNames[0] === column.name,\n        )\n        if (columnIndex) {\n            upQueries.push(this.createIndexSql(table, columnIndex))\n            downQueries.push(this.dropIndexSql(table, columnIndex))\n        } else if (column.isUnique) {\n            const uniqueIndex = new TableIndex({\n                name: this.connection.namingStrategy.indexName(table, [\n                    column.name,\n                ]),\n                columnNames: [column.name],\n                isUnique: true,\n            })\n            clonedTable.indices.push(uniqueIndex)\n            clonedTable.uniques.push(\n                new TableUnique({\n                    name: uniqueIndex.name,\n                    columnNames: uniqueIndex.columnNames,\n                }),\n            )\n\n            upQueries.push(this.createIndexSql(table, uniqueIndex))\n            downQueries.push(this.dropIndexSql(table, uniqueIndex))\n        }\n\n        if (column.generatedType && column.asExpression) {\n            const insertQuery = this.insertTypeormMetadataSql({\n                table: table.name,\n                type: MetadataTableType.GENERATED_COLUMN,\n                name: column.name,\n                value: column.asExpression,\n            })\n\n            const deleteQuery = this.deleteTypeormMetadataSql({\n                table: table.name,\n                type: MetadataTableType.GENERATED_COLUMN,\n                name: column.name,\n            })\n\n            upQueries.push(insertQuery)\n            downQueries.push(deleteQuery)\n        }\n\n        await this.executeQueries(upQueries, downQueries)\n\n        clonedTable.addColumn(column)\n        this.replaceCachedTable(table, clonedTable)\n    }\n\n    /**\n     * Creates a new columns from the column in the table.\n     */\n    async addColumns(\n        tableOrName: Table | string,\n        columns: TableColumn[],\n    ): Promise<void> {\n        for (const column of columns) {\n            await this.addColumn(tableOrName, column)\n        }\n    }\n\n    /**\n     * Renames column in the given table.\n     */\n    async renameColumn(\n        tableOrName: Table | string,\n        oldTableColumnOrName: TableColumn | string,\n        newTableColumnOrName: TableColumn | string,\n    ): Promise<void> {\n        const table =\n            tableOrName instanceof Table\n                ? tableOrName\n                : await this.getCachedTable(tableOrName)\n        const oldColumn =\n            oldTableColumnOrName instanceof TableColumn\n                ? oldTableColumnOrName\n                : table.columns.find((c) => c.name === oldTableColumnOrName)\n        if (!oldColumn)\n            throw new TypeORMError(\n                `Column \"${oldTableColumnOrName}\" was not found in the \"${table.name}\" table.`,\n            )\n\n        let newColumn\n        if (newTableColumnOrName instanceof TableColumn) {\n            newColumn = newTableColumnOrName\n        } else {\n            newColumn = oldColumn.clone()\n            newColumn.name = newTableColumnOrName\n        }\n\n        return this.changeColumn(table, oldColumn, newColumn)\n    }\n\n    /**\n     * Changes a column in the table.\n     */\n    async changeColumn(\n        tableOrName: Table | string,\n        oldTableColumnOrName: TableColumn | string,\n        newColumn: TableColumn,\n    ): Promise<void> {\n        const table =\n            tableOrName instanceof Table\n                ? tableOrName\n                : await this.getCachedTable(tableOrName)\n        let clonedTable = table.clone()\n        const upQueries: Query[] = []\n        const downQueries: Query[] = []\n\n        const oldColumn =\n            oldTableColumnOrName instanceof TableColumn\n                ? oldTableColumnOrName\n                : table.columns.find(\n                      (column) => column.name === oldTableColumnOrName,\n                  )\n        if (!oldColumn)\n            throw new TypeORMError(\n                `Column \"${oldTableColumnOrName}\" was not found in the \"${table.name}\" table.`,\n            )\n\n        if (\n            oldColumn.name !== newColumn.name ||\n            oldColumn.type !== newColumn.type ||\n            oldColumn.length !== newColumn.length ||\n            oldColumn.isArray !== newColumn.isArray ||\n            oldColumn.generatedType !== newColumn.generatedType ||\n            oldColumn.asExpression !== newColumn.asExpression\n        ) {\n            // To avoid data conversion, we just recreate column\n            await this.dropColumn(table, oldColumn)\n            await this.addColumn(table, newColumn)\n\n            // update cloned table\n            clonedTable = table.clone()\n        } else {\n            if (\n                newColumn.precision !== oldColumn.precision ||\n                newColumn.scale !== oldColumn.scale\n            ) {\n                upQueries.push(\n                    new Query(\n                        `ALTER TABLE ${this.escapePath(table)} ALTER COLUMN \"${\n                            newColumn.name\n                        }\" TYPE ${this.driver.createFullType(newColumn)}`,\n                    ),\n                )\n                downQueries.push(\n                    new Query(\n                        `ALTER TABLE ${this.escapePath(table)} ALTER COLUMN \"${\n                            newColumn.name\n                        }\" TYPE ${this.driver.createFullType(oldColumn)}`,\n                    ),\n                )\n            }\n\n            if (oldColumn.isNullable !== newColumn.isNullable) {\n                if (newColumn.isNullable) {\n                    upQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} ALTER COLUMN \"${oldColumn.name}\" DROP NOT NULL`,\n                        ),\n                    )\n                    downQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} ALTER COLUMN \"${oldColumn.name}\" SET NOT NULL`,\n                        ),\n                    )\n                } else {\n                    upQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} ALTER COLUMN \"${oldColumn.name}\" SET NOT NULL`,\n                        ),\n                    )\n                    downQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} ALTER COLUMN \"${oldColumn.name}\" DROP NOT NULL`,\n                        ),\n                    )\n                }\n            }\n\n            if (newColumn.isUnique !== oldColumn.isUnique) {\n                if (newColumn.isUnique === true) {\n                    const uniqueIndex = new TableIndex({\n                        name: this.connection.namingStrategy.indexName(table, [\n                            newColumn.name,\n                        ]),\n                        columnNames: [newColumn.name],\n                        isUnique: true,\n                    })\n                    clonedTable.indices.push(uniqueIndex)\n                    clonedTable.uniques.push(\n                        new TableUnique({\n                            name: uniqueIndex.name,\n                            columnNames: uniqueIndex.columnNames,\n                        }),\n                    )\n\n                    upQueries.push(this.createIndexSql(table, uniqueIndex))\n                    downQueries.push(this.dropIndexSql(table, uniqueIndex))\n                } else {\n                    const uniqueIndex = clonedTable.indices.find((index) => {\n                        return (\n                            index.columnNames.length === 1 &&\n                            index.isUnique === true &&\n                            !!index.columnNames.find(\n                                (columnName) => columnName === newColumn.name,\n                            )\n                        )\n                    })\n                    clonedTable.indices.splice(\n                        clonedTable.indices.indexOf(uniqueIndex!),\n                        1,\n                    )\n\n                    const tableUnique = clonedTable.uniques.find(\n                        (unique) => unique.name === uniqueIndex!.name,\n                    )\n                    clonedTable.uniques.splice(\n                        clonedTable.uniques.indexOf(tableUnique!),\n                        1,\n                    )\n\n                    upQueries.push(this.dropIndexSql(table, uniqueIndex!))\n                    downQueries.push(this.createIndexSql(table, uniqueIndex!))\n                }\n            }\n        }\n\n        await this.executeQueries(upQueries, downQueries)\n        this.replaceCachedTable(table, clonedTable)\n    }\n\n    /**\n     * Changes a column in the table.\n     */\n    async changeColumns(\n        tableOrName: Table | string,\n        changedColumns: { newColumn: TableColumn; oldColumn: TableColumn }[],\n    ): Promise<void> {\n        for (const { oldColumn, newColumn } of changedColumns) {\n            await this.changeColumn(tableOrName, oldColumn, newColumn)\n        }\n    }\n\n    /**\n     * Drops column in the table.\n     */\n    async dropColumn(\n        tableOrName: Table | string,\n        columnOrName: TableColumn | string,\n    ): Promise<void> {\n        const table =\n            tableOrName instanceof Table\n                ? tableOrName\n                : await this.getCachedTable(tableOrName)\n        const column =\n            columnOrName instanceof TableColumn\n                ? columnOrName\n                : table.findColumnByName(columnOrName)\n        if (!column)\n            throw new TypeORMError(\n                `Column \"${columnOrName}\" was not found in table \"${table.name}\"`,\n            )\n\n        const clonedTable = table.clone()\n        const upQueries: Query[] = []\n        const downQueries: Query[] = []\n\n        // drop column index\n        const columnIndex = clonedTable.indices.find(\n            (index) =>\n                index.columnNames.length === 1 &&\n                index.columnNames[0] === column.name,\n        )\n        if (columnIndex) {\n            clonedTable.indices.splice(\n                clonedTable.indices.indexOf(columnIndex),\n                1,\n            )\n            upQueries.push(this.dropIndexSql(table, columnIndex))\n            downQueries.push(this.createIndexSql(table, columnIndex))\n        }\n\n        // drop column check\n        const columnCheck = clonedTable.checks.find(\n            (check) =>\n                !!check.columnNames &&\n                check.columnNames.length === 1 &&\n                check.columnNames[0] === column.name,\n        )\n        if (columnCheck) {\n            clonedTable.checks.splice(\n                clonedTable.checks.indexOf(columnCheck),\n                1,\n            )\n            upQueries.push(this.dropCheckConstraintSql(table, columnCheck))\n            downQueries.push(this.createCheckConstraintSql(table, columnCheck))\n        }\n\n        upQueries.push(\n            new Query(\n                `ALTER TABLE ${this.escapePath(\n                    table,\n                )} DROP COLUMN ${this.driver.escape(column.name)}`,\n            ),\n        )\n        downQueries.push(\n            new Query(\n                `ALTER TABLE ${this.escapePath(\n                    table,\n                )} ADD ${this.buildCreateColumnSql(column)}`,\n            ),\n        )\n\n        if (column.generatedType && column.asExpression) {\n            const deleteQuery = this.deleteTypeormMetadataSql({\n                table: table.name,\n                type: MetadataTableType.GENERATED_COLUMN,\n                name: column.name,\n            })\n            const insertQuery = this.insertTypeormMetadataSql({\n                table: table.name,\n                type: MetadataTableType.GENERATED_COLUMN,\n                name: column.name,\n                value: column.asExpression,\n            })\n\n            upQueries.push(deleteQuery)\n            downQueries.push(insertQuery)\n        }\n\n        await this.executeQueries(upQueries, downQueries)\n\n        clonedTable.removeColumn(column)\n        this.replaceCachedTable(table, clonedTable)\n    }\n\n    /**\n     * Drops the columns in the table.\n     */\n    async dropColumns(\n        tableOrName: Table | string,\n        columns: TableColumn[] | string[],\n    ): Promise<void> {\n        for (const column of columns) {\n            await this.dropColumn(tableOrName, column)\n        }\n    }\n\n    /**\n     * Creates a new primary key.\n     *\n     * Not supported in Spanner.\n     * @see https://cloud.google.com/spanner/docs/schema-and-data-model#notes_about_key_columns\n     */\n    async createPrimaryKey(\n        tableOrName: Table | string,\n        columnNames: string[],\n    ): Promise<void> {\n        throw new Error(\n            \"The keys of a table can't change; you can't add a key column to an existing table or remove a key column from an existing table.\",\n        )\n    }\n\n    /**\n     * Updates composite primary keys.\n     */\n    async updatePrimaryKeys(\n        tableOrName: Table | string,\n        columns: TableColumn[],\n    ): Promise<void> {\n        throw new Error(\n            \"The keys of a table can't change; you can't add a key column to an existing table or remove a key column from an existing table.\",\n        )\n    }\n\n    /**\n     * Creates a new primary key.\n     *\n     * Not supported in Spanner.\n     * @see https://cloud.google.com/spanner/docs/schema-and-data-model#notes_about_key_columns\n     */\n    async dropPrimaryKey(tableOrName: Table | string): Promise<void> {\n        throw new Error(\n            \"The keys of a table can't change; you can't add a key column to an existing table or remove a key column from an existing table.\",\n        )\n    }\n\n    /**\n     * Creates new unique constraint.\n     */\n    async createUniqueConstraint(\n        tableOrName: Table | string,\n        uniqueConstraint: TableUnique,\n    ): Promise<void> {\n        throw new TypeORMError(\n            `Spanner does not support unique constraints. Use unique index instead.`,\n        )\n    }\n\n    /**\n     * Creates new unique constraints.\n     */\n    async createUniqueConstraints(\n        tableOrName: Table | string,\n        uniqueConstraints: TableUnique[],\n    ): Promise<void> {\n        throw new TypeORMError(\n            `Spanner does not support unique constraints. Use unique index instead.`,\n        )\n    }\n\n    /**\n     * Drops unique constraint.\n     */\n    async dropUniqueConstraint(\n        tableOrName: Table | string,\n        uniqueOrName: TableUnique | string,\n    ): Promise<void> {\n        throw new TypeORMError(\n            `Spanner does not support unique constraints. Use unique index instead.`,\n        )\n    }\n\n    /**\n     * Drops unique constraints.\n     */\n    async dropUniqueConstraints(\n        tableOrName: Table | string,\n        uniqueConstraints: TableUnique[],\n    ): Promise<void> {\n        throw new TypeORMError(\n            `Spanner does not support unique constraints. Use unique index instead.`,\n        )\n    }\n\n    /**\n     * Creates new check constraint.\n     */\n    async createCheckConstraint(\n        tableOrName: Table | string,\n        checkConstraint: TableCheck,\n    ): Promise<void> {\n        const table =\n            tableOrName instanceof Table\n                ? tableOrName\n                : await this.getCachedTable(tableOrName)\n\n        // new check constraint may be passed without name. In this case we generate unique name manually.\n        if (!checkConstraint.name)\n            checkConstraint.name =\n                this.connection.namingStrategy.checkConstraintName(\n                    table,\n                    checkConstraint.expression!,\n                )\n\n        const up = this.createCheckConstraintSql(table, checkConstraint)\n        const down = this.dropCheckConstraintSql(table, checkConstraint)\n        await this.executeQueries(up, down)\n        table.addCheckConstraint(checkConstraint)\n    }\n\n    /**\n     * Creates new check constraints.\n     */\n    async createCheckConstraints(\n        tableOrName: Table | string,\n        checkConstraints: TableCheck[],\n    ): Promise<void> {\n        const promises = checkConstraints.map((checkConstraint) =>\n            this.createCheckConstraint(tableOrName, checkConstraint),\n        )\n        await Promise.all(promises)\n    }\n\n    /**\n     * Drops check constraint.\n     */\n    async dropCheckConstraint(\n        tableOrName: Table | string,\n        checkOrName: TableCheck | string,\n    ): Promise<void> {\n        const table =\n            tableOrName instanceof Table\n                ? tableOrName\n                : await this.getCachedTable(tableOrName)\n        const checkConstraint =\n            checkOrName instanceof TableCheck\n                ? checkOrName\n                : table.checks.find((c) => c.name === checkOrName)\n        if (!checkConstraint)\n            throw new TypeORMError(\n                `Supplied check constraint was not found in table ${table.name}`,\n            )\n\n        const up = this.dropCheckConstraintSql(table, checkConstraint)\n        const down = this.createCheckConstraintSql(table, checkConstraint)\n        await this.executeQueries(up, down)\n        table.removeCheckConstraint(checkConstraint)\n    }\n\n    /**\n     * Drops check constraints.\n     */\n    async dropCheckConstraints(\n        tableOrName: Table | string,\n        checkConstraints: TableCheck[],\n    ): Promise<void> {\n        const promises = checkConstraints.map((checkConstraint) =>\n            this.dropCheckConstraint(tableOrName, checkConstraint),\n        )\n        await Promise.all(promises)\n    }\n\n    /**\n     * Creates new exclusion constraint.\n     */\n    async createExclusionConstraint(\n        tableOrName: Table | string,\n        exclusionConstraint: TableExclusion,\n    ): Promise<void> {\n        throw new TypeORMError(\n            `Spanner does not support exclusion constraints.`,\n        )\n    }\n\n    /**\n     * Creates new exclusion constraints.\n     */\n    async createExclusionConstraints(\n        tableOrName: Table | string,\n        exclusionConstraints: TableExclusion[],\n    ): Promise<void> {\n        throw new TypeORMError(\n            `Spanner does not support exclusion constraints.`,\n        )\n    }\n\n    /**\n     * Drops exclusion constraint.\n     */\n    async dropExclusionConstraint(\n        tableOrName: Table | string,\n        exclusionOrName: TableExclusion | string,\n    ): Promise<void> {\n        throw new TypeORMError(\n            `Spanner does not support exclusion constraints.`,\n        )\n    }\n\n    /**\n     * Drops exclusion constraints.\n     */\n    async dropExclusionConstraints(\n        tableOrName: Table | string,\n        exclusionConstraints: TableExclusion[],\n    ): Promise<void> {\n        throw new TypeORMError(\n            `Spanner does not support exclusion constraints.`,\n        )\n    }\n\n    /**\n     * Creates a new foreign key.\n     */\n    async createForeignKey(\n        tableOrName: Table | string,\n        foreignKey: TableForeignKey,\n    ): Promise<void> {\n        const table =\n            tableOrName instanceof Table\n                ? tableOrName\n                : await this.getCachedTable(tableOrName)\n\n        // new FK may be passed without name. In this case we generate FK name manually.\n        if (!foreignKey.name)\n            foreignKey.name = this.connection.namingStrategy.foreignKeyName(\n                table,\n                foreignKey.columnNames,\n                this.getTablePath(foreignKey),\n                foreignKey.referencedColumnNames,\n            )\n\n        const up = this.createForeignKeySql(table, foreignKey)\n        const down = this.dropForeignKeySql(table, foreignKey)\n        await this.executeQueries(up, down)\n        table.addForeignKey(foreignKey)\n    }\n\n    /**\n     * Creates a new foreign keys.\n     */\n    async createForeignKeys(\n        tableOrName: Table | string,\n        foreignKeys: TableForeignKey[],\n    ): Promise<void> {\n        for (const foreignKey of foreignKeys) {\n            await this.createForeignKey(tableOrName, foreignKey)\n        }\n    }\n\n    /**\n     * Drops a foreign key from the table.\n     */\n    async dropForeignKey(\n        tableOrName: Table | string,\n        foreignKeyOrName: TableForeignKey | string,\n    ): Promise<void> {\n        const table =\n            tableOrName instanceof Table\n                ? tableOrName\n                : await this.getCachedTable(tableOrName)\n        const foreignKey =\n            foreignKeyOrName instanceof TableForeignKey\n                ? foreignKeyOrName\n                : table.foreignKeys.find((fk) => fk.name === foreignKeyOrName)\n        if (!foreignKey)\n            throw new TypeORMError(\n                `Supplied foreign key was not found in table ${table.name}`,\n            )\n\n        const up = this.dropForeignKeySql(table, foreignKey)\n        const down = this.createForeignKeySql(table, foreignKey)\n        await this.executeQueries(up, down)\n        table.removeForeignKey(foreignKey)\n    }\n\n    /**\n     * Drops a foreign keys from the table.\n     */\n    async dropForeignKeys(\n        tableOrName: Table | string,\n        foreignKeys: TableForeignKey[],\n    ): Promise<void> {\n        for (const foreignKey of foreignKeys) {\n            await this.dropForeignKey(tableOrName, foreignKey)\n        }\n    }\n\n    /**\n     * Creates a new index.\n     */\n    async createIndex(\n        tableOrName: Table | string,\n        index: TableIndex,\n    ): Promise<void> {\n        const table =\n            tableOrName instanceof Table\n                ? tableOrName\n                : await this.getCachedTable(tableOrName)\n\n        // new index may be passed without name. In this case we generate index name manually.\n        if (!index.name) index.name = this.generateIndexName(table, index)\n\n        const up = this.createIndexSql(table, index)\n        const down = this.dropIndexSql(table, index)\n        await this.executeQueries(up, down)\n        table.addIndex(index)\n    }\n\n    /**\n     * Creates a new indices\n     */\n    async createIndices(\n        tableOrName: Table | string,\n        indices: TableIndex[],\n    ): Promise<void> {\n        for (const index of indices) {\n            await this.createIndex(tableOrName, index)\n        }\n    }\n\n    /**\n     * Drops an index from the table.\n     */\n    async dropIndex(\n        tableOrName: Table | string,\n        indexOrName: TableIndex | string,\n    ): Promise<void> {\n        const table =\n            tableOrName instanceof Table\n                ? tableOrName\n                : await this.getCachedTable(tableOrName)\n        const index =\n            indexOrName instanceof TableIndex\n                ? indexOrName\n                : table.indices.find((i) => i.name === indexOrName)\n        if (!index)\n            throw new TypeORMError(\n                `Supplied index ${indexOrName} was not found in table ${table.name}`,\n            )\n\n        // new index may be passed without name. In this case we generate index name manually.\n        if (!index.name) index.name = this.generateIndexName(table, index)\n\n        const up = this.dropIndexSql(table, index)\n        const down = this.createIndexSql(table, index)\n        await this.executeQueries(up, down)\n        table.removeIndex(index)\n    }\n\n    /**\n     * Drops an indices from the table.\n     */\n    async dropIndices(\n        tableOrName: Table | string,\n        indices: TableIndex[],\n    ): Promise<void> {\n        for (const index of indices) {\n            await this.dropIndex(tableOrName, index)\n        }\n    }\n\n    /**\n     * Clears all table contents.\n     * Spanner does not support TRUNCATE TABLE statement, so we use DELETE FROM.\n     */\n    async clearTable(tableName: string): Promise<void> {\n        await this.query(`DELETE FROM ${this.escapePath(tableName)} WHERE true`)\n    }\n\n    /**\n     * Removes all tables from the currently connected database.\n     */\n    async clearDatabase(): Promise<void> {\n        // drop index queries\n        const selectIndexDropsQuery =\n            `SELECT concat('DROP INDEX \\`', INDEX_NAME, '\\`') AS \\`query\\` ` +\n            `FROM \\`INFORMATION_SCHEMA\\`.\\`INDEXES\\` ` +\n            `WHERE \\`TABLE_CATALOG\\` = '' AND \\`TABLE_SCHEMA\\` = '' AND \\`INDEX_TYPE\\` = 'INDEX' AND \\`SPANNER_IS_MANAGED\\` = false`\n        const dropIndexQueries: ObjectLiteral[] = await this.query(\n            selectIndexDropsQuery,\n        )\n\n        // drop foreign key queries\n        const selectFKDropsQuery =\n            `SELECT concat('ALTER TABLE \\`', TABLE_NAME, '\\`', ' DROP CONSTRAINT \\`', CONSTRAINT_NAME, '\\`') AS \\`query\\` ` +\n            `FROM \\`INFORMATION_SCHEMA\\`.\\`TABLE_CONSTRAINTS\\` ` +\n            `WHERE \\`TABLE_CATALOG\\` = '' AND \\`TABLE_SCHEMA\\` = '' AND \\`CONSTRAINT_TYPE\\` = 'FOREIGN KEY'`\n        const dropFKQueries: ObjectLiteral[] = await this.query(\n            selectFKDropsQuery,\n        )\n\n        // drop view queries\n        // const selectViewDropsQuery = `SELECT concat('DROP VIEW \\`', TABLE_NAME, '\\`') AS \\`query\\` FROM \\`INFORMATION_SCHEMA\\`.\\`VIEWS\\``\n        // const dropViewQueries: ObjectLiteral[] = await this.query(\n        //     selectViewDropsQuery,\n        // )\n\n        // drop table queries\n        const dropTablesQuery =\n            `SELECT concat('DROP TABLE \\`', TABLE_NAME, '\\`') AS \\`query\\` ` +\n            `FROM \\`INFORMATION_SCHEMA\\`.\\`TABLES\\` ` +\n            `WHERE \\`TABLE_CATALOG\\` = '' AND \\`TABLE_SCHEMA\\` = '' AND \\`TABLE_TYPE\\` = 'BASE TABLE'`\n        const dropTableQueries: ObjectLiteral[] = await this.query(\n            dropTablesQuery,\n        )\n\n        if (\n            !dropIndexQueries.length &&\n            !dropFKQueries.length &&\n            // !dropViewQueries.length &&\n            !dropTableQueries.length\n        )\n            return\n\n        const isAnotherTransactionActive = this.isTransactionActive\n        if (!isAnotherTransactionActive) await this.startTransaction()\n        try {\n            for (const query of dropIndexQueries) {\n                await this.updateDDL(query[\"query\"])\n            }\n            for (const query of dropFKQueries) {\n                await this.updateDDL(query[\"query\"])\n            }\n\n            // for (let query of dropViewQueries) {\n            //     await this.updateDDL(query[\"query\"])\n            // }\n\n            for (const query of dropTableQueries) {\n                await this.updateDDL(query[\"query\"])\n            }\n\n            await this.commitTransaction()\n        } catch (error) {\n            try {\n                // we throw original error even if rollback thrown an error\n                if (!isAnotherTransactionActive)\n                    await this.rollbackTransaction()\n            } catch (rollbackError) {}\n            throw error\n        }\n    }\n\n    // -------------------------------------------------------------------------\n    // Override Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Executes up sql queries.\n     */\n    async executeMemoryUpSql(): Promise<void> {\n        for (const { query, parameters } of this.sqlInMemory.upQueries) {\n            if (this.isDMLQuery(query)) {\n                await this.query(query, parameters)\n            } else {\n                await this.updateDDL(query, parameters)\n            }\n        }\n    }\n\n    /**\n     * Executes down sql queries.\n     */\n    async executeMemoryDownSql(): Promise<void> {\n        for (const {\n            query,\n            parameters,\n        } of this.sqlInMemory.downQueries.reverse()) {\n            if (this.isDMLQuery(query)) {\n                await this.query(query, parameters)\n            } else {\n                await this.updateDDL(query, parameters)\n            }\n        }\n    }\n\n    // -------------------------------------------------------------------------\n    // Protected Methods\n    // -------------------------------------------------------------------------\n\n    protected async loadViews(viewNames?: string[]): Promise<View[]> {\n        // const hasTable = await this.hasTable(this.getTypeormMetadataTableName())\n        // if (!hasTable) {\n        //     return []\n        // }\n        //\n        // if (!viewNames) {\n        //     viewNames = []\n        // }\n        //\n        // const escapedViewNames = viewNames\n        //     .map((viewName) => `'${viewName}'`)\n        //     .join(\", \")\n        //\n        // const query =\n        //     `SELECT \\`T\\`.*, \\`V\\`.\\`VIEW_DEFINITION\\` FROM ${this.escapePath(\n        //         this.getTypeormMetadataTableName(),\n        //     )} \\`T\\` ` +\n        //     `INNER JOIN \\`INFORMATION_SCHEMA\\`.\\`VIEWS\\` \\`V\\` ON \\`V\\`.\\`TABLE_NAME\\` = \\`T\\`.\\`NAME\\` ` +\n        //     `WHERE \\`T\\`.\\`TYPE\\` = '${MetadataTableType.VIEW}' ${\n        //         viewNames.length\n        //             ? ` AND \\`T\\`.\\`NAME\\` IN (${escapedViewNames})`\n        //             : \"\"\n        //     }`\n        // const dbViews = await this.query(query)\n        // return dbViews.map((dbView: any) => {\n        //     const view = new View()\n        //     view.database = dbView[\"NAME\"]\n        //     view.name = this.driver.buildTableName(dbView[\"NAME\"])\n        //     view.expression = dbView[\"NAME\"]\n        //     return view\n        // })\n\n        return Promise.resolve([])\n    }\n\n    /**\n     * Loads all tables (with given names) from the database and creates a Table from them.\n     */\n    protected async loadTables(tableNames?: string[]): Promise<Table[]> {\n        if (tableNames && tableNames.length === 0) {\n            return []\n        }\n\n        const dbTables: { TABLE_NAME: string }[] = []\n\n        if (!tableNames || !tableNames.length) {\n            // Since we don't have any of this data we have to do a scan\n            const tablesSql =\n                `SELECT \\`TABLE_NAME\\` ` +\n                `FROM \\`INFORMATION_SCHEMA\\`.\\`TABLES\\` ` +\n                `WHERE \\`TABLE_CATALOG\\` = '' AND \\`TABLE_SCHEMA\\` = '' AND \\`TABLE_TYPE\\` = 'BASE TABLE'`\n            dbTables.push(...(await this.query(tablesSql)))\n        } else {\n            const tablesSql =\n                `SELECT \\`TABLE_NAME\\` ` +\n                `FROM \\`INFORMATION_SCHEMA\\`.\\`TABLES\\` ` +\n                `WHERE \\`TABLE_CATALOG\\` = '' AND \\`TABLE_SCHEMA\\` = '' AND \\`TABLE_TYPE\\` = 'BASE TABLE' ` +\n                `AND \\`TABLE_NAME\\` IN (${tableNames\n                    .map((tableName) => `'${tableName}'`)\n                    .join(\", \")})`\n\n            dbTables.push(...(await this.query(tablesSql)))\n        }\n\n        // if tables were not found in the db, no need to proceed\n        if (!dbTables.length) return []\n\n        const loadedTableNames = dbTables\n            .map((dbTable) => `'${dbTable.TABLE_NAME}'`)\n            .join(\", \")\n\n        const columnsSql = `SELECT * FROM \\`INFORMATION_SCHEMA\\`.\\`COLUMNS\\` WHERE \\`TABLE_CATALOG\\` = '' AND \\`TABLE_SCHEMA\\` = '' AND \\`TABLE_NAME\\` IN (${loadedTableNames})`\n\n        const primaryKeySql =\n            `SELECT \\`KCU\\`.\\`TABLE_NAME\\`, \\`KCU\\`.\\`COLUMN_NAME\\` ` +\n            `FROM \\`INFORMATION_SCHEMA\\`.\\`TABLE_CONSTRAINTS\\` \\`TC\\` ` +\n            `INNER JOIN \\`INFORMATION_SCHEMA\\`.\\`KEY_COLUMN_USAGE\\` \\`KCU\\` ON \\`KCU\\`.\\`CONSTRAINT_NAME\\` = \\`TC\\`.\\`CONSTRAINT_NAME\\` ` +\n            `WHERE \\`TC\\`.\\`TABLE_CATALOG\\` = '' AND \\`TC\\`.\\`TABLE_SCHEMA\\` = '' AND \\`TC\\`.\\`CONSTRAINT_TYPE\\` = 'PRIMARY KEY' ` +\n            `AND \\`TC\\`.\\`TABLE_NAME\\` IN (${loadedTableNames})`\n\n        const indicesSql =\n            `SELECT \\`I\\`.\\`TABLE_NAME\\`, \\`I\\`.\\`INDEX_NAME\\`, \\`I\\`.\\`IS_UNIQUE\\`, \\`I\\`.\\`IS_NULL_FILTERED\\`, \\`IC\\`.\\`COLUMN_NAME\\` ` +\n            `FROM \\`INFORMATION_SCHEMA\\`.\\`INDEXES\\` \\`I\\` ` +\n            `INNER JOIN \\`INFORMATION_SCHEMA\\`.\\`INDEX_COLUMNS\\` \\`IC\\` ON \\`IC\\`.\\`INDEX_NAME\\` = \\`I\\`.\\`INDEX_NAME\\` ` +\n            `AND \\`IC\\`.\\`TABLE_NAME\\` = \\`I\\`.\\`TABLE_NAME\\` ` +\n            `WHERE \\`I\\`.\\`TABLE_CATALOG\\` = '' AND \\`I\\`.\\`TABLE_SCHEMA\\` = '' AND \\`I\\`.\\`TABLE_NAME\\` IN (${loadedTableNames}) ` +\n            `AND \\`I\\`.\\`INDEX_TYPE\\` = 'INDEX' AND \\`I\\`.\\`SPANNER_IS_MANAGED\\` = false`\n\n        const checksSql =\n            `SELECT \\`TC\\`.\\`TABLE_NAME\\`, \\`TC\\`.\\`CONSTRAINT_NAME\\`, \\`CC\\`.\\`CHECK_CLAUSE\\`, \\`CCU\\`.\\`COLUMN_NAME\\`` +\n            `FROM \\`INFORMATION_SCHEMA\\`.\\`TABLE_CONSTRAINTS\\` \\`TC\\` ` +\n            `INNER JOIN \\`INFORMATION_SCHEMA\\`.\\`CONSTRAINT_COLUMN_USAGE\\` \\`CCU\\` ON \\`CCU\\`.\\`CONSTRAINT_NAME\\` = \\`TC\\`.\\`CONSTRAINT_NAME\\` ` +\n            `INNER JOIN \\`INFORMATION_SCHEMA\\`.\\`CHECK_CONSTRAINTS\\` \\`CC\\` ON \\`CC\\`.\\`CONSTRAINT_NAME\\` = \\`TC\\`.\\`CONSTRAINT_NAME\\` ` +\n            `WHERE \\`TC\\`.\\`TABLE_CATALOG\\` = '' AND \\`TC\\`.\\`TABLE_SCHEMA\\` = '' AND \\`TC\\`.\\`CONSTRAINT_TYPE\\` = 'CHECK' ` +\n            `AND \\`TC\\`.\\`TABLE_NAME\\` IN (${loadedTableNames}) AND \\`TC\\`.\\`CONSTRAINT_NAME\\` NOT LIKE 'CK_IS_NOT_NULL%'`\n\n        const foreignKeysSql =\n            `SELECT \\`TC\\`.\\`TABLE_NAME\\`, \\`TC\\`.\\`CONSTRAINT_NAME\\`, \\`KCU\\`.\\`COLUMN_NAME\\`, ` +\n            `\\`CTU\\`.\\`TABLE_NAME\\` AS \\`REFERENCED_TABLE_NAME\\`, \\`CCU\\`.\\`COLUMN_NAME\\` AS \\`REFERENCED_COLUMN_NAME\\`, ` +\n            `\\`RC\\`.\\`UPDATE_RULE\\`, \\`RC\\`.\\`DELETE_RULE\\` ` +\n            `FROM \\`INFORMATION_SCHEMA\\`.\\`TABLE_CONSTRAINTS\\` \\`TC\\` ` +\n            `INNER JOIN \\`INFORMATION_SCHEMA\\`.\\`KEY_COLUMN_USAGE\\` \\`KCU\\` ON \\`KCU\\`.\\`CONSTRAINT_NAME\\` = \\`TC\\`.\\`CONSTRAINT_NAME\\` ` +\n            `INNER JOIN \\`INFORMATION_SCHEMA\\`.\\`CONSTRAINT_TABLE_USAGE\\` \\`CTU\\` ON \\`CTU\\`.\\`CONSTRAINT_NAME\\` = \\`TC\\`.\\`CONSTRAINT_NAME\\` ` +\n            `INNER JOIN \\`INFORMATION_SCHEMA\\`.\\`REFERENTIAL_CONSTRAINTS\\` \\`RC\\` ON \\`RC\\`.\\`CONSTRAINT_NAME\\` = \\`TC\\`.\\`CONSTRAINT_NAME\\` ` +\n            `INNER JOIN \\`INFORMATION_SCHEMA\\`.\\`CONSTRAINT_COLUMN_USAGE\\` \\`CCU\\` ON \\`CCU\\`.\\`CONSTRAINT_NAME\\` = \\`TC\\`.\\`CONSTRAINT_NAME\\` ` +\n            `WHERE \\`TC\\`.\\`TABLE_CATALOG\\` = '' AND \\`TC\\`.\\`TABLE_SCHEMA\\` = '' AND \\`TC\\`.\\`CONSTRAINT_TYPE\\` = 'FOREIGN KEY' ` +\n            `AND \\`TC\\`.\\`TABLE_NAME\\` IN (${loadedTableNames})`\n\n        const [\n            dbColumns,\n            dbPrimaryKeys,\n            dbIndices,\n            dbChecks,\n            dbForeignKeys,\n        ]: ObjectLiteral[][] = await Promise.all([\n            this.query(columnsSql),\n            this.query(primaryKeySql),\n            this.query(indicesSql),\n            this.query(checksSql),\n            this.query(foreignKeysSql),\n        ])\n\n        // create tables for loaded tables\n        return Promise.all(\n            dbTables.map(async (dbTable) => {\n                const table = new Table()\n\n                table.name = this.driver.buildTableName(dbTable[\"TABLE_NAME\"])\n\n                // create columns from the loaded columns\n                table.columns = await Promise.all(\n                    dbColumns\n                        .filter(\n                            (dbColumn) =>\n                                dbColumn[\"TABLE_NAME\"] ===\n                                dbTable[\"TABLE_NAME\"],\n                        )\n                        .map(async (dbColumn) => {\n                            const columnUniqueIndices = dbIndices.filter(\n                                (dbIndex) => {\n                                    return (\n                                        dbIndex[\"TABLE_NAME\"] ===\n                                            dbTable[\"TABLE_NAME\"] &&\n                                        dbIndex[\"COLUMN_NAME\"] ===\n                                            dbColumn[\"COLUMN_NAME\"] &&\n                                        dbIndex[\"IS_UNIQUE\"] === true\n                                    )\n                                },\n                            )\n\n                            const tableMetadata =\n                                this.connection.entityMetadatas.find(\n                                    (metadata) =>\n                                        this.getTablePath(table) ===\n                                        this.getTablePath(metadata),\n                                )\n                            const hasIgnoredIndex =\n                                columnUniqueIndices.length > 0 &&\n                                tableMetadata &&\n                                tableMetadata.indices.some((index) => {\n                                    return columnUniqueIndices.some(\n                                        (uniqueIndex) => {\n                                            return (\n                                                index.name ===\n                                                    uniqueIndex[\"INDEX_NAME\"] &&\n                                                index.synchronize === false\n                                            )\n                                        },\n                                    )\n                                })\n\n                            const isConstraintComposite =\n                                columnUniqueIndices.every((uniqueIndex) => {\n                                    return dbIndices.some(\n                                        (dbIndex) =>\n                                            dbIndex[\"INDEX_NAME\"] ===\n                                                uniqueIndex[\"INDEX_NAME\"] &&\n                                            dbIndex[\"COLUMN_NAME\"] !==\n                                                dbColumn[\"COLUMN_NAME\"],\n                                    )\n                                })\n\n                            const tableColumn = new TableColumn()\n                            tableColumn.name = dbColumn[\"COLUMN_NAME\"]\n\n                            let fullType =\n                                dbColumn[\"SPANNER_TYPE\"].toLowerCase()\n                            if (fullType.indexOf(\"array\") !== -1) {\n                                tableColumn.isArray = true\n                                fullType = fullType.substring(\n                                    fullType.indexOf(\"<\") + 1,\n                                    fullType.indexOf(\">\"),\n                                )\n                            }\n\n                            if (fullType.indexOf(\"(\") !== -1) {\n                                tableColumn.type = fullType.substring(\n                                    0,\n                                    fullType.indexOf(\"(\"),\n                                )\n                            } else {\n                                tableColumn.type = fullType\n                            }\n\n                            if (\n                                this.driver.withLengthColumnTypes.indexOf(\n                                    tableColumn.type as ColumnType,\n                                ) !== -1\n                            ) {\n                                tableColumn.length = fullType.substring(\n                                    fullType.indexOf(\"(\") + 1,\n                                    fullType.indexOf(\")\"),\n                                )\n                            }\n\n                            if (dbColumn[\"IS_GENERATED\"] === \"ALWAYS\") {\n                                tableColumn.asExpression =\n                                    dbColumn[\"GENERATION_EXPRESSION\"]\n                                tableColumn.generatedType = \"STORED\"\n\n                                // We cannot relay on information_schema.columns.generation_expression, because it is formatted different.\n                                const asExpressionQuery =\n                                    this.selectTypeormMetadataSql({\n                                        table: dbTable[\"TABLE_NAME\"],\n                                        type: MetadataTableType.GENERATED_COLUMN,\n                                        name: tableColumn.name,\n                                    })\n\n                                const results = await this.query(\n                                    asExpressionQuery.query,\n                                    asExpressionQuery.parameters,\n                                )\n\n                                if (results[0] && results[0].value) {\n                                    tableColumn.asExpression = results[0].value\n                                } else {\n                                    tableColumn.asExpression = \"\"\n                                }\n                            }\n\n                            tableColumn.isUnique =\n                                columnUniqueIndices.length > 0 &&\n                                !hasIgnoredIndex &&\n                                !isConstraintComposite\n                            tableColumn.isNullable =\n                                dbColumn[\"IS_NULLABLE\"] === \"YES\"\n                            tableColumn.isPrimary = dbPrimaryKeys.some(\n                                (dbPrimaryKey) => {\n                                    return (\n                                        dbPrimaryKey[\"TABLE_NAME\"] ===\n                                            dbColumn[\"TABLE_NAME\"] &&\n                                        dbPrimaryKey[\"COLUMN_NAME\"] ===\n                                            dbColumn[\"COLUMN_NAME\"]\n                                    )\n                                },\n                            )\n\n                            return tableColumn\n                        }),\n                )\n\n                const tableForeignKeys = dbForeignKeys.filter(\n                    (dbForeignKey) => {\n                        return (\n                            dbForeignKey[\"TABLE_NAME\"] === dbTable[\"TABLE_NAME\"]\n                        )\n                    },\n                )\n\n                table.foreignKeys = OrmUtils.uniq(\n                    tableForeignKeys,\n                    (dbForeignKey) => dbForeignKey[\"CONSTRAINT_NAME\"],\n                ).map((dbForeignKey) => {\n                    const foreignKeys = tableForeignKeys.filter(\n                        (dbFk) =>\n                            dbFk[\"CONSTRAINT_NAME\"] ===\n                            dbForeignKey[\"CONSTRAINT_NAME\"],\n                    )\n                    return new TableForeignKey({\n                        name: dbForeignKey[\"CONSTRAINT_NAME\"],\n                        columnNames: OrmUtils.uniq(\n                            foreignKeys.map((dbFk) => dbFk[\"COLUMN_NAME\"]),\n                        ),\n                        referencedDatabase:\n                            dbForeignKey[\"REFERENCED_TABLE_SCHEMA\"],\n                        referencedTableName:\n                            dbForeignKey[\"REFERENCED_TABLE_NAME\"],\n                        referencedColumnNames: OrmUtils.uniq(\n                            foreignKeys.map(\n                                (dbFk) => dbFk[\"REFERENCED_COLUMN_NAME\"],\n                            ),\n                        ),\n                        onDelete: dbForeignKey[\"DELETE_RULE\"],\n                        onUpdate: dbForeignKey[\"UPDATE_RULE\"],\n                    })\n                })\n\n                const tableIndices = dbIndices.filter(\n                    (dbIndex) =>\n                        dbIndex[\"TABLE_NAME\"] === dbTable[\"TABLE_NAME\"],\n                )\n\n                table.indices = OrmUtils.uniq(\n                    tableIndices,\n                    (dbIndex) => dbIndex[\"INDEX_NAME\"],\n                ).map((constraint) => {\n                    const indices = tableIndices.filter((index) => {\n                        return index[\"INDEX_NAME\"] === constraint[\"INDEX_NAME\"]\n                    })\n\n                    return new TableIndex(<TableIndexOptions>{\n                        table: table,\n                        name: constraint[\"INDEX_NAME\"],\n                        columnNames: indices.map((i) => i[\"COLUMN_NAME\"]),\n                        isUnique: constraint[\"IS_UNIQUE\"],\n                        isNullFiltered: constraint[\"IS_NULL_FILTERED\"],\n                    })\n                })\n\n                const tableChecks = dbChecks.filter(\n                    (dbCheck) =>\n                        dbCheck[\"TABLE_NAME\"] === dbTable[\"TABLE_NAME\"],\n                )\n\n                table.checks = OrmUtils.uniq(\n                    tableChecks,\n                    (dbIndex) => dbIndex[\"CONSTRAINT_NAME\"],\n                ).map((constraint) => {\n                    const checks = tableChecks.filter(\n                        (dbC) =>\n                            dbC[\"CONSTRAINT_NAME\"] ===\n                            constraint[\"CONSTRAINT_NAME\"],\n                    )\n                    return new TableCheck({\n                        name: constraint[\"CONSTRAINT_NAME\"],\n                        columnNames: checks.map((c) => c[\"COLUMN_NAME\"]),\n                        expression: constraint[\"CHECK_CLAUSE\"],\n                    })\n                })\n\n                return table\n            }),\n        )\n    }\n\n    /**\n     * Builds create table sql.\n     */\n    protected createTableSql(table: Table, createForeignKeys?: boolean): Query {\n        const columnDefinitions = table.columns\n            .map((column) => this.buildCreateColumnSql(column))\n            .join(\", \")\n        let sql = `CREATE TABLE ${this.escapePath(table)} (${columnDefinitions}`\n\n        // we create unique indexes instead of unique constraints, because Spanner does not have unique constraints.\n        // if we mark column as Unique, it means that we create UNIQUE INDEX.\n        table.columns\n            .filter((column) => column.isUnique)\n            .forEach((column) => {\n                const isUniqueIndexExist = table.indices.some((index) => {\n                    return (\n                        index.columnNames.length === 1 &&\n                        !!index.isUnique &&\n                        index.columnNames.indexOf(column.name) !== -1\n                    )\n                })\n                const isUniqueConstraintExist = table.uniques.some((unique) => {\n                    return (\n                        unique.columnNames.length === 1 &&\n                        unique.columnNames.indexOf(column.name) !== -1\n                    )\n                })\n                if (!isUniqueIndexExist && !isUniqueConstraintExist)\n                    table.indices.push(\n                        new TableIndex({\n                            name: this.connection.namingStrategy.uniqueConstraintName(\n                                table,\n                                [column.name],\n                            ),\n                            columnNames: [column.name],\n                            isUnique: true,\n                        }),\n                    )\n            })\n\n        // as Spanner does not have unique constraints, we must create table indices from table uniques and mark them as unique.\n        if (table.uniques.length > 0) {\n            table.uniques.forEach((unique) => {\n                const uniqueExist = table.indices.some(\n                    (index) => index.name === unique.name,\n                )\n                if (!uniqueExist) {\n                    table.indices.push(\n                        new TableIndex({\n                            name: unique.name,\n                            columnNames: unique.columnNames,\n                            isUnique: true,\n                        }),\n                    )\n                }\n            })\n        }\n\n        if (table.checks.length > 0) {\n            const checksSql = table.checks\n                .map((check) => {\n                    const checkName = check.name\n                        ? check.name\n                        : this.connection.namingStrategy.checkConstraintName(\n                              table,\n                              check.expression!,\n                          )\n                    return `CONSTRAINT \\`${checkName}\\` CHECK (${check.expression})`\n                })\n                .join(\", \")\n\n            sql += `, ${checksSql}`\n        }\n\n        if (table.foreignKeys.length > 0 && createForeignKeys) {\n            const foreignKeysSql = table.foreignKeys\n                .map((fk) => {\n                    const columnNames = fk.columnNames\n                        .map((columnName) => `\\`${columnName}\\``)\n                        .join(\", \")\n                    if (!fk.name)\n                        fk.name = this.connection.namingStrategy.foreignKeyName(\n                            table,\n                            fk.columnNames,\n                            this.getTablePath(fk),\n                            fk.referencedColumnNames,\n                        )\n                    const referencedColumnNames = fk.referencedColumnNames\n                        .map((columnName) => `\\`${columnName}\\``)\n                        .join(\", \")\n\n                    return `CONSTRAINT \\`${\n                        fk.name\n                    }\\` FOREIGN KEY (${columnNames}) REFERENCES ${this.escapePath(\n                        this.getTablePath(fk),\n                    )} (${referencedColumnNames})`\n                })\n                .join(\", \")\n\n            sql += `, ${foreignKeysSql}`\n        }\n\n        sql += `)`\n\n        const primaryColumns = table.columns.filter(\n            (column) => column.isPrimary,\n        )\n        if (primaryColumns.length > 0) {\n            const columnNames = primaryColumns\n                .map((column) => this.driver.escape(column.name))\n                .join(\", \")\n            sql += ` PRIMARY KEY (${columnNames})`\n        }\n\n        return new Query(sql)\n    }\n\n    /**\n     * Builds drop table sql.\n     */\n    protected dropTableSql(tableOrPath: Table | string): Query {\n        return new Query(`DROP TABLE ${this.escapePath(tableOrPath)}`)\n    }\n\n    protected createViewSql(view: View): Query {\n        const materializedClause = view.materialized ? \"MATERIALIZED \" : \"\"\n        const viewName = this.escapePath(view)\n\n        const expression =\n            typeof view.expression === \"string\"\n                ? view.expression\n                : view.expression(this.connection).getQuery()\n        return new Query(\n            `CREATE ${materializedClause}VIEW ${viewName} SQL SECURITY INVOKER AS ${expression}`,\n        )\n    }\n\n    protected async insertViewDefinitionSql(view: View): Promise<Query> {\n        const { schema, tableName: name } = this.driver.parseTableName(view)\n\n        const type = view.materialized\n            ? MetadataTableType.MATERIALIZED_VIEW\n            : MetadataTableType.VIEW\n        const expression =\n            typeof view.expression === \"string\"\n                ? view.expression.trim()\n                : view.expression(this.connection).getQuery()\n        return this.insertTypeormMetadataSql({\n            type,\n            schema,\n            name,\n            value: expression,\n        })\n    }\n\n    /**\n     * Builds drop view sql.\n     */\n    protected dropViewSql(view: View): Query {\n        const materializedClause = view.materialized ? \"MATERIALIZED \" : \"\"\n        return new Query(\n            `DROP ${materializedClause}VIEW ${this.escapePath(view)}`,\n        )\n    }\n\n    /**\n     * Builds remove view sql.\n     */\n    protected async deleteViewDefinitionSql(view: View): Promise<Query> {\n        const { schema, tableName: name } = this.driver.parseTableName(view)\n\n        const type = view.materialized\n            ? MetadataTableType.MATERIALIZED_VIEW\n            : MetadataTableType.VIEW\n        return this.deleteTypeormMetadataSql({ type, schema, name })\n    }\n\n    /**\n     * Builds create index sql.\n     */\n    protected createIndexSql(table: Table, index: TableIndex): Query {\n        const columns = index.columnNames\n            .map((columnName) => this.driver.escape(columnName))\n            .join(\", \")\n        let indexType = \"\"\n        if (index.isUnique) indexType += \"UNIQUE \"\n        if (index.isNullFiltered) indexType += \"NULL_FILTERED \"\n\n        return new Query(\n            `CREATE ${indexType}INDEX \\`${index.name}\\` ON ${this.escapePath(\n                table,\n            )} (${columns})`,\n        )\n    }\n\n    /**\n     * Builds drop index sql.\n     */\n    protected dropIndexSql(\n        table: Table,\n        indexOrName: TableIndex | string,\n    ): Query {\n        const indexName =\n            indexOrName instanceof TableIndex ? indexOrName.name : indexOrName\n        return new Query(`DROP INDEX \\`${indexName}\\``)\n    }\n\n    /**\n     * Builds create check constraint sql.\n     */\n    protected createCheckConstraintSql(\n        table: Table,\n        checkConstraint: TableCheck,\n    ): Query {\n        return new Query(\n            `ALTER TABLE ${this.escapePath(table)} ADD CONSTRAINT \\`${\n                checkConstraint.name\n            }\\` CHECK (${checkConstraint.expression})`,\n        )\n    }\n\n    /**\n     * Builds drop check constraint sql.\n     */\n    protected dropCheckConstraintSql(\n        table: Table,\n        checkOrName: TableCheck | string,\n    ): Query {\n        const checkName =\n            checkOrName instanceof TableCheck ? checkOrName.name : checkOrName\n        return new Query(\n            `ALTER TABLE ${this.escapePath(\n                table,\n            )} DROP CONSTRAINT \\`${checkName}\\``,\n        )\n    }\n\n    /**\n     * Builds create foreign key sql.\n     */\n    protected createForeignKeySql(\n        table: Table,\n        foreignKey: TableForeignKey,\n    ): Query {\n        const columnNames = foreignKey.columnNames\n            .map((column) => this.driver.escape(column))\n            .join(\", \")\n        const referencedColumnNames = foreignKey.referencedColumnNames\n            .map((column) => this.driver.escape(column))\n            .join(\",\")\n        const sql =\n            `ALTER TABLE ${this.escapePath(table)} ADD CONSTRAINT \\`${\n                foreignKey.name\n            }\\` FOREIGN KEY (${columnNames}) ` +\n            `REFERENCES ${this.escapePath(\n                this.getTablePath(foreignKey),\n            )} (${referencedColumnNames})`\n\n        return new Query(sql)\n    }\n\n    /**\n     * Builds drop foreign key sql.\n     */\n    protected dropForeignKeySql(\n        table: Table,\n        foreignKeyOrName: TableForeignKey | string,\n    ): Query {\n        const foreignKeyName =\n            foreignKeyOrName instanceof TableForeignKey\n                ? foreignKeyOrName.name\n                : foreignKeyOrName\n        return new Query(\n            `ALTER TABLE ${this.escapePath(\n                table,\n            )} DROP CONSTRAINT \\`${foreignKeyName}\\``,\n        )\n    }\n\n    /**\n     * Escapes given table or view path.\n     */\n    protected escapePath(target: Table | View | string): string {\n        const { tableName } = this.driver.parseTableName(target)\n        return `\\`${tableName}\\``\n    }\n\n    /**\n     * Builds a part of query to create/change a column.\n     */\n    protected buildCreateColumnSql(column: TableColumn) {\n        let c = `${this.driver.escape(\n            column.name,\n        )} ${this.connection.driver.createFullType(column)}`\n\n        // Spanner supports only STORED generated column type\n        if (column.generatedType === \"STORED\" && column.asExpression) {\n            c += ` AS (${column.asExpression}) STORED`\n        } else {\n            if (!column.isNullable) c += \" NOT NULL\"\n        }\n\n        return c\n    }\n\n    /**\n     * Executes sql used special for schema build.\n     */\n    protected async executeQueries(\n        upQueries: Query | Query[],\n        downQueries: Query | Query[],\n    ): Promise<void> {\n        if (upQueries instanceof Query) upQueries = [upQueries]\n        if (downQueries instanceof Query) downQueries = [downQueries]\n\n        this.sqlInMemory.upQueries.push(...upQueries)\n        this.sqlInMemory.downQueries.push(...downQueries)\n\n        // if sql-in-memory mode is enabled then simply store sql in memory and return\n        if (this.sqlMemoryMode === true)\n            return Promise.resolve() as Promise<any>\n\n        for (const { query, parameters } of upQueries) {\n            if (this.isDMLQuery(query)) {\n                await this.query(query, parameters)\n            } else {\n                await this.updateDDL(query, parameters)\n            }\n        }\n    }\n\n    protected isDMLQuery(query: string): boolean {\n        return (\n            query.startsWith(\"INSERT\") ||\n            query.startsWith(\"UPDATE\") ||\n            query.startsWith(\"DELETE\")\n        )\n    }\n\n    /**\n     * Change table comment.\n     */\n    changeTableComment(\n        tableOrName: Table | string,\n        comment?: string,\n    ): Promise<void> {\n        throw new TypeORMError(\n            `spanner driver does not support change table comment.`,\n        )\n    }\n}\n"], "sourceRoot": "../.."}