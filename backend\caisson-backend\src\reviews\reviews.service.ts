import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Review, ReviewDocument } from '../entities/review.entity';
import { Product, ProductDocument } from '../entities/product.entity';
import { CreateReviewDto } from './dto/create-review.dto';
import { UpdateReviewDto } from './dto/update-review.dto';
import { ProductsService } from '../products/products.service';

@Injectable()
export class ReviewsService {
  constructor(
    @InjectModel(Review.name)
    private reviewModel: Model<ReviewDocument>,
    @InjectModel(Product.name)
    private productModel: Model<ProductDocument>,
    private productsService: ProductsService,
  ) {}

  async create(createReviewDto: CreateReviewDto): Promise<Review> {
    const { productId, ...reviewData } = createReviewDto;

    // Find product
    const product = await this.productModel.findById(productId).exec();

    if (!product) {
      throw new NotFoundException('Product not found');
    }

    // Create review
    const review = new this.reviewModel({
      ...reviewData,
      product: new Types.ObjectId(productId),
    });

    const savedReview = await review.save();

    // Update product rating
    await this.productsService.updateRating(productId);

    return savedReview;
  }

  async findAll(): Promise<Review[]> {
    return this.reviewModel.find()
      .populate('product')
      .sort({ createdAt: -1 })
      .exec();
  }

  async findByProduct(productId: string): Promise<Review[]> {
    const product = await this.productModel.findById(productId).exec();

    if (!product) {
      throw new NotFoundException('Product not found');
    }

    return this.reviewModel.find({
      product: new Types.ObjectId(productId)
    })
      .populate('product')
      .sort({ createdAt: -1 })
      .exec();
  }

  async findOne(id: string): Promise<Review> {
    const review = await this.reviewModel.findById(id)
      .populate('product')
      .exec();

    if (!review) {
      throw new NotFoundException('Review not found');
    }

    return review;
  }

  async update(id: string, updateReviewDto: UpdateReviewDto): Promise<Review> {
    const review = await this.reviewModel.findByIdAndUpdate(
      id,
      updateReviewDto,
      { new: true }
    ).populate('product').exec();

    if (!review) {
      throw new NotFoundException('Review not found');
    }

    // Update product rating if rating changed
    if (updateReviewDto.rating !== undefined) {
      await this.productsService.updateRating(review.product._id.toString());
    }

    return review;
  }

  async remove(id: string): Promise<void> {
    const review = await this.reviewModel.findById(id).exec();

    if (!review) {
      throw new NotFoundException('Review not found');
    }

    const productId = review.product.toString();

    await this.reviewModel.findByIdAndDelete(id).exec();

    // Update product rating after removal
    await this.productsService.updateRating(productId);
  }

  async getProductRatingStats(productId: string) {
    const product = await this.productModel.findById(productId).exec();

    if (!product) {
      throw new NotFoundException('Product not found');
    }

    // Use MongoDB aggregation to get rating statistics
    const stats = await this.reviewModel.aggregate([
      { $match: { product: new Types.ObjectId(productId) } },
      {
        $group: {
          _id: '$rating',
          count: { $sum: 1 }
        }
      },
      { $sort: { _id: 1 } }
    ]);

    const totalReviews = await this.reviewModel.countDocuments({
      product: new Types.ObjectId(productId)
    });

    const avgRatingResult = await this.reviewModel.aggregate([
      { $match: { product: new Types.ObjectId(productId) } },
      {
        $group: {
          _id: null,
          avgRating: { $avg: '$rating' }
        }
      }
    ]);

    return {
      totalReviews,
      averageRating: avgRatingResult.length > 0 ? avgRatingResult[0].avgRating : 0,
      ratingDistribution: stats.map(stat => ({
        rating: stat._id,
        count: stat.count,
      })),
    };
  }
}
