import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Review } from '../entities/review.entity';
import { Product } from '../entities/product.entity';
import { CreateReviewDto } from './dto/create-review.dto';
import { UpdateReviewDto } from './dto/update-review.dto';
import { ProductsService } from '../products/products.service';

@Injectable()
export class ReviewsService {
  constructor(
    @InjectRepository(Review)
    private reviewRepository: Repository<Review>,
    @InjectRepository(Product)
    private productRepository: Repository<Product>,
    private productsService: ProductsService,
  ) {}

  async create(createReviewDto: CreateReviewDto): Promise<Review> {
    const { productId, ...reviewData } = createReviewDto;

    // Find product
    const product = await this.productRepository.findOne({
      where: { id: productId },
    });

    if (!product) {
      throw new NotFoundException('Product not found');
    }

    // Create review
    const review = this.reviewRepository.create({
      ...reviewData,
      product,
    });

    const savedReview = await this.reviewRepository.save(review);

    // Update product rating
    await this.productsService.updateRating(productId);

    return savedReview;
  }

  async findAll(): Promise<Review[]> {
    return this.reviewRepository.find({
      relations: ['product'],
      order: { createdAt: 'DESC' },
    });
  }

  async findByProduct(productId: string): Promise<Review[]> {
    const product = await this.productRepository.findOne({
      where: { id: productId },
    });

    if (!product) {
      throw new NotFoundException('Product not found');
    }

    return this.reviewRepository.find({
      where: { product: { id: productId } },
      relations: ['product'],
      order: { createdAt: 'DESC' },
    });
  }

  async findOne(id: string): Promise<Review> {
    const review = await this.reviewRepository.findOne({
      where: { id },
      relations: ['product'],
    });

    if (!review) {
      throw new NotFoundException('Review not found');
    }

    return review;
  }

  async update(id: string, updateReviewDto: UpdateReviewDto): Promise<Review> {
    const review = await this.findOne(id);

    Object.assign(review, updateReviewDto);
    const updatedReview = await this.reviewRepository.save(review);

    // Update product rating if rating changed
    if (updateReviewDto.rating !== undefined) {
      await this.productsService.updateRating(review.product.id);
    }

    return updatedReview;
  }

  async remove(id: string): Promise<void> {
    const review = await this.findOne(id);
    const productId = review.product.id;

    await this.reviewRepository.remove(review);

    // Update product rating after removal
    await this.productsService.updateRating(productId);
  }

  async getProductRatingStats(productId: string) {
    const product = await this.productRepository.findOne({
      where: { id: productId },
    });

    if (!product) {
      throw new NotFoundException('Product not found');
    }

    const stats = await this.reviewRepository
      .createQueryBuilder('review')
      .select('review.rating', 'rating')
      .addSelect('COUNT(*)', 'count')
      .where('review.productId = :productId', { productId })
      .groupBy('review.rating')
      .orderBy('review.rating', 'ASC')
      .getRawMany();

    const totalReviews = await this.reviewRepository.count({
      where: { product: { id: productId } },
    });

    const avgRating = await this.reviewRepository
      .createQueryBuilder('review')
      .select('AVG(review.rating)', 'avg')
      .where('review.productId = :productId', { productId })
      .getRawOne();

    return {
      totalReviews,
      averageRating: parseFloat(avgRating.avg) || 0,
      ratingDistribution: stats.map(stat => ({
        rating: parseInt(stat.rating),
        count: parseInt(stat.count),
      })),
    };
  }
}
