// API service for communicating with the backend
const API_BASE_URL = 'http://localhost:3002';

// Helper function to get auth token
const getAuthToken = () => {
  if (typeof window !== 'undefined') {
    return localStorage.getItem('token');
  }
  return null;
};

// Helper function to create headers
const createHeaders = (includeAuth = false) => {
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
  };
  
  if (includeAuth) {
    const token = getAuthToken();
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }
  }
  
  return headers;
};

// Generic API request function
async function apiRequest<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> {
  const url = `${API_BASE_URL}${endpoint}`;
  
  const response = await fetch(url, {
    ...options,
    headers: {
      ...createHeaders(),
      ...options.headers,
    },
  });

  if (!response.ok) {
    throw new Error(`API request failed: ${response.statusText}`);
  }

  return response.json();
}

// Authenticated API request function
async function authenticatedApiRequest<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> {
  const url = `${API_BASE_URL}${endpoint}`;
  
  const response = await fetch(url, {
    ...options,
    headers: {
      ...createHeaders(true),
      ...options.headers,
    },
  });

  if (!response.ok) {
    throw new Error(`API request failed: ${response.statusText}`);
  }

  return response.json();
}

// Product API
export const productApi = {
  // Get all products
  getAll: () => apiRequest<any[]>('/products'),
  
  // Get product by ID
  getById: (id: string) => apiRequest<any>(`/products/${id}`),
  
  // Get product by slug
  getBySlug: (slug: string) => apiRequest<any>(`/products/slug/${slug}`),
  
  // Create product (authenticated)
  create: (data: any) => authenticatedApiRequest<any>('/products', {
    method: 'POST',
    body: JSON.stringify(data),
  }),
  
  // Update product (authenticated)
  update: (id: string, data: any) => authenticatedApiRequest<any>(`/products/${id}`, {
    method: 'PATCH',
    body: JSON.stringify(data),
  }),
  
  // Delete product (authenticated)
  delete: (id: string) => authenticatedApiRequest<void>(`/products/${id}`, {
    method: 'DELETE',
  }),
};

// Category API
export const categoryApi = {
  // Get all categories
  getAll: () => apiRequest<any[]>('/categories'),
  
  // Get category by ID
  getById: (id: string) => apiRequest<any>(`/categories/${id}`),
  
  // Get category by slug
  getBySlug: (slug: string) => apiRequest<any>(`/categories/slug/${slug}`),
  
  // Create category (authenticated)
  create: (data: any) => authenticatedApiRequest<any>('/categories', {
    method: 'POST',
    body: JSON.stringify(data),
  }),
  
  // Update category (authenticated)
  update: (id: string, data: any) => authenticatedApiRequest<any>(`/categories/${id}`, {
    method: 'PATCH',
    body: JSON.stringify(data),
  }),
  
  // Delete category (authenticated)
  delete: (id: string) => authenticatedApiRequest<void>(`/categories/${id}`, {
    method: 'DELETE',
  }),
};

// Tag API
export const tagApi = {
  // Get all tags
  getAll: () => apiRequest<any[]>('/tags'),
  
  // Get tag by ID
  getById: (id: string) => apiRequest<any>(`/tags/${id}`),
  
  // Create tag (authenticated)
  create: (data: any) => authenticatedApiRequest<any>('/tags', {
    method: 'POST',
    body: JSON.stringify(data),
  }),
  
  // Update tag (authenticated)
  update: (id: string, data: any) => authenticatedApiRequest<any>(`/tags/${id}`, {
    method: 'PATCH',
    body: JSON.stringify(data),
  }),
  
  // Delete tag (authenticated)
  delete: (id: string) => authenticatedApiRequest<void>(`/tags/${id}`, {
    method: 'DELETE',
  }),
};

// Auth API
export const authApi = {
  // Login
  login: (email: string, password: string) => apiRequest<{access_token: string, user: any}>('/auth/login', {
    method: 'POST',
    body: JSON.stringify({ email, password }),
  }),
  
  // Get profile (authenticated)
  getProfile: () => authenticatedApiRequest<any>('/auth/profile'),
  
  // Logout (authenticated)
  logout: () => authenticatedApiRequest<void>('/auth/logout', {
    method: 'POST',
  }),
};

// Upload API
export const uploadApi = {
  // Upload single file (authenticated)
  uploadSingle: (file: File, title?: string, description?: string) => {
    const formData = new FormData();
    formData.append('file', file);
    if (title) formData.append('title', title);
    if (description) formData.append('description', description);
    
    const token = getAuthToken();
    return fetch(`${API_BASE_URL}/upload/single`, {
      method: 'POST',
      headers: token ? { Authorization: `Bearer ${token}` } : {},
      body: formData,
    }).then(res => res.json());
  },
  
  // Upload multiple files (authenticated)
  uploadMultiple: (files: File[], title?: string, description?: string) => {
    const formData = new FormData();
    files.forEach(file => formData.append('files', file));
    if (title) formData.append('title', title);
    if (description) formData.append('description', description);
    
    const token = getAuthToken();
    return fetch(`${API_BASE_URL}/upload/multiple`, {
      method: 'POST',
      headers: token ? { Authorization: `Bearer ${token}` } : {},
      body: formData,
    }).then(res => res.json());
  },
  
  // Get all uploaded files
  getAll: () => apiRequest<any[]>('/upload'),
  
  // Get file by ID
  getById: (id: string) => apiRequest<any>(`/upload/${id}`),
  
  // Delete file (authenticated)
  delete: (id: string) => authenticatedApiRequest<void>(`/upload/${id}`, {
    method: 'DELETE',
  }),
};
