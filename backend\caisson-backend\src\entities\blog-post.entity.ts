import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToMany,
  JoinTable,
} from 'typeorm';
import { Tag } from './tag.entity';

export enum BlogCategory {
  ISOLATION = 'ISOLATION',
  PRODUCTS = 'PRODUCTS',
  INNOVATION = 'INNOVATION',
  ADVICE = 'ADVICE',
}

@Entity('blog_posts')
export class BlogPost {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  title: string;

  @Column({ unique: true })
  slug: string;

  @Column({ type: 'text' })
  excerpt: string;

  @Column({ type: 'longtext' })
  content: string;

  @Column({ nullable: true })
  image: string;

  @Column()
  author: string;

  @Column({
    type: 'enum',
    enum: BlogCategory,
  })
  category: BlogCategory;

  @ManyToMany(() => Tag, (tag) => tag.blogPosts)
  @JoinTable()
  tags: Tag[];

  @Column({ default: false })
  published: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
