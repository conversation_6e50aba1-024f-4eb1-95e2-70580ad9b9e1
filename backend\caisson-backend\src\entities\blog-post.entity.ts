import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export enum BlogCategory {
  ISOLATION = 'ISOLATION',
  PRODUCTS = 'PRODUCTS',
  INNOVATION = 'INNOVATION',
  ADVICE = 'ADVICE',
}

export type BlogPostDocument = BlogPost & Document;

@Schema({ timestamps: true })
export class BlogPost {
  @Prop({ required: true })
  title: string;

  @Prop({ required: true, unique: true })
  slug: string;

  @Prop({ required: true })
  excerpt: string;

  @Prop({ required: true })
  content: string;

  @Prop()
  image?: string;

  @Prop({ required: true })
  author: string;

  @Prop({
    type: String,
    enum: Object.values(BlogCategory),
    required: true
  })
  category: BlogCategory;

  @Prop({ type: [{ type: Types.ObjectId, ref: 'Tag' }], default: [] })
  tags: Types.ObjectId[];

  @Prop({ default: false })
  published: boolean;

  createdAt?: Date;
  updatedAt?: Date;
}

export const BlogPostSchema = SchemaFactory.createForClass(BlogPost);
