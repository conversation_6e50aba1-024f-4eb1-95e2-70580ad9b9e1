globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/_not-found/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./components/footer.tsx":{"*":{"id":"(ssr)/./components/footer.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/header.tsx":{"*":{"id":"(ssr)/./components/header.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/theme-provider.tsx":{"*":{"id":"(ssr)/./components/theme-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/ui/toaster.tsx":{"*":{"id":"(ssr)/./components/ui/toaster.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./lib/auth.tsx":{"*":{"id":"(ssr)/./lib/auth.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/page.tsx":{"*":{"id":"(ssr)/./app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/contact/page.tsx":{"*":{"id":"(ssr)/./app/contact/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/ui/tabs.tsx":{"*":{"id":"(ssr)/./components/ui/tabs.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/image-component.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/add-to-cart-button.tsx":{"*":{"id":"(ssr)/./components/add-to-cart-button.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/product-faq.tsx":{"*":{"id":"(ssr)/./components/product-faq.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/product-reviews.tsx":{"*":{"id":"(ssr)/./components/product-reviews.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/seo/structured-data.tsx":{"*":{"id":"(ssr)/./components/seo/structured-data.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/view-3d-button.tsx":{"*":{"id":"(ssr)/./components/view-3d-button.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/panier/page.tsx":{"*":{"id":"(ssr)/./app/panier/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/layout.tsx":{"*":{"id":"(ssr)/./app/admin/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/login/page.tsx":{"*":{"id":"(ssr)/./app/admin/login/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/produits/page.tsx":{"*":{"id":"(ssr)/./app/admin/produits/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/services/page.tsx":{"*":{"id":"(ssr)/./app/admin/services/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/temoignages/page.tsx":{"*":{"id":"(ssr)/./app/admin/temoignages/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/commandes/page.tsx":{"*":{"id":"(ssr)/./app/admin/commandes/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Desktop\\projects\\site caisson\\soci-t--caisson-tunisie\\app\\globals.css":{"id":"(app-pages-browser)/./app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\projects\\site caisson\\soci-t--caisson-tunisie\\components\\footer.tsx":{"id":"(app-pages-browser)/./components/footer.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\projects\\site caisson\\soci-t--caisson-tunisie\\components\\header.tsx":{"id":"(app-pages-browser)/./components/header.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\projects\\site caisson\\soci-t--caisson-tunisie\\components\\theme-provider.tsx":{"id":"(app-pages-browser)/./components/theme-provider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\projects\\site caisson\\soci-t--caisson-tunisie\\components\\ui\\toaster.tsx":{"id":"(app-pages-browser)/./components/ui/toaster.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\projects\\site caisson\\soci-t--caisson-tunisie\\lib\\auth.tsx":{"id":"(app-pages-browser)/./lib/auth.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\projects\\site caisson\\soci-t--caisson-tunisie\\node_modules\\next\\font\\google\\target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\projects\\site caisson\\soci-t--caisson-tunisie\\app\\page.tsx":{"id":"(app-pages-browser)/./app/page.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\projects\\site caisson\\soci-t--caisson-tunisie\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\projects\\site caisson\\soci-t--caisson-tunisie\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\projects\\site caisson\\soci-t--caisson-tunisie\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\projects\\site caisson\\soci-t--caisson-tunisie\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\projects\\site caisson\\soci-t--caisson-tunisie\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\projects\\site caisson\\soci-t--caisson-tunisie\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\projects\\site caisson\\soci-t--caisson-tunisie\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\projects\\site caisson\\soci-t--caisson-tunisie\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\projects\\site caisson\\soci-t--caisson-tunisie\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\projects\\site caisson\\soci-t--caisson-tunisie\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\projects\\site caisson\\soci-t--caisson-tunisie\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\projects\\site caisson\\soci-t--caisson-tunisie\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\projects\\site caisson\\soci-t--caisson-tunisie\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\projects\\site caisson\\soci-t--caisson-tunisie\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\projects\\site caisson\\soci-t--caisson-tunisie\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\projects\\site caisson\\soci-t--caisson-tunisie\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\projects\\site caisson\\soci-t--caisson-tunisie\\app\\contact\\page.tsx":{"id":"(app-pages-browser)/./app/contact/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\projects\\site caisson\\soci-t--caisson-tunisie\\components\\ui\\tabs.tsx":{"id":"(app-pages-browser)/./components/ui/tabs.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\projects\\site caisson\\soci-t--caisson-tunisie\\node_modules\\next\\dist\\client\\app-dir\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\projects\\site caisson\\soci-t--caisson-tunisie\\node_modules\\next\\dist\\esm\\client\\app-dir\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\projects\\site caisson\\soci-t--caisson-tunisie\\node_modules\\next\\dist\\client\\image-component.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\projects\\site caisson\\soci-t--caisson-tunisie\\node_modules\\next\\dist\\esm\\client\\image-component.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\projects\\site caisson\\soci-t--caisson-tunisie\\components\\add-to-cart-button.tsx":{"id":"(app-pages-browser)/./components/add-to-cart-button.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\projects\\site caisson\\soci-t--caisson-tunisie\\components\\product-faq.tsx":{"id":"(app-pages-browser)/./components/product-faq.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\projects\\site caisson\\soci-t--caisson-tunisie\\components\\product-reviews.tsx":{"id":"(app-pages-browser)/./components/product-reviews.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\projects\\site caisson\\soci-t--caisson-tunisie\\components\\seo\\structured-data.tsx":{"id":"(app-pages-browser)/./components/seo/structured-data.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\projects\\site caisson\\soci-t--caisson-tunisie\\components\\view-3d-button.tsx":{"id":"(app-pages-browser)/./components/view-3d-button.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\projects\\site caisson\\soci-t--caisson-tunisie\\app\\panier\\page.tsx":{"id":"(app-pages-browser)/./app/panier/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\projects\\site caisson\\soci-t--caisson-tunisie\\app\\admin\\layout.tsx":{"id":"(app-pages-browser)/./app/admin/layout.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\projects\\site caisson\\soci-t--caisson-tunisie\\app\\admin\\login\\page.tsx":{"id":"(app-pages-browser)/./app/admin/login/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\projects\\site caisson\\soci-t--caisson-tunisie\\app\\admin\\produits\\page.tsx":{"id":"(app-pages-browser)/./app/admin/produits/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\projects\\site caisson\\soci-t--caisson-tunisie\\app\\admin\\services\\page.tsx":{"id":"(app-pages-browser)/./app/admin/services/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\projects\\site caisson\\soci-t--caisson-tunisie\\app\\admin\\temoignages\\page.tsx":{"id":"(app-pages-browser)/./app/admin/temoignages/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\projects\\site caisson\\soci-t--caisson-tunisie\\app\\admin\\commandes\\page.tsx":{"id":"(app-pages-browser)/./app/admin/commandes/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Desktop\\projects\\site caisson\\soci-t--caisson-tunisie\\":[],"C:\\Users\\<USER>\\Desktop\\projects\\site caisson\\soci-t--caisson-tunisie\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"C:\\Users\\<USER>\\Desktop\\projects\\site caisson\\soci-t--caisson-tunisie\\app\\page":[],"C:\\Users\\<USER>\\Desktop\\projects\\site caisson\\soci-t--caisson-tunisie\\app\\_not-found\\page":[]},"rscModuleMapping":{"(app-pages-browser)/./app/globals.css":{"*":{"id":"(rsc)/./app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/footer.tsx":{"*":{"id":"(rsc)/./components/footer.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/header.tsx":{"*":{"id":"(rsc)/./components/header.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/theme-provider.tsx":{"*":{"id":"(rsc)/./components/theme-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/ui/toaster.tsx":{"*":{"id":"(rsc)/./components/ui/toaster.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./lib/auth.tsx":{"*":{"id":"(rsc)/./lib/auth.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/page.tsx":{"*":{"id":"(rsc)/./app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/contact/page.tsx":{"*":{"id":"(rsc)/./app/contact/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/ui/tabs.tsx":{"*":{"id":"(rsc)/./components/ui/tabs.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/image-component.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/add-to-cart-button.tsx":{"*":{"id":"(rsc)/./components/add-to-cart-button.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/product-faq.tsx":{"*":{"id":"(rsc)/./components/product-faq.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/product-reviews.tsx":{"*":{"id":"(rsc)/./components/product-reviews.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/seo/structured-data.tsx":{"*":{"id":"(rsc)/./components/seo/structured-data.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/view-3d-button.tsx":{"*":{"id":"(rsc)/./components/view-3d-button.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/panier/page.tsx":{"*":{"id":"(rsc)/./app/panier/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/layout.tsx":{"*":{"id":"(rsc)/./app/admin/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/login/page.tsx":{"*":{"id":"(rsc)/./app/admin/login/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/produits/page.tsx":{"*":{"id":"(rsc)/./app/admin/produits/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/services/page.tsx":{"*":{"id":"(rsc)/./app/admin/services/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/temoignages/page.tsx":{"*":{"id":"(rsc)/./app/admin/temoignages/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/commandes/page.tsx":{"*":{"id":"(rsc)/./app/admin/commandes/page.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}